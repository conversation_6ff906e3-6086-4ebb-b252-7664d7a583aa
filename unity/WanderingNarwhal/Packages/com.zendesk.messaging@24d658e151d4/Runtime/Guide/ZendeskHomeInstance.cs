using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using Zendesk.MessagingCore.Guide.Application.DTOs;
using Zendesk.MessagingCore.Guide.Core.Controller.Interfaces;
using Zendesk.MessagingCore.Logging;
using Zendesk.Runtime.UI.Manager;
using Logger = Zendesk.MessagingCore.Logging.Logger;

namespace Zendesk.Runtime.Guide
{
    internal class ZendeskHomeInstance : HomeInstance
    {
        private readonly IZendeskUiManager _zendeskUiManager;
        private readonly string _locale;
        private bool _initializationInProgress;
        private readonly Logger _logger = LoggerManager.GetLogger<ZendeskHomeInstance>();

        internal ZendeskHomeInstance(
            IZendeskUiManager zendeskUiManager,
            IGuideController guideController,
            string locale)
        {
            _zendeskUiManager = zendeskUiManager;
            _locale = locale;
            GuideController = guideController;
        }

        public override async Task ShowHomeAsync(Transform parentTransform = null)
        {
            if (_initializationInProgress)
                return;

            _initializationInProgress = true;
            try
            {
                await Show(parentTransform);
            }
            catch (MissingReferenceException)
            {
                _logger.LogWarning("Home Prefab Destroyed");
            }
            catch (Exception e)
            {
                _logger.LogException(e);
            }
            finally
            {
                _initializationInProgress = false;
            }
        }

        private async Task Show(Transform parentTransform = null)
        {
            LocaleInfo localeInfo = await GuideController.GetSupportedLocaleAsync(_locale);
            List<CategoryDto> articles = await GuideController.GetArticles(localeInfo);

            if (IsArticlesEmpty(articles))
            {
                await _zendeskUiManager.DisplayMessaging(parentTransform);
            }
            else
            {
                await _zendeskUiManager.DisplayHome(parentTransform);
            }
        }

        private static bool IsArticlesEmpty(List<CategoryDto> articles) => articles == null || articles.Count <= 0;
    }
}