using System.IO;
using System.Text;

namespace Zendesk.Runtime.Utils.FileUtils
{
    // Implementation for writing to a file thread-safely.
    internal class FileWriter : IFileWriter
    {
        private readonly IPath _path;
        private readonly IDirectory _directory;
        private readonly object _lock = new object();
        private string _filePath;

        internal FileWriter() : this(new PathWrapper(), new DirectoryWrapper())
        {
        }

        internal FileWriter(IPath path, IDirectory directory)
        {
            _path = path;
            _directory = directory;
        }

        public void Initialize(string filePath)
        {
            var directory = _path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !_directory.Exists(directory))
            {
                _directory.CreateDirectory(directory);
            }

            _filePath = filePath;
        }

        public void WriteLine(string message)
        {
            lock (_lock)
            {
                using (var fileStream = new FileStream(_filePath, FileMode.OpenOrCreate, FileAccess.Write, FileShare.Read))
                {
                    // Move to end for appending
                    fileStream.Seek(0, SeekOrigin.End);
                    
                    using (var streamWriter = new StreamWriter(fileStream, Encoding.UTF8))
                    {
                        streamWriter.AutoFlush = true;
                        streamWriter.WriteLine(message);
                    }
                }
            }
        }
    }
}
    