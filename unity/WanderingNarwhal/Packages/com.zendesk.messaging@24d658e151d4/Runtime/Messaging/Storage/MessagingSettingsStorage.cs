using System;
using Zendesk.Runtime.Models;
using Zendesk.Runtime.Storage;

namespace Zendesk.Runtime.Messaging.Storage
{
    public interface IMessagingSettingsStorage
    {
        bool IsCacheValid { get; }
        void SaveSettings(MessagingSettings settings);
        MessagingSettings GetSettings();
        void Clear();
    }

    internal class MessagingSettingsStorage : IMessagingSettingsStorage
    {
        private const string _NameSpace = "ZendeskMessagingSettings";
        private const string _SettingsKey = "ZENDESK_MESSAGING_SETTINGS";
        private const int _ValidCacheTimeInSeconds = 3600; // in seconds
        private readonly IStorage _storage;
        private long _timeWhenCached;
        public bool IsCacheValid => DateTime.UtcNow.GetUnixTimestamp() <= _timeWhenCached + _ValidCacheTimeInSeconds;

        public MessagingSettingsStorage()
        {
            _storage = StorageFactory.Create(_NameSpace, StorageType.Complex);
        }

        public void SaveSettings(MessagingSettings settings)
        {
            _timeWhenCached = DateTime.UtcNow.GetUnixTimestamp();
            _storage.Set(_SettingsKey, settings);
        }

        public MessagingSettings GetSettings()
        {
            if (IsCacheValid)
                return _storage.TryGet<MessagingSettings>(_SettingsKey, out var cachedSettings) ? cachedSettings : null;
            return null;
        }

        public void Clear()
        {
            _storage.Clear();
        }
    }
}