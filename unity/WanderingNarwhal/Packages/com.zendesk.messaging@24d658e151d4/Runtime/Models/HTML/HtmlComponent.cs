using System;
using System.Collections.Generic;
using System.Linq;

namespace Zendesk.Runtime.Models.HTML
{
    public class HtmlComponent : IEquatable<HtmlComponent>
    {
        public HtmlComponent()
        {
            Tags = new List<string>();
            Children = new List<HtmlComponent>();
        }
        public HtmlComponent(HtmlComponentType type, string value)
        {
            Type = type;
            Value = value;
            Children = new List<HtmlComponent>();
            Tags = new List<string>();
        }
        
        public HtmlComponent(HtmlComponentType type, string value, List<string> tags)
        {
            Type = type;
            Value = value;
            Tags = tags;
            Children = new List<HtmlComponent>();
        }
        public HtmlComponentType Type { get; set; }
        public string Value { get; set; }
        public List<string> Tags { get; set; }
        public List<HtmlComponent> Children { get; set; }

        public bool Equals(HtmlComponent other)
        {
            return 
                other != null && 
                other.Type.Equals(Type) && 
                other.Value == Value && 
                other.Children.SequenceEqual(Children) && 
                other.Tags.SequenceEqual(Tags);
        }
    }
}