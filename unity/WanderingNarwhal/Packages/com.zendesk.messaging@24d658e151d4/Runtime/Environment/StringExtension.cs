using System;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using Zendesk.Runtime.Utils;

namespace Zendesk.Runtime.Environment
{
    public static class StringExtension
    {
        // Obfuscate string which contains json ClientId value.
        public static string ObfuscateClientId(this string text)
        {
            return Regex.Replace(text, "\"clientId\":\"([A-Za-z0-9\\-]+)\"", "\"clientId\":\"**********\"");
        }

        // Remove all but numbers and dots. Used to generate a string compatible with REST headers
        public static string RemoveAllExceptNumbersAndDots(this string text)
        {
            return Regex.Replace(text, "[^0-9.]", "");
        }

        /// <summary>
        /// Validates if the string is a valid email
        /// </summary>
        /// <param name="email">email to be checked</param>
        /// <returns>if the email is valid</returns>
        public static bool IsValidEmail(this string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                // Normalize the domain
                email = Regex.Replace(email, @"(@)(.+)$", DomainMapper,
                    RegexOptions.None, TimeSpan.FromMilliseconds(200));

                // Examines the domain part of the email and normalizes it.
                string DomainMapper(Match match)
                {
                    // Use IdnMapping class to convert Unicode domain names.
                    var idn = new IdnMapping();

                    // Pull out and process domain name (throws ArgumentException on invalid)
                    string domainName = idn.GetAscii(match.Groups[2].Value);

                    return match.Groups[1].Value + domainName;
                }
            }
            catch
            {
                return false;
            }

            try
            {
                return Regex.IsMatch(email,
                    @"^[^@\s]+@[^@\s]+\.[^@\s]+$",
                    RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Verify if a given URL represents an article and get its correspondent article id
        /// </summary>
        /// <param name="url">Input url</param>
        /// <param name="articleId">Out parameter to return a valid article's id. Null if the input is not a valid article url.</param>
        /// <returns>
        /// True if the URL is valid and represents an article.
        /// False for a non-article or invalid article URL.
        /// </returns>
        public static bool IsArticleUrl(this string url, out long? articleId)
        {
            articleId = null;
            
            if (!SecureUrlOperations.TryGetSanitisedUrl(url, out var sanitisedUrl))
                return false;

            try
            {
                var articleUri = new Uri(sanitisedUrl);
                articleId = TryGetFromLastSegment(articleUri) ?? TryGetFromParsingUrl(articleUri);
                return articleId != null;
            }
            catch
            {
                return false;
            }
        }

        private static long? TryGetFromParsingUrl(Uri articleUrl)
        {
            try
            {
                for (var i = 0; i < articleUrl.Segments.Length; i++)
                {
                    if (!articleUrl.Segments[i].ToLower().Equals("articles/")) continue;

                    var articleId = long.Parse(articleUrl.Segments[i + 1].Replace("/", ""));
                    return articleId;
                }
            }
            catch
            {
                return null;
            }

            return null;
        }

        private static long? TryGetFromLastSegment(Uri articleUrl)
        {
            try
            {
                var uriLastSegment = articleUrl.Segments.Last();
                var splitLastSegment = uriLastSegment.Split('-');
                var articleIdString = splitLastSegment.First();
                var articleId = long.Parse(articleIdString);
                return articleId;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Get the Host from URL
        /// </summary>
        /// <param name="url">URL</param>
        /// <returns>Null if invalid or https url</returns>
        public static string GetHostUrl(this string url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return null;

            try
            {
                var uri = new Uri(url);
                return $"https://{uri.Host}/";
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Get file name from media url
        /// </summary>
        /// <param name="mediaUrl">URL</param>
        /// <returns>If valid, returns the media file name. If not, returns an empty string.</returns>
        internal static string GetMediaFileName(this string mediaUrl)
        {
            if (string.IsNullOrEmpty(mediaUrl))
            {
                return string.Empty;
            }

            string fileNameFallback = Path.GetFileName(mediaUrl);

            if (string.IsNullOrEmpty(fileNameFallback))
            {
                return string.Empty;
            }

            if (!Uri.IsWellFormedUriString(mediaUrl, UriKind.Absolute))
            {
                return fileNameFallback;
            }

            // Handle files / images from agent workspace
            string query = new Uri(mediaUrl).Query;
            if (string.IsNullOrEmpty(query))
            {
                return fileNameFallback;
            }

            Match match = Regex.Match(query, "(?:\\?name=)(?<fileName>([^&\\s]*))");
            return match.Success ? match.Groups["fileName"].Value : fileNameFallback;
        }

        /// <summary>
        /// Get file type from mime type
        /// </summary>
        /// <param name="mediaType">Mime Type</param>
        /// <returns>File Type</returns>
        internal static string GetMediaType(string mediaType)
        {
            if (string.IsNullOrEmpty(mediaType))
            {
                return string.Empty;
            }

            int startIndex = mediaType.IndexOf('/') + 1;
            // No '/' character in string
            if (startIndex == 0)
            {
                return string.Empty;
            }

            // Remove the "image/" from "image/png" to just get the extension
            return mediaType.Substring(startIndex);
        }
    }
}