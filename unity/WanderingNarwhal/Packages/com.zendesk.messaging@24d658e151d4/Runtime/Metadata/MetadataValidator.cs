using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Zendesk.MessagingCore.Logging;
using Zendesk.MessagingCore.RestClient.Client;
using Zendesk.MessagingCore.RestClient.DTO.NativeMessaging.Metadata;

namespace Zendesk.Runtime.Metadata
{
    internal interface IMetadataValidator
    {
        Task<(bool validationResult, List<string> validationErrors)> ValidateFields(Dictionary<string, object> fields);
        bool ValidateTags(List<string> tags, out List<string> errors);
    }

    internal class MetadataValidator : IMetadataValidator
    {
        private readonly IMetadataApi _metadataApi;

        private List<ConversationFieldDto> _availableFields = new List<ConversationFieldDto>();
        private readonly Logger _logger = LoggerManager.GetLogger<MetadataValidator>();

        public MetadataValidator(string baseUrl) : this(new MetadataApi(baseUrl))
        {
        }

        public MetadataValidator(IMetadataApi metadataApi)
        {
            _metadataApi = metadataApi;
        }

        public async Task<(bool validationResult, List<string> validationErrors)> ValidateFields(Dictionary<string, object> fields)
        {
            await PopulateConversationFields();

            if (_availableFields?.Count == 0)
                return (true, null); // If no validation rules are available then we just continue without validating

            var validationErrors = new List<string>();
            foreach (var fieldKvp in fields)
            {
                if (!ValidateField(fieldKvp.Key, fieldKvp.Value, out var error))
                    validationErrors.Add(error);
            }

            return validationErrors.Any() ? (false, validationErrors) : (true, null);
        }

        public bool ValidateTags(List<string> tags, out List<string> errors)
        {
            errors = new List<string>();

            if (tags == null || tags.Count == 0)
            {
                errors.Add("List of tags is null or empty");
                return false;
            }

            if (tags.Count > 20)
            {
                errors.Add("The maximum amounts of tags the you can set in 20.");
                return false;
            }

            for (var i = 0; i < tags.Count; i++)
            {
                if (string.IsNullOrWhiteSpace(tags[i]))
                {
                    errors.Add($"Tag at index: {i} is null or empty.");
                    continue;
                }

                if (tags[i].Length > 255)
                {
                    errors.Add($"Tag at index: {i} is too long (max 254 characters allowed).");
                    continue;
                }

                if (new Regex("[$`´{}()<>&?+,;@!*#$='\" ]").Match(tags[i]).Success)
                {
                    errors.Add($"Tag at index: {i} contains an invalid character.");
                    continue;
                }
            }

            return !errors.Any();
        }

        private async Task PopulateConversationFields()
        {
            if (_availableFields?.Count == 0)
            {
                var result = await _metadataApi.GetConversationFields();
                if (result.Success)
                    _availableFields = result.Data;
                else
                    _logger.LogError("Unable to fetch conversation field rules. Validation of conversation fields will be skipped");
            }
        }

        private bool ValidateField(string fieldId, object fieldValue, out string error)
        {
            error = null;
            var conversationField = GetConversationField(fieldId);

            if (conversationField == null || conversationField.Type == TicketFieldType.NotHandled)
            {
                error =
                    $"Conversation Field: {fieldId} can't be validated. Please ensure that the provided ID is correct and the field is customer editable.";
                return false;
            }

            if (!ValidateValue(fieldValue, conversationField, out var valueError))
            {
                error = $"The value provided for the Conversation Field: {fieldId} is not correct. {valueError}";
                return false;
            }

            return true;
        }

        private ConversationFieldDto GetConversationField(string fieldId)
        {
            foreach (ConversationFieldDto conversationField in _availableFields)
            {
                if (conversationField.Id == fieldId)
                {
                    return conversationField;
                }
            }

            return null;
        }

        private bool ValidateValue(object fieldValue, ConversationFieldDto fieldRules, out string error)
        {
            error = null;

            try
            {
                switch (fieldRules.Type)
                {
                    case TicketFieldType.Checkbox:
                        if (fieldValue is bool || IsAcceptedCheckboxValue(fieldValue))
                            return true;

                        error = "Expected value of type bool for a field of type Checkbox.";
                        break;
                    case TicketFieldType.Regex:
                        if (fieldValue is string regexValue && IsMatchingRegex(regexValue, fieldRules.RegexpForValidation))
                            return true;

                        error = "Expected value did not pass the regular expression validation for a field of type Regex.";
                        break;
                    case TicketFieldType.Text:
                        if (fieldValue is string textFieldString && !string.IsNullOrWhiteSpace(textFieldString))
                            return true;

                        error = "Expected value of type string for a field of type Text.";
                        break;
                    case TicketFieldType.DropDown:
                        if (IsDropDownValueValid(fieldValue, fieldRules.Options))
                            return true;

                        error = "Expected value of type string for a field of type DropDown.";
                        break;
                    case TicketFieldType.MultiSelect:
                        if (IsMultiSelectValueValid(fieldValue, fieldRules.Options))
                            return true;

                        error = "Expected value of type string (with comma separated values) for a field of type MultiSelect.";
                        break;
                    case TicketFieldType.Date:
                        if (fieldValue is string dateValue && IsMatchingRegex(dateValue, fieldRules.RegexpForValidation))
                            return true;

                        error = "Expected value did not pass the regular expression validation for a field of type Date.";
                        break;
                    case TicketFieldType.MultiLine:
                        if (fieldValue is string multiLineString && !string.IsNullOrWhiteSpace(multiLineString))
                            return true;

                        error = "Expected value of type string for a field of type MultiLine.";
                        break;
                    case TicketFieldType.Number:
                        if (IsMatchingRegex($"{fieldValue}", fieldRules.RegexpForValidation))
                            return true;

                        error = "Expected value did not pass the regular expression validation for a field of type Number.";
                        break;
                    case TicketFieldType.Decimal:
                        if (IsMatchingRegex($"{fieldValue}", fieldRules.RegexpForValidation))
                            return true;

                        error = "Expected value did not pass the regular expression validation for a field of type Decimal.";
                        break;
                    case TicketFieldType.NotHandled:
                        error = "Unknown or unsupported field type, unable to validate.";
                        break;
                    default:
                        error = "Unexpected conversation field type encountered. Unable to validate the field value.";
                        break;
                }
            }
            catch (Exception)
            {
                error = "Unexpected error occurred during validation of the provided value.";
            }

            return false;
        }

        private static bool IsMultiSelectValueValid(object fieldValue, List<string> availableOptions)
        {
            if (fieldValue is string multiSelectString && !string.IsNullOrWhiteSpace(multiSelectString))
            {
                if (availableOptions == null)
                    return true; // Skipping further validation if there are no options provided
                var submittedOptions = multiSelectString.Split(',').ToArray();
                foreach (var option in submittedOptions)
                {
                    if (!availableOptions.Contains(option.Trim()))
                        return false;
                }

                return true;
            }

            return false;
        }

        private static bool IsAcceptedCheckboxValue(object fieldValue)
        {
            var acceptedValues = new[] {"0", "1", "true", "false"};
            return fieldValue is string stringValue && acceptedValues.Contains(stringValue.ToLowerInvariant());
        }

        private static bool IsDropDownValueValid(object fieldValue, List<string> availableOptions)
        {
            if (fieldValue is string dropDownValue)
            {
                if (availableOptions == null)
                    return true; // Skipping validation if there are no options provided

                var optionsSet = new HashSet<string>(availableOptions, StringComparer.InvariantCulture);
                return optionsSet.Contains(dropDownValue);
            }

            return false;
        }

        private static bool IsMatchingRegex(string fieldValue, string regexPattern)
        {
            if (string.IsNullOrWhiteSpace(regexPattern))
                return true; // If the rules are missing, we're omitting the validation 

            return new Regex(regexPattern).Match(fieldValue).Success;
        }
    }
}