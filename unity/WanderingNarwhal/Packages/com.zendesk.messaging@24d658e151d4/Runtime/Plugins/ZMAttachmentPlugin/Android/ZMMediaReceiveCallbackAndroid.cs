/*
MIT License

Copyright (c) 2017 <PERSON><PERSON><PERSON><PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
 */

#if UNITY_EDITOR || UNITY_ANDROID
using UnityEngine;

namespace Zendesk.Runtime.Plugins.ZMAttachmentPlugin
{
    public class ZMMediaReceiveCallbackAndroid : AndroidJavaProxy
    {
        private readonly ZMAttachmentPicker.MediaPickCallback _mediaPickCallback;

        private readonly ZMCallbackHelper _callbackHelper;

        public ZMMediaReceiveCallbackAndroid(ZMAttachmentPicker.MediaPickCallback mediaPickCallback) : base(
            "com.zendesk.unity.ZMMediaReceiver")
        {
            _mediaPickCallback = mediaPickCallback;
            _callbackHelper = new GameObject("ZMCallbackHelper").AddComponent<ZMCallbackHelper>();
        }

        public void OnMediaReceived(string path)
        {
            _callbackHelper.CallOnMainThread(() => MediaReceiveCallback(path));
        }

        private void MediaReceiveCallback(string path)
        {
            if (string.IsNullOrEmpty(path))
                path = null;

            try
            {
                _mediaPickCallback?.Invoke(path);
            }
            finally
            {
                Object.Destroy(_callbackHelper.gameObject);
            }
        }
    }
}
#endif