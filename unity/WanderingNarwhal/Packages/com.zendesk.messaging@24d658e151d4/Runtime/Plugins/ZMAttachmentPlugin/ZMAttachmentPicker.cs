/*
MIT License

Copyright (c) 2017 <PERSON><PERSON><PERSON><PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
 */

using System;
using System.IO;
using UnityEngine;
using Object = UnityEngine.Object;
#if UNITY_IOS || UNITY_ANDROID
using Zendesk.Runtime.Plugins.ZMAttachmentPlugin;
#endif

namespace Zendesk.Runtime.Plugins.ZMAttachmentPlugin
{
    public static class ZMAttachmentPicker
    {
        public struct ImageProperties
        {
            public readonly int width;
            public readonly int height;
            public readonly string mimeType;
            public readonly ImageOrientation orientation;

            public ImageProperties(int width, int height, string mimeType, ImageOrientation orientation)
            {
                this.width = width;
                this.height = height;
                this.mimeType = mimeType;
                this.orientation = orientation;
            }
        }

        [Flags]
        public enum MediaType
        {
            Image = 1
        };

        // EXIF orientation: http://sylvana.net/jpegcrop/exif_orientation.html (indices are reordered)
        public enum ImageOrientation
        {
            Unknown = -1,
            Normal = 0,
            Rotate90 = 1,
            Rotate180 = 2,
            Rotate270 = 3,
            FlipHorizontal = 4,
            Transpose = 5,
            FlipVertical = 6,
            Transverse = 7
        };

        public delegate void MediaPickCallback(string path);

        #region Platform Specific Elements

        #if !UNITY_EDITOR && UNITY_ANDROID
	private static AndroidJavaClass m_ajc = null;
	private static AndroidJavaClass AJC
	{
		get
		{
			if( m_ajc == null )
				m_ajc = new AndroidJavaClass( "com.zendesk.unity.ZMAttachmentPicker" );

			return m_ajc;
		}
	}

	private static AndroidJavaObject m_context = null;
	private static AndroidJavaObject Context
	{
		get
		{
			if( m_context == null )
			{
				using( AndroidJavaObject unityClass = new AndroidJavaClass( "com.unity3d.player.UnityPlayer" ) )
				{
					m_context = unityClass.GetStatic<AndroidJavaObject>( "currentActivity" );
				}
			}

			return m_context;
		}
	}
    #elif !UNITY_EDITOR && UNITY_IOS
	[System.Runtime.InteropServices.DllImport( "__Internal" )]
	private static extern void _ZMAttachmentPicker_OpenSettings();

	[System.Runtime.InteropServices.DllImport( "__Internal" )]
	private static extern void _ZMAttachmentPicker_PickMedia( string mediaSavePath);

	[System.Runtime.InteropServices.DllImport( "__Internal" )]
	private static extern string _ZMAttachmentPicker_GetImageProperties( string path );

	[System.Runtime.InteropServices.DllImport( "__Internal" )]
	private static extern string _ZMAttachmentPicker_LoadImageAtPath( string path, string temporaryFilePath, int maxSize );
    #endif

    #if !UNITY_EDITOR && ( UNITY_ANDROID || UNITY_IOS )
	private static string m_temporaryImagePath = null;
	private static string TemporaryImagePath
	{
		get
		{
			if( m_temporaryImagePath == null )
			{
				m_temporaryImagePath = Path.Combine( Application.temporaryCachePath, "tmpImg" );
				Directory.CreateDirectory( Application.temporaryCachePath );
			}

			return m_temporaryImagePath;
		}
	}

	private static string m_selectedMediaPath = null;
	private static string SelectedMediaPath
	{
		get
		{
			if( m_selectedMediaPath == null )
			{
				m_selectedMediaPath = Path.Combine( Application.temporaryCachePath, "pickedMedia" );
				Directory.CreateDirectory( Application.temporaryCachePath );
			}
			return m_selectedMediaPath;
		}
	}
        #endif

        #endregion

        #region Load Functions

        /// <summary>
        ///
        /// </summary>
        /// <param name="callback"></param>
        /// <param name="title"></param>
        /// <param name="mime"></param>
        public static void GetImageFromGallery(MediaPickCallback callback, string title = "", string mime = "image/*")
        {
            GetMediaFromGallery(callback, MediaType.Image, mime, title);
        }

        public static bool IsMediaPickerBusy()
        {
            #if !UNITY_EDITOR && UNITY_IOS
		return ZMMediaReceiveCallbackiOS.IsBusy;
            #else
            return false;
            #endif
        }

        #endregion

        #region Internal Functions

        private static string GetTemporarySavePath(string filename)
        {
            var saveDir = Path.Combine(Application.persistentDataPath, "ZendeskAttachments");
            Directory.CreateDirectory(saveDir);

            #if !UNITY_EDITOR && UNITY_IOS
		// Ensure a unique temporary filename on iOS:
		// iOS internally copies images/videos to Photos directory of the system,
		// but the process is async. The redundant file is deleted by objective-c code
		// automatically after the media is saved but while it is being saved, the file
		// should NOT be overwritten. Therefore, always ensure a unique filename on iOS
		string path = Path.Combine( saveDir, filename );
		if( File.Exists( path ) )
		{
			int fileIndex = 0;
			string filenameWithoutExtension = Path.GetFileNameWithoutExtension( filename );
			string extension = Path.GetExtension( filename );

			do
			{
				path = Path.Combine( saveDir, string.Concat( filenameWithoutExtension, ++fileIndex, extension ) );
			} while( File.Exists( path ) );
		}

		return path;
            #else
            return Path.Combine(saveDir, filename);
            #endif
        }

        private static void GetMediaFromGallery(MediaPickCallback mediaPickCallback, MediaType mediaType, string mime,
            string title)
        {
            if (IsMediaPickerBusy()) return;

            #if UNITY_EDITOR
            var editorFilters = new System.Collections.Generic.List<string>(4)
            {
                "Image files",
                "png,jpg,jpeg,heic,bmp",
                "All files",
                "*"
            };

            var pickedFile =
                UnityEditor.EditorUtility.OpenFilePanelWithFilters("Select file", "", editorFilters.ToArray());

            mediaPickCallback?.Invoke(pickedFile != "" ? pickedFile : null);

            #elif UNITY_ANDROID && !UNITY_EDITOR
			AJC.CallStatic( "PickMedia", Context, new ZMMediaReceiveCallbackAndroid(mediaPickCallback), SelectedMediaPath, mime, title );
            #elif UNITY_IOS && !UNITY_EDITOR
			ZMMediaReceiveCallbackiOS.Initialize(mediaPickCallback);
			_ZMAttachmentPicker_PickMedia( SelectedMediaPath);
            #else
			mediaPickCallback?.Invoke(null);
            #endif

            return;
        }

        private static byte[] GetTextureBytes(Texture2D texture, bool isJpeg)
        {
            try
            {
                return isJpeg ? texture.EncodeToJPG(100) : texture.EncodeToPNG();
            }
            catch (UnityException)
            {
                return GetTextureBytesFromCopy(texture, isJpeg);
            }
            catch (ArgumentException)
            {
                return GetTextureBytesFromCopy(texture, isJpeg);
            }

#pragma warning disable 0162
            return null;
#pragma warning restore 0162
        }

        private static byte[] GetTextureBytesFromCopy(Texture2D texture, bool isJpeg)
        {
            Texture2D sourceTexReadable = null;
            RenderTexture rt = RenderTexture.GetTemporary(texture.width, texture.height);
            RenderTexture activeRT = RenderTexture.active;

            try
            {
                Graphics.Blit(texture, rt);
                RenderTexture.active = rt;

                sourceTexReadable = new Texture2D(texture.width, texture.height,
                    isJpeg ? TextureFormat.RGB24 : TextureFormat.RGBA32, false);
                sourceTexReadable.ReadPixels(new Rect(0, 0, texture.width, texture.height), 0, 0, false);
                sourceTexReadable.Apply(false, false);
            }
            catch (Exception e)
            {
                Debug.LogException(e);

                Object.DestroyImmediate(sourceTexReadable);
                return null;
            }
            finally
            {
                RenderTexture.active = activeRT;
                RenderTexture.ReleaseTemporary(rt);
            }

            try
            {
                return isJpeg ? sourceTexReadable.EncodeToJPG(100) : sourceTexReadable.EncodeToPNG();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                return null;
            }
            finally
            {
                Object.DestroyImmediate(sourceTexReadable);
            }
        }

        #endregion

        #region Utility Functions
        public static Texture2D LoadImageAtPath(string imagePath, int maxSize = 0, bool markTextureNonReadable = true,
            bool generateMipmaps = true, bool linearColorSpace = false)
        {
            if (string.IsNullOrEmpty(imagePath))
                throw new ArgumentException("Parameter 'imagePath' is null or empty!");

            if (!File.Exists(imagePath))
                throw new FileNotFoundException("File not found at " + imagePath);

            if (maxSize == 0)
                maxSize = SystemInfo.maxTextureSize;

            #if !UNITY_EDITOR && UNITY_ANDROID
		string loadPath = AJC.CallStatic<string>( "LoadImageAtPath", Context, imagePath, TemporaryImagePath, maxSize );
            #elif !UNITY_EDITOR && UNITY_IOS
		string loadPath = _ZMAttachmentPicker_LoadImageAtPath( imagePath, TemporaryImagePath, maxSize );
            #else
            var loadPath = imagePath;
            #endif

            var extension = Path.GetExtension(imagePath).ToLowerInvariant();
            var format =
                (extension == ".jpg" || extension == ".jpeg") ? TextureFormat.RGB24 : TextureFormat.RGBA32;

            var result = new Texture2D(2, 2, format, generateMipmaps, linearColorSpace);

            try
            {
                if (!result.LoadImage(File.ReadAllBytes(loadPath), markTextureNonReadable))
                {
                    Object.DestroyImmediate(result);
                    return null;
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                Object.DestroyImmediate(result);
                return null;
            }
            finally
            {
                if (loadPath != imagePath)
                {
                    try
                    {
                        File.Delete(loadPath);
                    }
                    catch
                    {
                        // ignored
                    }
                }
            }

            return result;
        }

        public static ImageProperties? GetImageProperties(string imagePath)
        {
            if (!File.Exists(imagePath))
                throw new FileNotFoundException("File not found at " + imagePath);

            #if !UNITY_EDITOR && UNITY_ANDROID
		string value = AJC.CallStatic<string>( "GetImageProperties", Context, imagePath );
            #elif !UNITY_EDITOR && UNITY_IOS
		string value = _ZMAttachmentPicker_GetImageProperties( imagePath );
            #else
            string value = null;
            #endif

            int width = 0, height = 0;
            string mimeType = null;
            var orientation = ImageOrientation.Unknown;
            if (string.IsNullOrEmpty(value)) return new ImageProperties(width, height, mimeType, orientation);

            var properties = value.Split('>');
            if (properties == null || properties.Length < 4)
                return new ImageProperties(width, height, mimeType, orientation);
            if (!int.TryParse(properties[0].Trim(), out width))
                width = 0;
            if (!int.TryParse(properties[1].Trim(), out height))
                height = 0;

            mimeType = properties[2].Trim();
            if (mimeType.Length == 0)
            {
                var extension = Path.GetExtension(imagePath).ToLowerInvariant();
                switch (extension)
                {
                    case ".png":
                        mimeType = "image/png";
                        break;
                    case ".jpg":
                    case ".jpeg":
                        mimeType = "image/jpeg";
                        break;
                    case ".gif":
                        mimeType = "image/gif";
                        break;
                    case ".bmp":
                        mimeType = "image/bmp";
                        break;
                    default:
                        mimeType = null;
                        break;
                }
            }

            if (int.TryParse(properties[3].Trim(), out var orientationInt))
                orientation = (ImageOrientation)orientationInt;

            return new ImageProperties(width, height, mimeType, orientation);
        }

        #endregion
    }
}