using System.Threading.Tasks;
using UnityEngine;

namespace Zendesk.Runtime.UI.Manager
{
    internal interface IZendeskUiManager
    {
        /// <summary>
        /// Displays the messaging UI.
        /// </summary>
        Task DisplayMessaging(Transform parentTransform = null);
        
        /// <summary>
        /// Displays the Home UI.
        /// </summary>
        Task DisplayHome(Transform parentTransform = null);
        
        /// <summary>
        /// Close screen.
        /// </summary>
        void CloseActiveScreen();

        /// <summary>
        /// Set active on screen.
        /// </summary>
        void SetActiveScreen(bool enable);
    }
}