using System;
using Zendesk.MessagingCore.Guide.Core.Controller.Interfaces;
using Zendesk.Runtime.Localisation;
using Zendesk.Runtime.UI.Guide.Core.MVVM.Interfaces;
using Zendesk.Runtime.UI.Guide.Core.Navigation.Enums;
using Zendesk.Runtime.UI.Guide.Core.Navigation.Interfaces;
using Zendesk.Runtime.UI.Guide.Screens.ArticleScreen.ViewModels;
using Zendesk.Runtime.UI.Guide.Screens.CategoryScreen.ViewModels;
using Zendesk.Runtime.UI.Guide.Screens.HelpCenterScreen.ViewModels;
using Zendesk.Runtime.UI.Guide.Screens.HomeScreen.ViewModels;
using Zendesk.Runtime.UI.Guide.Screens.MessagingScreen.ViewModels;
using Zendesk.Runtime.UI.Guide.Services.Interfaces;
using Zendesk.Runtime.UI.Manager;

namespace Zendesk.Runtime.UI.Guide.Core
{
    internal class ViewModelFactory : IViewModelFactory
    {
        private readonly Lazy<INavigationService> _navigationService;
        private readonly IGuideController _guideController;
        private readonly ITranslationService _translationService;
        private readonly IMessagingService _messagingService;
        private readonly ILocalisationService _localisationService;
        private readonly IZendeskUiManager _uiManager;

        public ViewModelFactory(
            Lazy<INavigationService> navigationService,
            IGuideController guideController,
            ITranslationService translationService,
            IMessagingService messagingService,
            ILocalisationService localisationService,
            IZendeskUiManager uiManager)
        {

            _navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));
            _guideController = guideController ?? throw new ArgumentNullException(nameof(guideController));
            _translationService = translationService ?? throw new ArgumentNullException(nameof(translationService));
            _messagingService = messagingService ?? throw new ArgumentNullException(nameof(messagingService));
            _localisationService = localisationService ?? throw new ArgumentNullException(nameof(localisationService));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
        }

        public IViewModel Create(GuideScreen screen)
        {
            return screen switch
            {
                GuideScreen.Category => new CategoryScreenViewModel(_navigationService.Value, _guideController, _translationService, _localisationService),
                GuideScreen.HelpCenter => new HelpCenterViewModel(_navigationService.Value, _guideController, _translationService, _localisationService),
                GuideScreen.Home => new HomeScreenViewModel(_navigationService.Value, _guideController, _messagingService, _translationService, _localisationService),
                GuideScreen.Article => new ArticleScreenViewModel(_navigationService.Value),
                GuideScreen.Messaging => new MessagingScreenViewModel(_navigationService.Value, _messagingService, _uiManager),
                _ => throw new ArgumentException($"Unknown screen: {screen}")
            };
        }
    }
}