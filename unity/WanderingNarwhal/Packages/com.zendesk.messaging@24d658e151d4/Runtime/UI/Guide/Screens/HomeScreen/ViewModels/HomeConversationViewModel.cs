using Zendesk.Runtime.Models;
using Zendesk.Runtime.Translations;
using Zendesk.Runtime.UI.Guide.Core.MVVM.Base;
using Zendesk.Runtime.UI.Guide.Core.Navigation.Interfaces;
using Zendesk.Runtime.UI.Guide.Services.Interfaces;

namespace Zendesk.Runtime.UI.Guide.Screens.HomeScreen.ViewModels
{
    /// <summary>
    /// ViewModel for the conversation section of the home screen.
    /// </summary>
    internal class HomeConversationViewModel : ViewModelBase
    {
        private readonly ITranslationService _translationService;

        public string ConversationTitle => Translate(GuideTranslationKeys.GuideUnityZmuConversationTitle);

        public bool HasConversation { get; }
        public HomeConversationPreviewViewModel ConversationPreview { get; }
        public HomeStartConversationViewModel StartConversation { get; }

        public HomeConversationViewModel(
            INavigationService navigationService,
            ITranslationService translationService,
            Message message,
            HomeConversationPreviewViewModel preview = null,
            HomeStartConversationViewModel start = null)
            : base(navigationService)
        {
            _translationService = translationService;
            HasConversation = message != null;
            ConversationPreview = preview;
            StartConversation = start;
        }

        private string Translate(string key) => _translationService.GetString(key);

        public static HomeConversationViewModel MapFrom(INavigationService navigationService,
            ITranslationService translationService,
            IMessagingService messagingService,
            Message message)
        {
            if (message == null)
            {
                var startConversation = new HomeStartConversationViewModel(navigationService, translationService);
                return new HomeConversationViewModel(navigationService, translationService, null, null,
                    startConversation);
            }

            var preview = HomeConversationPreviewViewModel.MapFrom(
                navigationService,
                translationService,
                message,
                messagingService.GetMessagingSettings()
            );
            return new HomeConversationViewModel(navigationService, translationService, message, preview);
        }
    }
}