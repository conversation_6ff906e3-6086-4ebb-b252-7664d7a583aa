using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zendesk.MessagingCore.Logging;
using Logger = Zendesk.MessagingCore.Logging.Logger;

namespace Zendesk.Runtime.UI.ZMPopUpList
{
    public class ZMPopUpListScreen : ZMListScreen
    {
        public override event Action<ZMOptionData> ItemSelected;
        public override event Action CancelButtonClicked;
        public override event Action ItemDeselected;

        [SerializeField] internal TMP_Text _itemText;
        [SerializeField] internal Toggle _itemToggle;
        [SerializeField] internal Button _cancelButton;
        [SerializeField] internal TMP_Text _headerTitle;

        internal readonly List<ZMPopUpListItem> _popUpListItems = new List<ZMPopUpListItem>();
        internal List<ZMOptionData> _options;
        internal int _selectedIndex = _Deselected;
        private const int _Deselected = -999;
        private readonly Color _selectedItemColor = new Color32(237, 248, 244, 255);

        private ZMPopUpListItem _itemTemplate;
        private readonly Logger _logger = LoggerManager.GetLogger<ZMPopUpListScreen>();


        public override bool ValidateListScreen()
        {
            var rectTransform = gameObject.transform;

            if (!_itemToggle || _itemToggle.transform == rectTransform)
            {
                _logger.LogError(
                    "The PopUpList screen is not valid. The screen must have a child GameObject with a Toggle component serving as the item.");
                return false;
            }

            if (!(_itemToggle.transform.parent is RectTransform))
            {
                _logger.LogError(
                    "The PopUpList screen is not valid. The child GameObject with a Toggle component (the item) must have a RectTransform on its parent.");
                return false;
            }

            if (_itemText != null && !_itemText.transform.IsChildOf(_itemToggle.transform))
            {
                _logger.LogError(
                    "The PopUpList screen is not valid. The Item Text must be on the item GameObject or children of it.");
                return false;
            }

            if (!_cancelButton || _cancelButton.transform == rectTransform)
            {
                _logger.LogError(
                    "The PopUpList screen is not valid. The screen must have a child gameObject serving as the cancel button.");
                return false;
            }

            if (!_headerTitle)
            {
                _logger.LogError(
                    "The PopUpList screen is not valid. The screen must have a child TextMeshPro Text component serving as the header for the list screen.");
                return false;
            }

            return true;
        }

        public override void ConfigureListCanvas(Transform buttonTransform, out Canvas listCanvas)
        {
            var listScreenGo = gameObject;
            listCanvas = listScreenGo.GetOrAddComponent<Canvas>();
            listCanvas.overrideSorting = true;
            listCanvas.sortingOrder = 30000;

            var parentCanvas = buttonTransform.FindFirstCanvas();

            // If we have a parent canvas, apply the same raycasters as the parent for consistency.
            if (parentCanvas)
            {
                listScreenGo.CopyRaycasters(parentCanvas);
            }
            else
            {
                listScreenGo.GetOrAddComponent<GraphicRaycaster>();
            }

            listScreenGo.GetOrAddComponent<CanvasGroup>();
        }

        public override void CreatePopUpListItems(List<ZMOptionData> options, int selectedIndex)
        {
            _popUpListItems.Clear();
            _options = options;
            _selectedIndex = selectedIndex;
            _itemTemplate = _itemToggle.GetComponent<ZMPopUpListItem>();

            _itemTemplate.gameObject.SetActive(true);
            AddPopUpListItems(options, selectedIndex);
            // Make item template inactive to remove it from the list
            _itemTemplate.gameObject.SetActive(false);
        }

        public override void SetUpCancelButton()
        {
            _cancelButton.onClick.AddListener(() =>
            {
                _logger.Log("Cancel clicked");
                CancelButtonClicked?.Invoke();
            });
        }

        public override void SetHeaderText(string listTitleText)
        {
            _headerTitle.text = listTitleText ?? "";
        }

        private void AddPopUpListItems(IEnumerable<IOptionData> options, int selectedIndex)
        {
            ZMPopUpListItem previous = null;

            foreach (var (option, index) in options.WithIndex())
            {
                var item = CreateItem(option, _itemTemplate);
                if (item == null)
                    continue;

                item.Index = index;

                item.Toggle.isOn = selectedIndex == index;

                // Select current option
                if (item.Toggle.isOn)
                {
                    ConfigureSelectedItem(item);
                }

                // Subscribe to item events
                item.ItemClicked += HandleItemClicked;

                // Automatically set up explicit navigation
                SetUpNavigation(previous, item);

                previous = item;

                _popUpListItems.Add(item);
            }
        }

        /// <summary>
        /// Apply "selected item" changes, e.g. background color
        /// </summary>
        /// <param name="item"> item to apply the changes to </param>
        private void ConfigureSelectedItem(ZMPopUpListItem item)
        {
            //Toggles the checkbox icon
            item.Toggle.Select();
            // Changes background color
            item.BackgroundImage.color = _selectedItemColor;
        }

        /// <summary>
        /// Create a new PopUpList item
        /// </summary>
        /// <param name="data"> option data </param>
        /// <param name="itemTemplate"> item template to use as the foundation </param>
        /// <returns> the newly created PopUpList item </returns>
        private ZMPopUpListItem CreateItem(IOptionData data, ZMPopUpListItem itemTemplate)
        {
            // Add a new item to the PopUpList screen.
            var item = Instantiate(itemTemplate, itemTemplate.RectTransform.parent, false);
            var itemGO = item.gameObject;
            itemGO.SetActive(true);
            itemGO.name = $"PopUpListItem: {data.Text}";

            // Turning toggle off by default
            if (item.Toggle != null)
            {
                item.Toggle.isOn = false;
            }

            // Set the item's data
            if (item.Text)
                item.Text.text = data.Text;

            return item;
        }

        /// <summary>
        /// Handle click on the list item
        /// </summary>
        /// <param name="itemIndex">index of the selected item </param>
        private void HandleItemClicked(int itemIndex)
        {
            // Was the item already selected?
            if (_selectedIndex == itemIndex)
            {
                
                _logger.Log("PopUpListScreen: Handle Item Deselected");
                _selectedIndex = _Deselected;
                ItemDeselected?.Invoke();
                return;
            }

            _logger.Log("PopUpListScreen: Handle Item Clicked");
            _selectedIndex = itemIndex;
            ItemSelected?.Invoke(_options[itemIndex]);
        }

        private static void SetUpNavigation(ZMPopUpListItem previousItem, ZMPopUpListItem currentItem)
        {
            // If it's the first element, nothing to do here
            if (previousItem == null) return;

            var prevNav = previousItem.Toggle.navigation;
            var toggleNav = currentItem.Toggle.navigation;
            prevNav.mode = Navigation.Mode.Explicit;
            toggleNav.mode = Navigation.Mode.Explicit;

            prevNav.selectOnDown = currentItem.Toggle;
            prevNav.selectOnRight = currentItem.Toggle;
            toggleNav.selectOnLeft = previousItem.Toggle;
            toggleNav.selectOnUp = previousItem.Toggle;

            previousItem.Toggle.navigation = prevNav;
            currentItem.Toggle.navigation = toggleNav;
        }
    }
}