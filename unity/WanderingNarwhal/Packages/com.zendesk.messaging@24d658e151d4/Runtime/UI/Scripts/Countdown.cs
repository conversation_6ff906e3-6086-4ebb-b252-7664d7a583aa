using System;
using Zendesk.Runtime.Messaging;

namespace Zendesk.Runtime.UI.Scripts
{
    public readonly struct Countdown
    {
        private readonly long _startTime;
        private readonly uint _duration;
        
        public bool HasExpired => DateTime.UtcNow.GetUnixTimestamp() - _startTime >= _duration;
    
        public Countdown(uint duration) : this(duration, DateTime.UtcNow.GetUnixTimestamp())
        {
        }

        internal Countdown(uint duration, long startTime)
        {
            _duration = duration;
            _startTime = startTime;
        }
    }
}