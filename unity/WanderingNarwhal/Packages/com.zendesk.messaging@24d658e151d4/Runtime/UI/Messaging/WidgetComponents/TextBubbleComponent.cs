using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zendesk.Runtime.UI.Messaging.WidgetComponents;
using Zendesk.Runtime.UI.Utilities;

namespace Zendesk.Runtime.UI.Messaging
{
    public class TextBubbleComponent : MonoBehaviour
    {
        [SerializeField] private TMP_Text _textComponent;
        [SerializeField] private RectTransform _textRectTransform;
        [SerializeField] private Image _bubbleImage;
        [SerializeField] private RectTransform _bubbleTransform;
        [SerializeField] private LayoutGroup _bubbleLayoutGroup;

        [SerializeField] private AiDisclaimerComponent _aiDisclaimerComponent;

        private float _defaultMessageBubbleWidth;
        private float _defaultTextWidth;
        private float _messageBubbleWidth;
        private string _defaultTextString;

        public float Width => _messageBubbleWidth > 0f
            ? _messageBubbleWidth
            : _defaultMessageBubbleWidth;

        public bool IsValid =>
            _textComponent != null &&
            _textRectTransform != null &&
            _bubbleImage != null &&
            _bubbleTransform != null &&
            _bubbleLayoutGroup != null &&
            _aiDisclaimerComponent != null &&
            _aiDisclaimerComponent.IsValid;

        public void SetText(string text, bool isAiGenerated = false)
        {
            _textComponent.text = text;

            if (isAiGenerated)
            {
                _aiDisclaimerComponent.Show();
                SetAiMessageBubbleWidth();
            }
            else
                SetMessageBubbleWidth();
        }

        public void ClearText()
        {
            _textComponent.text = string.Empty;
            SetMessageBubbleWidth();
            _aiDisclaimerComponent.Hide();
        }

        public void SetButtonLinkMessageBubbleText(string text, bool isAiGenerated = false)
        {
            _textComponent.text = text;
            if (!isAiGenerated) 
                return;
            
            //Set separator min width to fit inside of the message bubble
            _aiDisclaimerComponent.SetMinWidth(_bubbleTransform.sizeDelta.x - _bubbleLayoutGroup.padding.horizontal);
            _aiDisclaimerComponent.Show();
        }

        public void ClearButtonLinkMessageBubbleText()
        {
            _textComponent.text = string.Empty;
            _aiDisclaimerComponent.Hide();
        }

        public void SetTextColor(Color color)
        {
            _textComponent.color = color;
        }

        public void SetBubbleColor(Color color)
        {
            _bubbleImage.color = color;
        }

        private void Awake()
        {
            _defaultMessageBubbleWidth = _bubbleTransform.rect.width;
            _defaultTextWidth = _defaultMessageBubbleWidth - _bubbleLayoutGroup.padding.horizontal;
        }

        private void SetMessageBubbleWidth()
        {
            Vector2 bubbleSize = CalculateTextBubbleSize();
            SetTextBubbleContainerSize(bubbleSize);
        }

        private void SetAiMessageBubbleWidth()
        {
            //Calculate the size of the text bubble for both the text message and the disclaimer message
            Vector2 originalBubbleSize = CalculateTextBubbleSize();
            Vector2 aiDisclaimerBubbleSize = _aiDisclaimerComponent.CalculateAiDisclaimerBubbleSize(_defaultTextWidth);

            //Verify which of the previously calculated bubbles is the largest
            Vector2 largestSize = ComponentUtilities.GetLargestBubbleSize(aiDisclaimerBubbleSize, originalBubbleSize);

            //Set the bubble size to fit the largest text component
            SetTextBubbleContainerSize(largestSize);

            _aiDisclaimerComponent.SetSeparatorSize(largestSize);
        }

        #region Bubble size calculations

        private void SetTextBubbleContainerSize(Vector2 largestSize)
        {
            // Ceil() guards against floating point truncation potentially making the width too small for the text.
            var containerSize = new Vector2(
                Mathf.Ceil(largestSize.x + _bubbleLayoutGroup.padding.horizontal),
                Mathf.Ceil(largestSize.y + _bubbleLayoutGroup.padding.vertical));

            _messageBubbleWidth = containerSize.x;
            _bubbleTransform.sizeDelta = containerSize;
        }

        private Vector2 CalculateTextBubbleSize()
        {
            //get text message size
            ComponentUtilities.ResizeRectTransformToText(
                _textRectTransform, _textComponent,
                _textComponent.text, _defaultTextWidth,
                out Vector2 originalBubbleSize);

            return originalBubbleSize;
        }

        #endregion
    }
}