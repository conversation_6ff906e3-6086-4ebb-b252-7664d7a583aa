using System.Threading.Tasks;
using Zendesk.MessagingCore.Guide.Application.DTOs;
using Zendesk.MessagingCore.Guide.Application.Services.Interfaces;
using Zendesk.MessagingCore.Guide.Domain.Repositories;

namespace Zendesk.MessagingCore.Guide.Application.Services
{
    internal class PostStatsService : IPostStatsService
    {
        private readonly IPostStatsRepository _postStatsRepository;

        /// <summary>
        /// Default constructor
        /// </summary>
        public PostStatsService(IPostStatsRepository postStatsRepository)
        {
            _postStatsRepository = postStatsRepository;
        }

        /// <summary>
        /// Asynchronously posts the view statistic for a given article to the server.
        /// </summary>
        /// <param name="requestDto">The request data transfer object containing the request parameters.</param>
        /// <param name="headers">The headers to include in the request.</param>
        /// <returns>A task containing the result of the request in the form of a boolean.</returns>
        public async Task<bool> PostStatsAsync(PostStatsRequestDto requestDto, Headers headers)
        {
            var response = await _postStatsRepository.PostStatsAsync(
                requestDto.Locale,
                requestDto.ArticleId.ToString(),
                requestDto.Origin,
                headers.ToHeadersDictionary()
            );

            return response;
        }
    }
}