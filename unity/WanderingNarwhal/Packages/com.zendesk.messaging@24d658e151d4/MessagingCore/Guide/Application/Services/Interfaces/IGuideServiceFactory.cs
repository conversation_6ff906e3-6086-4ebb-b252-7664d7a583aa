namespace Zendesk.MessagingCore.Guide.Application.Services.Interfaces
{
    /// <summary>
    /// Represents a factory for creating instances of Guide Service.
    /// </summary>
    internal interface IGuideServiceFactory
    {
        /// <summary>
        /// Creates an instance of IGuideService.
        /// </summary>
        IGuideService Create(
            IArticlesService articlesService,
            IArticleService articleService,
            IPostStatsService postStatsService,
            ILocalesService localesService
        );
    }
}