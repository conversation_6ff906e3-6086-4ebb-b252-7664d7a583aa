using Newtonsoft.Json;

namespace Zendesk.MessagingCore.Messaging.DTO
{
    public class SourceDto
    {
        [JsonProperty("type")] public readonly string Type;
        [JsonProperty("id")] public readonly string Id;
        [JsonProperty("sessionId")] public readonly string SessionId;

        public SourceDto() {}
        
        public SourceDto(
            string type = null,
            string id = null,
            string sessionId = null)
        {
            Type = type;
            Id = id;
            SessionId = sessionId;
        }
    }
}