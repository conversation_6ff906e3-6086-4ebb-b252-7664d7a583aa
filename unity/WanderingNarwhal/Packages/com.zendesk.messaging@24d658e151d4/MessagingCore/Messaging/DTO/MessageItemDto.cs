using System.Collections.Generic;
using Newtonsoft.Json;

namespace Zendesk.MessagingCore.Messaging.DTO
{
    [JsonObject(MemberSerialization.OptIn)]
    public class MessageItemDto
    {
        [JsonProperty("_id")] public readonly string Id;
        [JsonProperty("title")] public readonly string Title;
        [JsonProperty("actions")] private readonly MessageActionDto[] _actions;
        [JsonProperty("description")] public readonly string Description;
        [JsonProperty("mediaUrl")] public readonly string MediaUrl;
        [JsonProperty("mediaType")] public readonly string MediaType;

        public IReadOnlyList<MessageActionDto> Actions => _actions;

        public MessageItemDto(
            string id = null,
            string title = null,
            MessageActionDto[] actions = null,
            string description = null,
            string mediaUrl = null,
            string mediaType = null)
        {
            Id = id;
            Title = title;
            _actions = actions;
            Description = description;
            MediaUrl = mediaUrl;
            MediaType = mediaType;
        }
    }
    
}