using System;
using System.Threading.Tasks;
using Zendesk.MessagingCore.FayeClient.DTO;
using Zendesk.MessagingCore.Models;
using Zendesk.MessagingCore.RestClient.Result;
using Zendesk.MessagingCore.SocketClient;

namespace Zendesk.MessagingCore.FayeClient.Client
{
    /// <summary>
    /// Interface for Faye Client
    /// </summary>
    internal interface IZendeskFayeClient
    {
        #region Methods

        /// <summary>
        /// Sends a handshake message from this client to the Faye Server
        /// </summary>
        /// <returns>Result</returns>
        Task<Result> Handshake();

        /// <summary>
        /// Sends a connect message from this client to the Faye Server
        /// </summary>
        /// <param name="clientId">Client id returned from handshake</param>
        /// <returns>Result</returns>
        Task<Result> Connect(string clientId);

        /// <summary>
        /// Sends a subscribe message from this client to the Faye Server
        /// </summary>
        /// <param name="clientId">Client id returned from handshake</param>
        /// <param name="appId">App id</param>
        /// <param name="userId">User id</param>
        /// <param name="authentication">User authentication</param>
        /// <returns>Result</returns>
        Task<Result> Subscribe(string clientId, string appId, string userId, Authentication authentication);

        /// <summary>
        /// Sends a disconnect message from this client to the Faye Server
        /// </summary>
        /// <param name="clientId">Client id returned from handshake</param>
        /// <returns>Result</returns>
        Task<Result> Disconnect(string clientId);

        /// <summary>
        /// Open a websocket connection
        /// </summary>
        /// <returns>Result</returns>
        Task<Result> Init();

        /// <summary>
        /// Close a websocket connection
        /// </summary>
        /// <param name="state">Used for internal testing</param>
        /// <returns>Result of the close attempt </returns>
        Task<Result> Close(int state = 1000);

        /// <summary>
        /// Reconnect to the websocket connection that was previously established 
        /// </summary>
        /// <returns>Result of the reconnect attempt </returns>
        Task<Result> Reconnect();

        /// <summary>
        /// Reconnect to the websocket connection with a new socket client using previously saved realtime connection configuration 
        /// </summary>
        /// <returns>Result of the attempt to resume connection </returns>
        Task<Result> ResumeConnection();

        #endregion

        #region Properties

        /// <summary>
        /// WebSocket state
        /// </summary>
        ZendeskSocketState State { get; }

        int CurrentConnectionAttempts { get; }

        #endregion

        #region Events

        /// <summary>
        /// event to be triggered when a message has been received from the server
        /// </summary>
        event Action<ZendeskWsEventArgs> MessageReceived;

        /// <summary>
        /// event to be triggered when websocket connection has been established
        /// </summary>
        event Action Connected;

        /// <summary>
        /// event to be triggered when the websocket connection has been disconnected
        /// </summary>
        event Action Disconnected;

        /// <summary>
        /// event to be triggered when the websocket connection has been in an error state
        /// </summary>
        event Action Error;

        #endregion
    }
}