using System;
using System.IO;
using System.Linq;

namespace Zendesk.MessagingCore.RestClient.DTO.NativeMessaging
{
    [Serializable]
    public class SendFileRequestDto
    {
        public AuthorRequestDto author;
        public FileSourceDto source;

        public static SendFileRequestDto Build(string filePath, string userId, string clientId, string integrationId)
        {
            var fileNameWithExtension = Path.GetFileName(filePath);
            var extension = fileNameWithExtension.Split('.').Last().ToLower();

            return new SendFileRequestDto
            {
                author = AuthorRequestDto.Build(userId, clientId, integrationId),
                source = new FileSourceDto(fileNameWithExtension, $"image/{extension}",
                    filePath)
            };
        }
    }

    [Serializable]
    public class FileSourceDto
    {
        public FileSourceDto(string fileName, string mimeType, string fileURL)
        {
            this.fileName = fileName;
            this.mimeType = mimeType;
            this.fileURL = fileURL;
        }

        public string fileName;
        public string mimeType;
        public string fileURL;
    }
}