using Newtonsoft.Json;
using Zendesk.MessagingCore.Environment;

namespace Zendesk.MessagingCore.RestClient.DTO.NativeMessaging
{
    public class ClientRequestDto
    {
        [JsonProperty("id")]
        public string Id { get; private set; }

        [JsonProperty("platform")]
        public string Platform { get; private set; }

        [JsonProperty("integrationId")]
        public string IntegrationId { get; private set; }

        [JsonProperty("appVersion")]
        public string AppVersion { get; private set; }

        [JsonProperty("info")]
        public ClientInfoDto Info { get; private set; }

        [JsonConstructor]
        public ClientRequestDto(string id, string platform, string integrationId, string appVersion, ClientInfoDto info)
        {
            Id = id;
            Platform = platform;
            IntegrationId = integrationId;
            AppVersion = appVersion;
            Info = info;
        }

        public static ClientRequestDto Build(string id, string integrationId, ClientInfoDto clientInfo)
        {
            return new ClientRequestDto(
                id: id,
                platform: Env.Current?.Platform.SunCoPlatform,
                integrationId: integrationId,
                appVersion: Env.Current?.Platform.SDKVersion,
                info: clientInfo
            );
        }
    }
}