using System;
using Zendesk.MessagingCore.Environment;

namespace Zendesk.MessagingCore.RestClient.Client
{
    internal class ZMRestBuilder : IZMRestBuilder
    {
        public IZendeskRestClient BuildSettingsRestClient(string url, string locale)
        {
            try
            {
                return new ZendeskRestClient(url, Env.Current.Platform.SDKVersion, Env.Current.Platform.OSVersion,
                    Env.Current.Platform.OSName,
                    locale);
            }
            catch (Exception exception)
            {
                Env.Current?.MessagingCoreEvents?.ExceptionCaught(exception);
                return null;
            }
        }

        public INativeMessagingRestClient BuildUserRestClient(string url, string appId, string integrationId, string locale)
        {
            try
            {
                return new NativeMessagingRestClient(
                    url, appId, integrationId,
                    Env.Current.Platform.SDKVersion, Env.Current.Platform.OSVersion, Env.Current.Platform.OSName, 
                    locale);
            }
            catch (Exception exception)
            {
                Env.Current?.MessagingCoreEvents?.ExceptionCaught(exception);
                return null;
            }
        }

        public IRestClient BuildRestClient()
        {
            try
            {
                return new RestClient();
            }
            catch (Exception exception)
            {
                Env.Current?.MessagingCoreEvents?.ExceptionCaught(exception);
                return null;
            }
        }
    }
}