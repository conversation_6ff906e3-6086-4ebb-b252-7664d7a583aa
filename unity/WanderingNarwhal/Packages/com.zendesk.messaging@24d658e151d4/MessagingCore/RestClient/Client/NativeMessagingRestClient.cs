using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendesk.MessagingCore.Messaging;
using Zendesk.MessagingCore.Models;
using Zendesk.MessagingCore.RestClient.Authenticator;
using Zendesk.MessagingCore.RestClient.Client.Request;
using Zendesk.MessagingCore.RestClient.DTO.NativeMessaging;
using Zendesk.MessagingCore.RestClient.Result;
using Zendesk.MessagingCore.RestClient.Services;
using Zendesk.MessagingCore.Serialisation;

namespace Zendesk.MessagingCore.RestClient.Client
{
    internal sealed class NativeMessagingRestClient : INativeMessagingRestClient
    {
        private const string _CreateUserPathTemplate = "/v2/apps/{0}/appusers";
        private const string _SendMessagePathTemplate = "/v2/apps/{0}/conversations/{1}/messages";
        private const string _SendFilePathTemplate = "/v2/apps/{0}/conversations/{1}/files";
        private const string _GetUserPathTemplate = "/v2/apps/{0}/appusers/{1}";
        private const string _GetConversationPathTemplate = "/v2/apps/{0}/conversations/{1}";
        private const string _CreateConversationPathTemplate = "/v2/apps/{0}/appusers/{1}/conversations";
        private const string _ConversationActivityPathTemplate = "/v2/apps/{0}/conversations/{1}/activity";
        private const string _UpdateConversationPathTemplate = "/v2/apps/{0}/conversations/{1}";
        private const string _UserLoginPathTemplate = "/v2/apps/{0}/login";
        private const string _UserLogoutPathTemplate = "/v2/apps/{0}/appusers/{1}/logout";

        private readonly IRestClient _restClient;
        private readonly ISerialisationStrategy _serialiser;
        private readonly string _appId;
        private readonly string _integrationId;
        private readonly Dictionary<string, string> _defaultHeaders;
        private readonly string _baseUrl;
        private readonly IPostbackService _postbackService;

        internal NativeMessagingRestClient(string baseUrl, string appId, string integrationId, string sdkVersion,
            string osVersion, string osName, string locale,
            ISerialisationStrategy serialiser = null)
        {
            _appId = appId;
            _integrationId = integrationId;
            _baseUrl = baseUrl;
            _defaultHeaders = GenerateDefaultHeaders(sdkVersion, osVersion, osName, appId, locale);
            _serialiser = serialiser ?? new JsonNetSerialisation();
            _restClient = new RestClient();
            _postbackService = new PostbackService(_baseUrl, _defaultHeaders, _restClient, _serialiser);
        }

        public async Task<Result<AppUserResponseDto>> CreateUser(string clientId,
            IReadOnlyDictionary<string, object> metadata = null, string intent = "conversation:start")
        {
            var createUserUrl = _baseUrl + string.Format(_CreateUserPathTemplate, _appId);

            // Generate request body
            var createAppUserRequestDto = AppUserRequestDto.Build(intent, clientId, _integrationId, metadata);
            var jsonBody = _serialiser.Serialise(createAppUserRequestDto);

            // Append smooch client id header
            var headers = new Dictionary<string, string>(_defaultHeaders) { { "x-smooch-clientid", clientId } };

            var request = new RestRequest.Builder()
                .WithMethod(HttpMethod.POST)
                .WithUrl(createUserUrl)
                .WithHeaders(headers)
                .WithBody(jsonBody)
                .Build();


            var response = await _restClient.PerformRequest(request);
            if (response.IsSuccess)
            {
                var appUserResponse = _serialiser.Deserialise<AppUserResponseDto>(response.Body);
                return new SuccessResult<AppUserResponseDto>(appUserResponse);
            }
            return new ErrorResult<AppUserResponseDto>(response.Body, response.StatusCode);
        }

        public async Task<Result.Result> SetConversationActivity(string conversationId,
            string userId, string clientId, Authentication authentication, string intent = "conversation:read")
        {
            var setConversationActivityUrl =
                _baseUrl + string.Format(_ConversationActivityPathTemplate, _appId, conversationId);

            // Generate request body
            var createAppUserRequestDto = ConversationActivityDto.Build(intent, userId, clientId, _integrationId);
            var jsonBody = _serialiser.Serialise(createAppUserRequestDto);

            // Append smooch client id header
            var headers = new Dictionary<string, string>(_defaultHeaders) { { "x-smooch-clientid", clientId } };

            var request = new RestRequest.Builder()
                .WithMethod(HttpMethod.POST)
                .WithUrl(setConversationActivityUrl)
                .WithHeaders(headers)
                .WithBody(jsonBody)
                .WithAuth(authentication.GetAuthenticator(userId))
                .Build();

            var response = await _restClient.PerformRequest(request);
            if (response.IsSuccess)
            {
                var conversationActivityResponse =
                    _serialiser.Deserialise<ConversationActivityResponseDto>(response.Body);
                return new SuccessResult<ConversationActivityResponseDto>(conversationActivityResponse);
            }
            return new ErrorResult<ConversationActivityResponseDto>(ZMConstants.ConversationCouldNotSetAsRead);
        }

        public async Task<Result<ConversationResponseDto>> UpdateConversation(string conversationId, string userId, Authentication authentication,
            string clientId, IReadOnlyDictionary<string, object> metadata)
        {
            var updateConversationUrl = _baseUrl + string.Format(_UpdateConversationPathTemplate, _appId, conversationId);

            // Generate request body
            var requestDto = UpdateConversationRequestDto.Build(clientId, _integrationId, metadata);
            var jsonBody = _serialiser.Serialise(requestDto);

            // Append smooch client id header
            var headers = new Dictionary<string, string>(_defaultHeaders) { { "x-smooch-clientid", clientId } };

            var request = new RestRequest.Builder()
                .WithMethod(HttpMethod.PUT)
                .WithUrl(updateConversationUrl)
                .WithHeaders(headers)
                .WithBody(jsonBody)
                .WithAuth(authentication.GetAuthenticator(userId))
                .Build();
            
            
            var response = await _restClient.PerformRequest(request);
            if (response.IsSuccess)
            {
                var conversationResponse = _serialiser.Deserialise<ConversationResponseDto>(response.Body);
                return new SuccessResult<ConversationResponseDto>(conversationResponse);
            }
            return new ErrorResult<ConversationResponseDto>(response.Body, response.StatusCode);
        }

        public async Task<Result<AppUserResponseDto>> GetUser(string clientId, string userId,
            Authentication authentication)
        {
            var getUserUrl = _baseUrl + string.Format(_GetUserPathTemplate, _appId, userId);

            // Append smooch client id header
            var headers = new Dictionary<string, string>(_defaultHeaders) { { "x-smooch-clientid", clientId } };

            var request = new RestRequest.Builder()
                .WithMethod(HttpMethod.GET)
                .WithUrl(getUserUrl)
                .WithHeaders(headers)
                .WithAuth(authentication.GetAuthenticator(userId))
                .Build();
            
            var response = await _restClient.PerformRequest(request);
            if (response.IsSuccess)
            {
                var appUserResponse = _serialiser.Deserialise<AppUserResponseDto>(response.Body);
                return new SuccessResult<AppUserResponseDto>(appUserResponse);
            }
            return new ErrorResult<AppUserResponseDto>(response.Body, response.StatusCode);
        }

        public async Task<Result<SendMessageResponseDto>> SendMessage(ZendeskMessageRequest messageRequest, Authentication authentication)
        {
            var sendMessageUrl = _baseUrl + string.Format(_SendMessagePathTemplate, _appId, messageRequest.ConversationId);
            messageRequest.IntegrationId = _integrationId;

            SendMessageRequestDto dto = messageRequest.Type switch
            {
                ZendeskMessageType.Text => SendMessageRequestDto.BuildTextMessage(
                    (ZendeskTextMessageRequest)messageRequest),
                ZendeskMessageType.QuickReplies => SendMessageRequestDto.BuildQuickRepliesMessage(
                    (ZendeskQuickRepliesMessageRequest)messageRequest),
                ZendeskMessageType.Form => SendMessageRequestDto.BuildFormMessage(
                    (ZendeskFormMessageRequest)messageRequest),
                _ => null
            };

            var jsonDto = _serialiser.Serialise(dto);

            var request = new RestRequest.Builder()
                .WithUrl(sendMessageUrl)
                .WithMethod(HttpMethod.POST)
                .WithHeaders(_defaultHeaders)
                .WithBody(jsonDto)
                .WithAuth(authentication.GetAuthenticator(messageRequest.UserId))
                .Build();
            
            var response = await _restClient.PerformRequest(request);
            if (response.IsSuccess)
            {
                var sendMessageResponse = _serialiser.Deserialise<SendMessageResponseDto>(response.Body);
                return new SuccessResult<SendMessageResponseDto>(sendMessageResponse);
            }
            return new ErrorResult<SendMessageResponseDto>(response.Body, response.StatusCode);
        }

        public async Task<Result<SendFileResponseDto>> SendFile(string filePath, string conversationId,
            string userId, Authentication authentication, string clientId)
        {
            // Update headers
            // make sure content type is not set in default headers
            // as it's most likely not correct and won't be overwritten later
            var updatedHeaders = new Dictionary<string, string>(_defaultHeaders);
            updatedHeaders.Remove("Content-Type");

            var sendFileUrl = _baseUrl + string.Format(_SendFilePathTemplate, _appId, conversationId);
            var dto = SendFileRequestDto.Build(filePath, userId, clientId, _integrationId);

            var formData = FileFormData.Create(
                FieldNameData.Create(
                    "author",
                    dto.author
                ),
                BinaryData.Create(
                    "source",
                    dto.source.fileURL,
                    dto.source.fileName,
                    dto.source.mimeType
                )
            );

            var request = new RestRequest.Builder()
                .WithMethod(HttpMethod.POST)
                .WithUrl(sendFileUrl)
                .WithHeaders(updatedHeaders)
                .WithFormData(formData)
                .WithAuth(authentication.GetAuthenticator(userId))
                .Build();
            
            var response = await _restClient.PerformRequest(request);
            if (response.IsSuccess)
            {
                var sendFileResponse = _serialiser.Deserialise<SendFileResponseDto>(response.Body);
                return new SuccessResult<SendFileResponseDto>(sendFileResponse);
            }
            return new ErrorResult<SendFileResponseDto>(response.Body, response.StatusCode);
        }

        public async Task<Result<bool>> SendPostback(PostbackParameters postbackParameters)
        {
            return await _postbackService.SendPostback(postbackParameters);
        }

        public async Task<Result<ConversationResponseDto>> GetConversation(string conversationId, string userId,
            Authentication authentication)
        {
            var getConversationUrl = _baseUrl + string.Format(_GetConversationPathTemplate, _appId, conversationId);

            var request = new RestRequest.Builder()
                .WithMethod(HttpMethod.GET)
                .WithHeaders(_defaultHeaders)
                .WithUrl(getConversationUrl)
                .WithAuth(authentication.GetAuthenticator(userId))
                .Build();

            var response = await _restClient.PerformRequest(request);
            if (response.IsSuccess)
            {
                var conversationResponse = _serialiser.Deserialise<ConversationResponseDto>(response.Body);
                return new SuccessResult<ConversationResponseDto>(conversationResponse);
            }
            return new ErrorResult<ConversationResponseDto>(response.Body, response.StatusCode);
        }
        
       public async Task<Result<AppUserResponseDto>> Login(string externalUserId, string jwt, string clientId, string anonymousUserId = null,
            string anonymousUserSessionToken = null)
        {
            var loginUrl = _baseUrl + string.Format(_UserLoginPathTemplate, _appId);

            var userLoginRequestDto = LoginRequestDto.Build(externalUserId, clientId, _integrationId, anonymousUserId,
                anonymousUserSessionToken);
            var jsonBody = _serialiser.Serialise(userLoginRequestDto);

            var request = new RestRequest.Builder()
                .WithMethod(HttpMethod.POST)
                .WithUrl(loginUrl)
                .WithHeaders(_defaultHeaders)
                .WithBody(jsonBody)
                .WithAuth(new JwtAuthenticator(jwt))
                .Build();

            var apiResponse = await _restClient.PerformRequest(request);
            
            if (apiResponse.StatusCode == 200 || apiResponse.StatusCode == 201)
            {
                try
                {
                    return new SuccessResult<AppUserResponseDto>(_serialiser.Deserialise<AppUserResponseDto>(apiResponse.Body));
                }
                catch (Exception)
                {
                    return new LoginApiErrorResult<AppUserResponseDto>(ErrorReason.FailedToParse);
                }
            }

            switch (apiResponse.StatusCode)
            {
                case 204:
                    return new LoginApiErrorResult<AppUserResponseDto>(ErrorReason.JwtValidButUserNotFound);
                case 401:
                    return new LoginApiErrorResult<AppUserResponseDto>(ErrorReason.InvalidJwt);
                case 403:
                    return new LoginApiErrorResult<AppUserResponseDto>(ErrorReason.ForbiddenAccess);
                default:
                    return new LoginApiErrorResult<AppUserResponseDto>(ErrorReason.UnhandledError);
            }
        }


        public async Task<Result.Result> Logout(string clientId, string userId, Authentication authentication)
        {
            var logoutUrl = _baseUrl + string.Format(_UserLogoutPathTemplate, _appId, userId);
            
            var userLogoutRequestDto = LogoutRequestDto.Build(clientId, _integrationId);
            var jsonBody = _serialiser.Serialise(userLogoutRequestDto);

            var request = new RestRequest.Builder()
                .WithMethod(HttpMethod.POST)
                .WithUrl(logoutUrl)
                .WithHeaders(_defaultHeaders)
                .WithBody(jsonBody)
                .WithAuth(new JwtAuthenticator(authentication.token))
                .Build();

            var apiResponse = await _restClient.PerformRequest(request);
            if (apiResponse.StatusCode == 200)
                return new SuccessResult();

            return new ErrorResult(ZMConstants.FailedToLogOut);
        }

        public async Task<Result<ConversationResponseDto>> CreateConversation(string clientId, string userId, Authentication authentication, Dictionary<string, object> metadata = null)
        {
            var createConversationUrl = _baseUrl + string.Format(_CreateConversationPathTemplate, _appId, userId);
            
            var createConversationDto = CreateConversationRequestDto.Build("conversation:start",  ConversationType.Personal, clientId, _integrationId, metadata);
            var jsonBody = _serialiser.Serialise(createConversationDto);

            // Append smooch client id header
            var headers = new Dictionary<string, string>(_defaultHeaders) {{"x-smooch-clientid", clientId}};

            var request = new RestRequest.Builder()
                .WithMethod(HttpMethod.POST)
                .WithHeaders(headers)
                .WithUrl(createConversationUrl)
                .WithBody(jsonBody)
                .WithAuth(authentication.GetAuthenticator(userId))
                .Build();

            var response = await _restClient.PerformRequest(request);
            if (response.IsSuccess)
            {
                var conversationResponse = _serialiser.Deserialise<ConversationResponseDto>(response.Body);
                return new SuccessResult<ConversationResponseDto>(conversationResponse);
            }
            return new ErrorResult<ConversationResponseDto>(response.Body, response.StatusCode);
        }

        private static Dictionary<string, string> GenerateDefaultHeaders(string sdkVersion, string osVersion,
            string osName, string appId, string locale)
        {
            var userAgentHeader = HeaderBuilder.CreateUserAgentHeader(sdkVersion, osName, osVersion);
            var smoochSdkHeader = HeaderBuilder.CreateSmoochSdkHeader(sdkVersion);
            
            return new Dictionary<string, string>
            {
                { "Content-Type", "application/json" },
                { "Accept", "application/json" },
                { userAgentHeader.Key, userAgentHeader.Value },
                { smoochSdkHeader.Key, smoochSdkHeader.Value },
                { "x-smooch-appid", $"{appId}" },
                { "Accept-Language", $"{locale}" }
            };
        }
    }
}