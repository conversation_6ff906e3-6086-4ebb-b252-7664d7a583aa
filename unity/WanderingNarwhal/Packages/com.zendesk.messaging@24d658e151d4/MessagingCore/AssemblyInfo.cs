using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("Unity.Zendesk")]
[assembly: InternalsVisibleTo("Unity.Zendesk.Editor")]
[assembly: InternalsVisibleTo("DevScenes")]
[assembly: InternalsVisibleTo("Internal_Test_Scenes")]
[assembly: InternalsVisibleTo("Unity.Zendesk.Tests")]
[assembly: InternalsVisibleTo("Unity.Zendesk.Editor.Tests")]
[assembly: InternalsVisibleTo("Unity.Zendesk.Play.Tests")]
[assembly: InternalsVisibleTo("DynamicProxyGenAssembly2")] // NSubstitute
