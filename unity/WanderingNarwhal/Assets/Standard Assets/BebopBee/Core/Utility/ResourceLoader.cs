using System;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.Audio;

namespace Bebopbee.Core.Utility
{
    public class ResourceLoader
    {
        public static T LoadJson<T>(string path) where T : class
        {
            return JsonFromText<T>(LoadText(path));
        }

        public static string LoadText(string path)
        {
            var textAsset = Resources.Load<TextAsset>(path);
            return textAsset == null ? null : textAsset.text;
        }

        private static T JsonFromText<T>(string content) where T : class
        {
            if (!string.IsNullOrEmpty(content))
            {
                try
                {
                    return JsonConvert.DeserializeObject<T>(content);
                }
                catch (Exception e)
                {
                    Debug.LogError("Error LoadJson [" + e.Message + "]");
                }
            }

            return null;
        }

        public static AudioClip LoadAudioClip(string path)
        {
            return Resources.Load<AudioClip>(path);
        }

        public static AudioMixer[] LoadAudioMixer(string path)
        {
            return Resources.LoadAll<AudioMixer>(path);
        }
    }
}