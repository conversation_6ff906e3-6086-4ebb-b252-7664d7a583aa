using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Audio;

namespace BebopBee.Core.Audio
{
    public class AudioContextSettings : ScriptableObject
    {
        [Serializable]
        public class AudioClipData
        {
            public string Id;
            public AudioClip Clip;
        }

        public string MixerName;
        public List<AudioClipData> Clips;
    }
}