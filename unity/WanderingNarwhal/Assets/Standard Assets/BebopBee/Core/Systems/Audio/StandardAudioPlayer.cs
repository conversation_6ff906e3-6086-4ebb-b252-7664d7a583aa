using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Audio;
using BebopBee.Core.Collections;
using Beebopbee.Core.Extensions;

namespace BebopBee.Core.Audio
{
    public class StandardAudioPlayer : MonoBehaviour, IMusicPlayer, ISoundsPlayer, IAudioConfigurable
    {
        private const string GameObjectName = "AudioPlayer";
        private static StandardAudioPlayer _instance;

        private AudioSettings _audioSettings;
        private readonly Dictionary<string, AudioMixer> _mixersDict = new Dictionary<string, AudioMixer>();

        private AudioSource _musicSource;
        private Tweener _musicSourceTweener;
        private AudioSourcePool _soundSourcePool;
        private readonly Queue<ActiveAudioSource> _activeSourcesQueue = new Queue<ActiveAudioSource>();
        private readonly Multimap<string, ActiveAudioSource> _activeSourcesMap = new Multimap<string, ActiveAudioSource>();

        private IAudioLogger _logger;
        public string CurrentMusicId { get; private set; }

        public static StandardAudioPlayer Create(IEnumerable<AudioMixer> mixersCollection, IAudioLogger logger = null)
        {
            if (_instance != null)
            {
                _instance.Clear();
                return _instance;
            }
            var manager = new GameObject(GameObjectName).AddComponent<StandardAudioPlayer>();

            manager.Init(mixersCollection, logger);
            _instance = manager;
            DontDestroyOnLoad(_instance);
            return _instance;
        }

        public static void Restart()
        {
            if (_instance != null)
            {
                _instance.Clear();
                Destroy(_instance.gameObject);
            }
            _instance = null;
        }

        private void Init(IEnumerable<AudioMixer> mixersCollection, IAudioLogger logger)
        {
            foreach (var mixer in mixersCollection)
            {
                if (null != mixer)
                {
                    _mixersDict.Add(mixer.name, mixer);
                }
            }

            _audioSettings = new AudioSettings(_mixersDict);

            _musicSource = gameObject.AddComponent<AudioSource>();
            _soundSourcePool = gameObject.AddComponent<AudioSourcePool>();
            _soundSourcePool.Prewarm(20);

            _logger = logger;
        }

        private void Clear()
        {
            ((ISoundsPlayer)this).StopAllSounds();
            _soundSourcePool.Clear();
            _audioSettings.Clear();
        }

        private void Update()
        {
            if (_activeSourcesQueue.Count == 0 || !_activeSourcesQueue.Peek().IsExpired(Time.time)) 
                return;

            var timestampedSource = _activeSourcesQueue.Dequeue();
            _activeSourcesMap.Remove(timestampedSource.SoundId, timestampedSource);
            _soundSourcePool.Release(timestampedSource.AudioSource);
        }

        void IAudioConfigurable.AddContext(AudioContextSettings settings)
        {
            _audioSettings.AddExternalContext(settings);
        }

        void IAudioConfigurable.RemoveContext(AudioContextSettings settings)
        {
            _audioSettings.RemoveExternalContext(settings);
        }

        void ISoundsPlayer.PlayRandomSound(string startingWith, bool loop)
        {
            var soundId = _audioSettings.GetRandomKeyStartingWith(startingWith);
            
            if(!string.IsNullOrWhiteSpace(soundId))
                ((ISoundsPlayer) this).PlaySound(soundId, loop, true);
        }

        void ISoundsPlayer.PlaySound(string soundId, bool loop, bool allowDuplicate)
        {
            if (soundId.IsNullOrEmpty()) return;
            var activeSourcesForThisSound = _activeSourcesMap.GetValues(soundId);

            if (activeSourcesForThisSound != null)
            {
                var currentTime = Time.time;
                foreach (var source in activeSourcesForThisSound)
                {
                    if (source.IsNear(currentTime))
                        return;
                }

                if (!allowDuplicate)
                    return;
            }

            var soundClipInfo = _audioSettings.GetClipInfo(soundId);

            if (soundClipInfo == null || soundClipInfo.Clip == null)
                return;

            var audioSource = _soundSourcePool.Spawn(out bool createdNew);

            if (createdNew)
                _logger.LogWarning($"Too many audiosources busy while playing {soundId}, created new one");

            var timestampedSource = new ActiveAudioSource(soundId, audioSource: audioSource);

            if (!loop)
            {
                _activeSourcesQueue.Enqueue(timestampedSource);
            }

            _activeSourcesMap.Add(soundId, timestampedSource);

            if (audioSource == null)
            {
                _logger.LogError($"Trying to play {soundId} with null AudioSource");
                return;
            }

            audioSource.loop                  = loop;
            audioSource.clip                  = soundClipInfo.Clip;
            audioSource.outputAudioMixerGroup = soundClipInfo.MixerGroup;
            audioSource.Play();
            _logger.LogSound(soundId, soundClipInfo, loop);
        }

        void ISoundsPlayer.StopSound(string soundId)
        {
            if (soundId.IsNullOrEmpty()) return;
            var sources = _activeSourcesMap.GetValues(soundId);

            if (sources != null)
            {
                foreach (var source in sources)
                {
                    if (source.AudioSource != null)
                    {

                        source.AudioSource.Stop();
                        _soundSourcePool.Release(source.AudioSource);
                    }
                }
            }

            _activeSourcesMap.Remove(soundId);
        }

        void ISoundsPlayer.StopAllSounds()
        {
            _activeSourcesQueue.Clear();

            foreach (var source in _activeSourcesMap.AllValues)
            {
                source.AudioSource.Stop();
                _soundSourcePool.Release(source.AudioSource);
            }

            _activeSourcesMap.Clear();
        }

        IMusicPlayer.MusicPlayed IMusicPlayer.TryPlayMusic(string musicId, bool loop)
        {
            var musicClipInfo = _audioSettings.GetClipInfo(musicId);

            if (musicClipInfo == null)
                return IMusicPlayer.MusicPlayed.Fail;

            _musicSource.loop = loop;

            if (_musicSource.clip != null && musicClipInfo.Clip.GetInstanceID() == _musicSource.clip.GetInstanceID())
                return IMusicPlayer.MusicPlayed.AlreadyPlaying;

            if (_musicSourceTweener != null)
            {
                _musicSourceTweener.Kill();
                _musicSourceTweener = null;
            }

            //if something is already playing we fade it down first
            if (_musicSource.isPlaying)
            {
                _musicSourceTweener = _musicSource.DOFade(0f, AudioConstants.MusicFadeTime).OnComplete(() =>
                {
                    CurrentMusicId = musicId;
                    StartMusicClip(musicClipInfo);
                });
            }
            else
            {
                CurrentMusicId = musicId;
                StartMusicClip(musicClipInfo);
            }

            return IMusicPlayer.MusicPlayed.Success;
        }

        private void StartMusicClip(AudioClipInfo musicClipInfo)
        {

            _musicSource.clip = musicClipInfo.Clip;
            _musicSource.outputAudioMixerGroup = musicClipInfo.MixerGroup;
            _musicSource.volume = 0f;
            _musicSource.Play();

            _musicSourceTweener = _musicSource.DOFade(1f, AudioConstants.MusicFadeTime).OnComplete(() =>
            {
                _musicSourceTweener = null;
            });
        }

        void IMusicPlayer.StopMusicImmediately()
        {
            if (_musicSource != null)
            {
                _musicSource.Stop();
                _musicSource.loop = false;
                _musicSource.clip = null;
                _musicSource.outputAudioMixerGroup = null;   
            }
        }
        
        void IMusicPlayer.StopMusic()
        {
            if (_musicSourceTweener != null)
            {
                _musicSourceTweener.Kill();
                _musicSourceTweener = null;
            }

            _musicSourceTweener = _musicSource.DOFade(0f, AudioConstants.MusicFadeTime)
                                              .OnComplete(OnStopMusicComplete);
        }

        private void OnStopMusicComplete()
        {
            _musicSource.Stop();
            _musicSource.loop = false;
            _musicSource.clip = null;
            _musicSource.outputAudioMixerGroup = null;
            _musicSourceTweener = null;
            CurrentMusicId = null;
        }

        void IMusicPlayer.MuteMusic(bool value)
        {
            if (_musicSourceTweener != null)
            {
                _musicSourceTweener.Kill();
                _musicSourceTweener = null;
            }

            if (!value)
                _musicSource.mute = false;

            _musicSourceTweener = _musicSource.DOFade(value ? 0f : 1f, AudioConstants.MusicFadeTime).OnComplete(() =>
            {
                if (value)
                    _musicSource.mute = true;

                _musicSourceTweener = null;
            });
        }

        void IAudioConfigurable.SetMixerValue(string mixerName, string valueName, float value)
        {
            if (_mixersDict.TryGetValue(mixerName, out var mixer))
            {
                bool success = mixer.SetFloat(valueName, value);
                _logger.Log($"SetMixerValue Mixer {mixerName} value {valueName} change to {value} ... Result: {success}");
            }
            else
            {
                _logger.LogError($"SetMixerValue Mixer {mixerName} is not found");
            }
        }

        float IAudioConfigurable.GetMixerValue(string mixerName, string valueName)
        {
            AudioMixer mixer;
            float toReturn = 0;
            if (_mixersDict.TryGetValue(mixerName, out mixer))
            {
                if (mixer.GetFloat(valueName, out toReturn))
                    _logger.Log($"[SoundMixer] Value of the {mixerName}, {valueName} is : {toReturn}");
                else
                    _logger.LogError($"[SoundMixer] Can't get {valueName} or {mixerName}");
            }
            else
            {
                _logger.LogError($"GetMixerValue Mixer {mixerName} is not found");
            }

            return toReturn;
        }
    }
}
