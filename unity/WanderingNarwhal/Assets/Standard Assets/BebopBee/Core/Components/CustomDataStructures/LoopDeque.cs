using System.Collections;
using System.Collections.Generic;

namespace BebopBee
{
    public class LoopDeque<T> : IEnumerable<T>
    {
        private readonly Deque<T> _deque;

        public LoopDeque(IEnumerable<T> elements)
        {
            _deque = new Deque<T>(elements);
        }

        public LoopDeque()
        {
            _deque = new Deque<T>();
        } 

        public IEnumerator<T> GetEnumerator()
        {
            return _deque.GetEnumerator();
        }
        
        IEnumerator IEnumerable.GetEnumerator()
        {
            return _deque.GetEnumerator();
        }

        public T Peek()
        {
            return _deque.GetFirst();
        }

        public void RollCounterClockwise()
        {
            var element = _deque.RemoveFromBack();
            _deque.AddToFront(element);
        }

        public void RollClockwise()
        {
            var element = _deque.RemoveFromFront();
            _deque.AddToBack(element);
        }
        
        public bool RollUntil(T element)
        {
            var startElement = Peek();
            var currentElement = Peek();

            while(!currentElement.Equals(element))
            {
                RollClockwise();
                currentElement = Peek();
                if (currentElement.Equals(startElement))
                    return false;
            }

            return true;
        }

        public void Add(T element)
        {
            _deque.AddToBack(element);
        }

        public void Clear()
        {
            _deque.Clear();
        }

    }
}