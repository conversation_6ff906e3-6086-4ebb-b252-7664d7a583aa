using System;
using UnityEngine.EventSystems;

namespace UnityEngine.UI.Extensions
{
    public class ScrollRectExtended : ScrollRect {
 
        private bool _routeToParent = false;
 

        private ScrollRect _parent;

        public void InitParent()
        {
            var parent = transform.parent;
            while (parent != null)
            {
                foreach (var component in parent.GetComponents<Component>())
                {
                    if (component is ScrollRect)
                    {
                        _parent = component as ScrollRect;
                        return;
                    }
                }

                parent = parent.parent;
            }
        }
 
        private void DoForParent<T>(Action<T> action) where T : IEventSystemHandler
        {
            if (_parent is T)
                action((T)(IEventSystemHandler)_parent);
        }
        
        /// <summary>
        /// Always route initialize potential drag event to parents
        /// </summary>
        public override void OnInitializePotentialDrag (PointerEventData eventData)
        {
            DoForParent<IInitializePotentialDragHandler>((parent) => { parent.OnInitializePotentialDrag(eventData); });
            base.OnInitializePotentialDrag (eventData);
        }
 
        /// <summary>
        /// Drag event
        /// </summary>
        public override void OnDrag (PointerEventData eventData)
        {
            if(_routeToParent)
                DoForParent<IDragHandler>((parent) => { parent.OnDrag(eventData); });
            else
                base.OnDrag (eventData);
        }
 
        /// <summary>
        /// Begin drag event
        /// </summary>
        public override void OnBeginDrag (PointerEventData eventData)
        {
            if(!horizontal && Math.Abs (eventData.delta.x) > Math.Abs (eventData.delta.y))
                _routeToParent = true;
            else if(!vertical && Math.Abs (eventData.delta.x) < Math.Abs (eventData.delta.y))
                _routeToParent = true;
            else
                _routeToParent = false;
 
            if(_routeToParent)
                DoForParent<IBeginDragHandler>((parent) => { parent.OnBeginDrag(eventData); });
            else
                base.OnBeginDrag (eventData);
        }
 
        /// <summary>
        /// End drag event
        /// </summary>
        public override void OnEndDrag (PointerEventData eventData)
        {
            if(_routeToParent)
                DoForParent<IEndDragHandler>((parent) => { parent.OnEndDrag(eventData); });
            else
                base.OnEndDrag (eventData);
            _routeToParent = false;
        }
    }
}