using System;
using System.Collections.Generic;

// <auto-generated> DO NOT EDIT — run Tools/Generate ConfigTypeRegistry </auto-generated>
public static class ConfigTypeRegistry
{
    public static Dictionary<string, Type> All { get; } = new Dictionary<string, Type>
    {
        { "AbTestingConfig", typeof(PBConfig.AbTestingConfig) },
        { "ActionItemT", typeof(FBConfig.ActionItemT) },
        { "ActionTupleT", typeof(FBConfig.ActionTupleT) },
        { "AdPlacementConfig", typeof(FBConfig.AdPlacementConfig) },
        { "AdPlacementConfigDictT", typeof(FBConfig.AdPlacementConfigDictT) },
        { "AdPlacementConfigT", typeof(FBConfig.AdPlacementConfigT) },
        { "AdsConfig", typeof(FBConfig.AdsConfig) },
        { "AdsConfigDictT", typeof(FBConfig.AdsConfigDictT) },
        { "AdsConfigT", typeof(FBConfig.AdsConfigT) },
        { "AimingValuesT", typeof(FBConfig.AimingValuesT) },
        { "AnimalsOnMapConfig", typeof(PBConfig.AnimalsOnMapConfig) },
        { "AnimatedDecorationConfig", typeof(PBConfig.AnimatedDecorationConfig) },
        { "AnimationDataT", typeof(FBConfig.AnimationDataT) },
        { "AnimationsConfig", typeof(PBConfig.AnimationsConfig) },
        { "ArtSelfieConfig", typeof(PBConfig.ArtSelfieConfig) },
        { "ArtSelfieSubjectConfig", typeof(FBConfig.ArtSelfieSubjectConfig) },
        { "ArtSelfieSubjectConfigDictT", typeof(FBConfig.ArtSelfieSubjectConfigDictT) },
        { "ArtSelfieSubjectConfigT", typeof(FBConfig.ArtSelfieSubjectConfigT) },
        { "Asset2BundleConfig", typeof(PBConfig.Asset2BundleConfig) },
        { "AssistConfig", typeof(PBConfig.AssistConfig) },
        { "AssistMatrixConfig", typeof(PBConfig.AssistMatrixConfig) },
        { "AssistSystemConfig", typeof(FBConfig.AssistSystemConfig) },
        { "AssistSystemConfigDictT", typeof(FBConfig.AssistSystemConfigDictT) },
        { "AssistSystemConfigT", typeof(FBConfig.AssistSystemConfigT) },
        { "AssistUidAfterIapT", typeof(FBConfig.AssistUidAfterIapT) },
        { "AssistWinRateAfterIapT", typeof(FBConfig.AssistWinRateAfterIapT) },
        { "AudioMixerConfig", typeof(PBConfig.AudioMixerConfig) },
        { "AutoPopupPriorityConfig", typeof(FBConfig.AutoPopupPriorityConfig) },
        { "AutoPopupPriorityConfigDictT", typeof(FBConfig.AutoPopupPriorityConfigDictT) },
        { "AutoPopupPriorityConfigT", typeof(FBConfig.AutoPopupPriorityConfigT) },
        { "BaseScoresT", typeof(FBConfig.BaseScoresT) },
        { "BattlePassConfig", typeof(PBConfig.BattlePassConfig) },
        { "BehaviorConfig", typeof(PBConfig.BehaviorConfig) },
        { "BoosterConfig", typeof(FBConfig.BoosterConfig) },
        { "BoosterConfigDictT", typeof(FBConfig.BoosterConfigDictT) },
        { "BoosterConfigT", typeof(FBConfig.BoosterConfigT) },
        { "BoosterOfferConfig", typeof(PBConfig.BoosterOfferConfig) },
        { "BotClaimConfigT", typeof(FBConfig.BotClaimConfigT) },
        { "BrainCloudSettingsT", typeof(FBConfig.BrainCloudSettingsT) },
        { "BuildingConfig", typeof(PBConfig.BuildingConfig) },
        { "BuildingsShopCategoryConfig", typeof(PBConfig.BuildingsShopCategoryConfig) },
        { "BundleIndexConfig", typeof(FBConfig.BundleIndexConfig) },
        { "BundleIndexConfigDictT", typeof(FBConfig.BundleIndexConfigDictT) },
        { "BundleIndexConfigT", typeof(FBConfig.BundleIndexConfigT) },
        { "BundleInfoConfig", typeof(FBConfig.BundleInfoConfig) },
        { "BundleInfoConfigDictT", typeof(FBConfig.BundleInfoConfigDictT) },
        { "BundleInfoConfigT", typeof(FBConfig.BundleInfoConfigT) },
        { "ButlerGiftConfig", typeof(FBConfig.ButlerGiftConfig) },
        { "ButlerGiftConfigDictT", typeof(FBConfig.ButlerGiftConfigDictT) },
        { "ButlerGiftConfigT", typeof(FBConfig.ButlerGiftConfigT) },
        { "CarrotConfig", typeof(FBConfig.CarrotConfig) },
        { "CarrotConfigT", typeof(FBConfig.CarrotConfigT) },
        { "CarrotsConfig", typeof(FBConfig.CarrotsConfig) },
        { "CarrotsConfigDictT", typeof(FBConfig.CarrotsConfigDictT) },
        { "CarrotsConfigT", typeof(FBConfig.CarrotsConfigT) },
        { "CategoryParamsT", typeof(FBConfig.CategoryParamsT) },
        { "ChallengeConfig", typeof(FBConfig.ChallengeConfig) },
        { "ChallengeConfigDictT", typeof(FBConfig.ChallengeConfigDictT) },
        { "ChallengeConfigT", typeof(FBConfig.ChallengeConfigT) },
        { "ChallengeLocationConfig", typeof(FBConfig.ChallengeLocationConfig) },
        { "ChallengeLocationConfigDictT", typeof(FBConfig.ChallengeLocationConfigDictT) },
        { "ChallengeLocationConfigT", typeof(FBConfig.ChallengeLocationConfigT) },
        { "ChallengeTriviaConfig", typeof(FBConfig.ChallengeTriviaConfig) },
        { "ChallengeTriviaConfigDictT", typeof(FBConfig.ChallengeTriviaConfigDictT) },
        { "ChallengeTriviaConfigT", typeof(FBConfig.ChallengeTriviaConfigT) },
        { "ChallengeTriviaRewardsConfig", typeof(FBConfig.ChallengeTriviaRewardsConfig) },
        { "ChallengeTriviaRewardsConfigDictT", typeof(FBConfig.ChallengeTriviaRewardsConfigDictT) },
        { "ChallengeTriviaRewardsConfigT", typeof(FBConfig.ChallengeTriviaRewardsConfigT) },
        { "CitySpawnDecorationConfig", typeof(PBConfig.CitySpawnDecorationConfig) },
        { "CollectionCardsConfig", typeof(FBConfig.CollectionCardsConfig) },
        { "CollectionCardsConfigDictT", typeof(FBConfig.CollectionCardsConfigDictT) },
        { "CollectionCardsConfigT", typeof(FBConfig.CollectionCardsConfigT) },
        { "CollectionConfig", typeof(FBConfig.CollectionConfig) },
        { "CollectionConfigDictT", typeof(FBConfig.CollectionConfigDictT) },
        { "CollectionConfigT", typeof(FBConfig.CollectionConfigT) },
        { "CollectionSetConfig", typeof(FBConfig.CollectionSetConfig) },
        { "CollectionSetConfigDictT", typeof(FBConfig.CollectionSetConfigDictT) },
        { "CollectionSetConfigT", typeof(FBConfig.CollectionSetConfigT) },
        { "CommunityConfig", typeof(PBConfig.CommunityConfig) },
        { "CompetitionGameEventConfig", typeof(FBConfig.CompetitionGameEventConfig) },
        { "CompetitionGameEventConfigDictT", typeof(FBConfig.CompetitionGameEventConfigDictT) },
        { "CompetitionGameEventConfigT", typeof(FBConfig.CompetitionGameEventConfigT) },
        { "CompetitionLeaderboardConfigT", typeof(FBConfig.CompetitionLeaderboardConfigT) },
        { "ConfigMetadataT", typeof(FBConfig.ConfigMetadataT) },
        { "ConfigsManifestT", typeof(FBConfig.ConfigsManifestT) },
        { "CountriesTiersConfig", typeof(PBConfig.CountriesTiersConfig) },
        { "CurrencyPairT", typeof(FBConfig.CurrencyPairT) },
        { "CurrencyT", typeof(FBConfig.CurrencyT) },
        { "DailyEventConfig", typeof(PBConfig.DailyEventConfig) },
        { "DailyLoginConfig", typeof(PBConfig.DailyLoginConfig) },
        { "DailyTasksConfig", typeof(FBConfig.DailyTasksConfig) },
        { "DailyTasksConfigDictT", typeof(FBConfig.DailyTasksConfigDictT) },
        { "DailyTasksConfigT", typeof(FBConfig.DailyTasksConfigT) },
        { "DailyTaskSettingsConfig", typeof(FBConfig.DailyTaskSettingsConfig) },
        { "DailyTaskSettingsConfigDictT", typeof(FBConfig.DailyTaskSettingsConfigDictT) },
        { "DailyTaskSettingsConfigT", typeof(FBConfig.DailyTaskSettingsConfigT) },
        { "DailyTasksLocalNotifierSettingsT", typeof(FBConfig.DailyTasksLocalNotifierSettingsT) },
        { "DailyTriviaConfig", typeof(PBConfig.DailyTriviaConfig) },
        { "DayMonthYearT", typeof(FBConfig.DayMonthYearT) },
        { "DecorationConfig", typeof(PBConfig.DecorationConfig) },
        { "DecorationConfigBase", typeof(PBConfig.DecorationConfigBase) },
        { "DefaultNamesConfig", typeof(FBConfig.DefaultNamesConfig) },
        { "DefaultNamesConfigDictT", typeof(FBConfig.DefaultNamesConfigDictT) },
        { "DefaultNamesConfigT", typeof(FBConfig.DefaultNamesConfigT) },
        { "DeviceGradingConfig", typeof(PBConfig.DeviceGradingConfig) },
        { "DialogsConfig", typeof(PBConfig.DialogsConfig) },
        { "DictIntIntT", typeof(FBConfig.DictIntIntT) },
        { "DictStringFloatT", typeof(FBConfig.DictStringFloatT) },
        { "DictStringIntT", typeof(FBConfig.DictStringIntT) },
        { "DictStringLeagueRatioT", typeof(FBConfig.DictStringLeagueRatioT) },
        { "DictStringListIntT", typeof(FBConfig.DictStringListIntT) },
        { "DictStringStringT", typeof(FBConfig.DictStringStringT) },
        { "DistrictConfig", typeof(PBConfig.DistrictConfig) },
        { "EndlessTreasureConfig", typeof(FBConfig.EndlessTreasureConfig) },
        { "EndlessTreasureConfigDictT", typeof(FBConfig.EndlessTreasureConfigDictT) },
        { "EndlessTreasureConfigItemT", typeof(FBConfig.EndlessTreasureConfigItemT) },
        { "EndlessTreasureConfigT", typeof(FBConfig.EndlessTreasureConfigT) },
        { "ExpansionConfig", typeof(PBConfig.ExpansionConfig) },
        { "FakeUsersConfig", typeof(FBConfig.FakeUsersConfig) },
        { "FakeUsersConfigDictT", typeof(FBConfig.FakeUsersConfigDictT) },
        { "FakeUsersConfigT", typeof(FBConfig.FakeUsersConfigT) },
        { "FeaturedConfig", typeof(PBConfig.FeaturedConfig) },
        { "FeedbackConfig", typeof(PBConfig.FeedbackConfig) },
        { "FragmentT", typeof(FBConfig.FragmentT) },
        { "FurryPalsConfig", typeof(PBConfig.FurryPalsConfig) },
        { "FurryPalsMetaConfig", typeof(PBConfig.FurryPalsMetaConfig) },
        { "GachaConfig", typeof(FBConfig.GachaConfig) },
        { "GachaConfigDictT", typeof(FBConfig.GachaConfigDictT) },
        { "GachaConfigT", typeof(FBConfig.GachaConfigT) },
        { "GachaPrizeConfig", typeof(PBConfig.GachaPrizeConfig) },
        { "GachaPrizeConfigT", typeof(FBConfig.GachaPrizeConfigT) },
        { "GachaSlotPrizesConfigT", typeof(FBConfig.GachaSlotPrizesConfigT) },
        { "GameEventConfig", typeof(PBConfig.GameEventConfig) },
        { "GameEventLevelConfig", typeof(PBConfig.GameEventLevelConfig) },
        { "GameEventMetaConfig", typeof(PBConfig.GameEventMetaConfig) },
        { "GameUpdateConfig", typeof(FBConfig.GameUpdateConfig) },
        { "GameUpdateConfigDictT", typeof(FBConfig.GameUpdateConfigDictT) },
        { "GameUpdateConfigT", typeof(FBConfig.GameUpdateConfigT) },
        { "GenericPromoSettingsT", typeof(FBConfig.GenericPromoSettingsT) },
        { "GiantPinataOutcomeConfig", typeof(FBConfig.GiantPinataOutcomeConfig) },
        { "GiantPinataOutcomeConfigDictT", typeof(FBConfig.GiantPinataOutcomeConfigDictT) },
        { "GiantPinataOutcomeConfigT", typeof(FBConfig.GiantPinataOutcomeConfigT) },
        { "GiftPacksConfig", typeof(PBConfig.GiftPacksConfig) },
        { "GiftsConfig", typeof(FBConfig.GiftsConfig) },
        { "GiftsConfigDictT", typeof(FBConfig.GiftsConfigDictT) },
        { "GiftsConfigT", typeof(FBConfig.GiftsConfigT) },
        { "GoalScoreConfig", typeof(FBConfig.GoalScoreConfig) },
        { "GoalScoreConfigDictT", typeof(FBConfig.GoalScoreConfigDictT) },
        { "GoalScoreConfigT", typeof(FBConfig.GoalScoreConfigT) },
        { "HelpingHandsConfig", typeof(FBConfig.HelpingHandsConfig) },
        { "HelpingHandsConfigDictT", typeof(FBConfig.HelpingHandsConfigDictT) },
        { "HelpingHandsConfigT", typeof(FBConfig.HelpingHandsConfigT) },
        { "HelpLevelValuesT", typeof(FBConfig.HelpLevelValuesT) },
        { "HintSystemConfig", typeof(FBConfig.HintSystemConfig) },
        { "HintSystemConfigDictT", typeof(FBConfig.HintSystemConfigDictT) },
        { "HintSystemConfigT", typeof(FBConfig.HintSystemConfigT) },
        { "HouseConfig", typeof(PBConfig.HouseConfig) },
        { "HudAssetReferenceConfig", typeof(FBConfig.HudAssetReferenceConfig) },
        { "HudAssetReferenceConfigDictT", typeof(FBConfig.HudAssetReferenceConfigDictT) },
        { "HudAssetReferenceConfigT", typeof(FBConfig.HudAssetReferenceConfigT) },
        { "HudConfig", typeof(FBConfig.HudConfig) },
        { "HudConfigDictT", typeof(FBConfig.HudConfigDictT) },
        { "HudConfigT", typeof(FBConfig.HudConfigT) },
        { "HudSettingsT", typeof(FBConfig.HudSettingsT) },
        { "IAPBasketConfig", typeof(PBConfig.IAPBasketConfig) },
        { "IAPCategoryConfig", typeof(FBConfig.IAPCategoryConfig) },
        { "IAPCategoryConfigDictT", typeof(FBConfig.IAPCategoryConfigDictT) },
        { "IAPCategoryConfigT", typeof(FBConfig.IAPCategoryConfigT) },
        { "IAPPromotionConfig", typeof(PBConfig.IAPPromotionConfig) },
        { "IAPRewardOrderedT", typeof(FBConfig.IAPRewardOrderedT) },
        { "IAPRewardT", typeof(FBConfig.IAPRewardT) },
        { "IAPStoreCategoryConfig", typeof(FBConfig.IAPStoreCategoryConfig) },
        { "IAPStoreCategoryConfigDictT", typeof(FBConfig.IAPStoreCategoryConfigDictT) },
        { "IAPStoreCategoryConfigT", typeof(FBConfig.IAPStoreCategoryConfigT) },
        { "IAPStoreItemConfigT", typeof(FBConfig.IAPStoreItemConfigT) },
        { "IAPStoreMarketItemConfig", typeof(FBConfig.IAPStoreMarketItemConfig) },
        { "IAPStoreMarketItemConfigDictT", typeof(FBConfig.IAPStoreMarketItemConfigDictT) },
        { "IAPStoreMarketItemConfigT", typeof(FBConfig.IAPStoreMarketItemConfigT) },
        { "IAPStoreVirtualItemConfig", typeof(PBConfig.IAPStoreVirtualItemConfig) },
        { "IAPStoreVirtualItemPackConfig", typeof(FBConfig.IAPStoreVirtualItemPackConfig) },
        { "IAPStoreVirtualItemPackConfigDictT", typeof(FBConfig.IAPStoreVirtualItemPackConfigDictT) },
        { "IAPStoreVirtualItemPackConfigT", typeof(FBConfig.IAPStoreVirtualItemPackConfigT) },
        { "IAPStoreVirtualItemPackDescriptionT", typeof(FBConfig.IAPStoreVirtualItemPackDescriptionT) },
        { "IceBreakerConfig", typeof(FBConfig.IceBreakerConfig) },
        { "IceBreakerConfigDictT", typeof(FBConfig.IceBreakerConfigDictT) },
        { "IceBreakerConfigT", typeof(FBConfig.IceBreakerConfigT) },
        { "JWT", typeof(Assets.SimpleSignIn.Google.Scripts.JWT) },
        { "LandmarkConfig", typeof(PBConfig.LandmarkConfig) },
        { "LeadLevelConfig", typeof(FBConfig.LeadLevelConfig) },
        { "LeadLevelConfigDictT", typeof(FBConfig.LeadLevelConfigDictT) },
        { "LeadLevelConfigT", typeof(FBConfig.LeadLevelConfigT) },
        { "LeagueRatioT", typeof(FBConfig.LeagueRatioT) },
        { "LevelAdsConfig", typeof(PBConfig.LevelAdsConfig) },
        { "LevelAssistWeightsConfig", typeof(PBConfig.LevelAssistWeightsConfig) },
        { "LevelBundleConfig", typeof(FBConfig.LevelBundleConfig) },
        { "LevelBundleConfigDictT", typeof(FBConfig.LevelBundleConfigDictT) },
        { "LevelBundleConfigT", typeof(FBConfig.LevelBundleConfigT) },
        { "LevelConfig", typeof(FBConfig.LevelConfig) },
        { "LevelConfigDictT", typeof(FBConfig.LevelConfigDictT) },
        { "LevelConfigT", typeof(FBConfig.LevelConfigT) },
        { "LevelLinkT", typeof(FBConfig.LevelLinkT) },
        { "LevelNarrativeConfig", typeof(FBConfig.LevelNarrativeConfig) },
        { "LevelNarrativeConfigDictT", typeof(FBConfig.LevelNarrativeConfigDictT) },
        { "LevelNarrativeConfigT", typeof(FBConfig.LevelNarrativeConfigT) },
        { "LevelRewardT", typeof(FBConfig.LevelRewardT) },
        { "LevelViewConfig", typeof(FBConfig.LevelViewConfig) },
        { "LevelViewConfigDictT", typeof(FBConfig.LevelViewConfigDictT) },
        { "LevelViewConfigT", typeof(FBConfig.LevelViewConfigT) },
        { "ListStringT", typeof(FBConfig.ListStringT) },
        { "LivesConfig", typeof(FBConfig.LivesConfig) },
        { "LivesConfigDictT", typeof(FBConfig.LivesConfigDictT) },
        { "LivesConfigT", typeof(FBConfig.LivesConfigT) },
        { "LocalNotificationsConfig", typeof(FBConfig.LocalNotificationsConfig) },
        { "LocalNotificationsConfigDictT", typeof(FBConfig.LocalNotificationsConfigDictT) },
        { "LocalNotificationsConfigT", typeof(FBConfig.LocalNotificationsConfigT) },
        { "LocalPushNotificationsTimingConfig", typeof(FBConfig.LocalPushNotificationsTimingConfig) },
        { "LocalPushNotificationsTimingConfigDictT", typeof(FBConfig.LocalPushNotificationsTimingConfigDictT) },
        { "LocalPushNotificationsTimingConfigT", typeof(FBConfig.LocalPushNotificationsTimingConfigT) },
        { "LocationConfig", typeof(FBConfig.LocationConfig) },
        { "LocationConfigDictT", typeof(FBConfig.LocationConfigDictT) },
        { "LocationConfigT", typeof(FBConfig.LocationConfigT) },
        { "LocationsPanelSettingsT", typeof(FBConfig.LocationsPanelSettingsT) },
        { "LockItemConfig", typeof(FBConfig.LockItemConfig) },
        { "LockItemConfigDictT", typeof(FBConfig.LockItemConfigDictT) },
        { "LockItemConfigT", typeof(FBConfig.LockItemConfigT) },
        { "MapBackgroundMaskConfig", typeof(PBConfig.MapBackgroundMaskConfig) },
        { "MapPlaceableConfig", typeof(FBConfig.MapPlaceableConfig) },
        { "MapPlaceableConfigDictT", typeof(FBConfig.MapPlaceableConfigDictT) },
        { "MapPlaceableConfigT", typeof(FBConfig.MapPlaceableConfigT) },
        { "Match3AwardsConfig", typeof(FBConfig.Match3AwardsConfig) },
        { "Match3AwardsConfigDictT", typeof(FBConfig.Match3AwardsConfigDictT) },
        { "Match3AwardsConfigT", typeof(FBConfig.Match3AwardsConfigT) },
        { "Match3SpecialVoiceoversConfig", typeof(FBConfig.Match3SpecialVoiceoversConfig) },
        { "Match3SpecialVoiceoversConfigDictT", typeof(FBConfig.Match3SpecialVoiceoversConfigDictT) },
        { "Match3SpecialVoiceoversConfigT", typeof(FBConfig.Match3SpecialVoiceoversConfigT) },
        { "MechanicTargetingConfig", typeof(FBConfig.MechanicTargetingConfig) },
        { "MechanicTargetingConfigDictT", typeof(FBConfig.MechanicTargetingConfigDictT) },
        { "MechanicTargetingConfigT", typeof(FBConfig.MechanicTargetingConfigT) },
        { "MiscConfig", typeof(PBConfig.MiscConfig) },
        { "MiscObjectDecorationConfig", typeof(PBConfig.MiscObjectDecorationConfig) },
        { "ModalsConfigT", typeof(FBConfig.ModalsConfigT) },
        { "MoonAccessConfig", typeof(PBConfig.MoonAccessConfig) },
        { "MysteryBoxConfig", typeof(PBConfig.MysteryBoxConfig) },
        { "MysteryBoxMapConfig", typeof(PBConfig.MysteryBoxMapConfig) },
        { "NarrativeDialogConfig", typeof(PBConfig.NarrativeDialogConfig) },
        { "NewsConfig", typeof(FBConfig.NewsConfig) },
        { "NewsConfigDictT", typeof(FBConfig.NewsConfigDictT) },
        { "NewsConfigT", typeof(FBConfig.NewsConfigT) },
        { "NotificationsFilterConfig", typeof(PBConfig.NotificationsFilterConfig) },
        { "NotificationsMetaConfig", typeof(PBConfig.NotificationsMetaConfig) },
        { "ObjectiveActionT", typeof(FBConfig.ObjectiveActionT) },
        { "ObjectiveRewardT", typeof(FBConfig.ObjectiveRewardT) },
        { "OfferConfig", typeof(FBConfig.OfferConfig) },
        { "OfferConfigDictT", typeof(FBConfig.OfferConfigDictT) },
        { "OfferConfigT", typeof(FBConfig.OfferConfigT) },
        { "OurGamesConfig", typeof(PBConfig.OurGamesConfig) },
        { "OurNewsConfig", typeof(PBConfig.OurNewsConfig) },
        { "PackModalVisualOverrideConfig", typeof(PBConfig.PackModalVisualOverrideConfig) },
        { "PassportConfig", typeof(PBConfig.PassportConfig) },
        { "PatchDecorationConfig", typeof(PBConfig.PatchDecorationConfig) },
        { "PBConfig", typeof(PBConfig.PBConfig) },
        { "PeopleConfig", typeof(PBConfig.PeopleConfig) },
        { "PlayerSkillConfig", typeof(FBConfig.PlayerSkillConfig) },
        { "PlayerSkillConfigDictT", typeof(FBConfig.PlayerSkillConfigDictT) },
        { "PlayerSkillConfigT", typeof(FBConfig.PlayerSkillConfigT) },
        { "POICategoryConfig", typeof(FBConfig.POICategoryConfig) },
        { "POICategoryConfigDictT", typeof(FBConfig.POICategoryConfigDictT) },
        { "POICategoryConfigT", typeof(FBConfig.POICategoryConfigT) },
        { "POIContentConfig", typeof(FBConfig.POIContentConfig) },
        { "POIContentConfigDictT", typeof(FBConfig.POIContentConfigDictT) },
        { "POIContentConfigT", typeof(FBConfig.POIContentConfigT) },
        { "POIEntityConfig", typeof(FBConfig.POIEntityConfig) },
        { "POIEntityConfigDictT", typeof(FBConfig.POIEntityConfigDictT) },
        { "POIEntityConfigT", typeof(FBConfig.POIEntityConfigT) },
        { "POILocationConfig", typeof(FBConfig.POILocationConfig) },
        { "POILocationConfigDictT", typeof(FBConfig.POILocationConfigDictT) },
        { "POILocationConfigT", typeof(FBConfig.POILocationConfigT) },
        { "Point3T", typeof(FBConfig.Point3T) },
        { "PointT", typeof(FBConfig.PointT) },
        { "POITextConfig", typeof(FBConfig.POITextConfig) },
        { "POITextConfigDictT", typeof(FBConfig.POITextConfigDictT) },
        { "POITextConfigT", typeof(FBConfig.POITextConfigT) },
        { "PrebuildingConfig", typeof(PBConfig.PrebuildingConfig) },
        { "PriceT", typeof(FBConfig.PriceT) },
        { "PriorityConfigT", typeof(FBConfig.PriorityConfigT) },
        { "PrizeProbT", typeof(FBConfig.PrizeProbT) },
        { "ProductOptionsT", typeof(FBConfig.ProductOptionsT) },
        { "ProgressionLevelConfig", typeof(FBConfig.ProgressionLevelConfig) },
        { "ProgressionLevelConfigDictT", typeof(FBConfig.ProgressionLevelConfigDictT) },
        { "ProgressionLevelConfigT", typeof(FBConfig.ProgressionLevelConfigT) },
        { "PromoBannerSettingsT", typeof(FBConfig.PromoBannerSettingsT) },
        { "PromotionConfig", typeof(PBConfig.PromotionConfig) },
        { "PropellerTargetingConfig", typeof(PBConfig.PropellerTargetingConfig) },
        { "QuestCategoryConfig", typeof(PBConfig.QuestCategoryConfig) },
        { "QuestConfig", typeof(FBConfig.QuestConfig) },
        { "QuestConfigDictT", typeof(FBConfig.QuestConfigDictT) },
        { "QuestConfigT", typeof(FBConfig.QuestConfigT) },
        { "QuestObjectiveConfig", typeof(FBConfig.QuestObjectiveConfig) },
        { "QuestObjectiveConfigT", typeof(FBConfig.QuestObjectiveConfigT) },
        { "QuickActionsConfig", typeof(FBConfig.QuickActionsConfig) },
        { "QuickActionsConfigDictT", typeof(FBConfig.QuickActionsConfigDictT) },
        { "QuickActionsConfigT", typeof(FBConfig.QuickActionsConfigT) },
        { "RaceBotSpeedConfig", typeof(PBConfig.RaceBotSpeedConfig) },
        { "RaceGameEventConfig", typeof(PBConfig.RaceGameEventConfig) },
        { "RaceStageConfig", typeof(FBConfig.RaceStageConfig) },
        { "RaceStageConfigDictT", typeof(FBConfig.RaceStageConfigDictT) },
        { "RaceStageConfigT", typeof(FBConfig.RaceStageConfigT) },
        { "ResourceExchangeConfig", typeof(PBConfig.ResourceExchangeConfig) },
        { "ResourcesConfig", typeof(PBConfig.ResourcesConfig) },
        { "ResourcesLocationConfig", typeof(PBConfig.ResourcesLocationConfig) },
        { "RewardIconsT", typeof(FBConfig.RewardIconsT) },
        { "RewardResourceT", typeof(FBConfig.RewardResourceT) },
        { "RewardSettingsT", typeof(FBConfig.RewardSettingsT) },
        { "RoadConfig", typeof(PBConfig.RoadConfig) },
        { "RoyaleGameEventConfig", typeof(PBConfig.RoyaleGameEventConfig) },
        { "SaveGameProgressConfigT", typeof(FBConfig.SaveGameProgressConfigT) },
        { "ScenesConfig", typeof(FBConfig.ScenesConfig) },
        { "ScenesConfigDictT", typeof(FBConfig.ScenesConfigDictT) },
        { "ScenesConfigT", typeof(FBConfig.ScenesConfigT) },
        { "SceneTaskConfig", typeof(FBConfig.SceneTaskConfig) },
        { "SceneTaskConfigDictT", typeof(FBConfig.SceneTaskConfigDictT) },
        { "SceneTaskConfigT", typeof(FBConfig.SceneTaskConfigT) },
        { "ScoreMultipliersT", typeof(FBConfig.ScoreMultipliersT) },
        { "ScreenConfig", typeof(FBConfig.ScreenConfig) },
        { "ScreenConfigDictT", typeof(FBConfig.ScreenConfigDictT) },
        { "ScreenConfigT", typeof(FBConfig.ScreenConfigT) },
        { "SdbConfig", typeof(FBConfig.SdbConfig) },
        { "SdbConfigDictT", typeof(FBConfig.SdbConfigDictT) },
        { "SdbConfigT", typeof(FBConfig.SdbConfigT) },
        { "ShopConfig", typeof(PBConfig.ShopConfig) },
        { "SlotMachineOutcomeConfig", typeof(FBConfig.SlotMachineOutcomeConfig) },
        { "SlotMachineOutcomeConfigDictT", typeof(FBConfig.SlotMachineOutcomeConfigDictT) },
        { "SlotMachineOutcomeConfigT", typeof(FBConfig.SlotMachineOutcomeConfigT) },
        { "SocialConfig", typeof(FBConfig.SocialConfig) },
        { "SocialConfigDictT", typeof(FBConfig.SocialConfigDictT) },
        { "SocialConfigT", typeof(FBConfig.SocialConfigT) },
        { "SpawnConfig", typeof(PBConfig.SpawnConfig) },
        { "SpawnDecorationConfig", typeof(PBConfig.SpawnDecorationConfig) },
        { "SpriteAtlasIndexConfig", typeof(FBConfig.SpriteAtlasIndexConfig) },
        { "SpriteAtlasIndexConfigDictT", typeof(FBConfig.SpriteAtlasIndexConfigDictT) },
        { "SpriteAtlasIndexConfigT", typeof(FBConfig.SpriteAtlasIndexConfigT) },
        { "StealsConfig", typeof(PBConfig.StealsConfig) },
        { "SuperBoostConfig", typeof(FBConfig.SuperBoostConfig) },
        { "SuperBoostConfigDictT", typeof(FBConfig.SuperBoostConfigDictT) },
        { "SuperBoostConfigT", typeof(FBConfig.SuperBoostConfigT) },
        { "SuperBoostPowerupsConfig", typeof(PBConfig.SuperBoostPowerupsConfig) },
        { "SuperBoostProgressConfig", typeof(FBConfig.SuperBoostProgressConfig) },
        { "SuperBoostProgressConfigDictT", typeof(FBConfig.SuperBoostProgressConfigDictT) },
        { "SuperBoostProgressConfigT", typeof(FBConfig.SuperBoostProgressConfigT) },
        { "SweepstakesDailyLoginConfig", typeof(FBConfig.SweepstakesDailyLoginConfig) },
        { "SweepstakesDailyLoginConfigDictT", typeof(FBConfig.SweepstakesDailyLoginConfigDictT) },
        { "SweepstakesDailyLoginConfigT", typeof(FBConfig.SweepstakesDailyLoginConfigT) },
        { "SweepStakesGameEventConfig", typeof(FBConfig.SweepStakesGameEventConfig) },
        { "SweepStakesGameEventConfigDictT", typeof(FBConfig.SweepStakesGameEventConfigDictT) },
        { "SweepStakesGameEventConfigT", typeof(FBConfig.SweepStakesGameEventConfigT) },
        { "SweepstakesVideoConfig", typeof(FBConfig.SweepstakesVideoConfig) },
        { "SweepstakesVideoConfigDictT", typeof(FBConfig.SweepstakesVideoConfigDictT) },
        { "SweepstakesVideoConfigT", typeof(FBConfig.SweepstakesVideoConfigT) },
        { "SystemConfig", typeof(FBConfig.SystemConfig) },
        { "SystemConfigDictT", typeof(FBConfig.SystemConfigDictT) },
        { "SystemConfigT", typeof(FBConfig.SystemConfigT) },
        { "TeamCoopMilestoneConfigT", typeof(FBConfig.TeamCoopMilestoneConfigT) },
        { "TeamEventConfig", typeof(FBConfig.TeamEventConfig) },
        { "TeamEventConfigDictT", typeof(FBConfig.TeamEventConfigDictT) },
        { "TeamEventConfigT", typeof(FBConfig.TeamEventConfigT) },
        { "TextConfig", typeof(FBConfig.TextConfig) },
        { "TextConfigDictT", typeof(FBConfig.TextConfigDictT) },
        { "TextConfigT", typeof(FBConfig.TextConfigT) },
        { "ThemeConfig", typeof(FBConfig.ThemeConfig) },
        { "ThemeConfigDictT", typeof(FBConfig.ThemeConfigDictT) },
        { "ThemeConfigT", typeof(FBConfig.ThemeConfigT) },
        { "ThemeMetaConfig", typeof(FBConfig.ThemeMetaConfig) },
        { "ThemeMetaConfigDictT", typeof(FBConfig.ThemeMetaConfigDictT) },
        { "ThemeMetaConfigT", typeof(FBConfig.ThemeMetaConfigT) },
        { "TileStateHpScoreT", typeof(FBConfig.TileStateHpScoreT) },
        { "TileStateScoreT", typeof(FBConfig.TileStateScoreT) },
        { "TipJarConfig", typeof(PBConfig.TipJarConfig) },
        { "TradingCardConfig", typeof(FBConfig.TradingCardConfig) },
        { "TradingCardConfigDictT", typeof(FBConfig.TradingCardConfigDictT) },
        { "TradingCardConfigT", typeof(FBConfig.TradingCardConfigT) },
        { "TransitionTipsConfig", typeof(FBConfig.TransitionTipsConfig) },
        { "TransitionTipsConfigDictT", typeof(FBConfig.TransitionTipsConfigDictT) },
        { "TransitionTipsConfigT", typeof(FBConfig.TransitionTipsConfigT) },
        { "TutorialConfig", typeof(FBConfig.TutorialConfig) },
        { "TutorialConfigDictT", typeof(FBConfig.TutorialConfigDictT) },
        { "TutorialConfigT", typeof(FBConfig.TutorialConfigT) },
        { "UnifiedPromotionConfig", typeof(FBConfig.UnifiedPromotionConfig) },
        { "UnifiedPromotionConfigDictT", typeof(FBConfig.UnifiedPromotionConfigDictT) },
        { "UnifiedPromotionConfigT", typeof(FBConfig.UnifiedPromotionConfigT) },
        { "VideoCaptionConfig", typeof(PBConfig.VideoCaptionConfig) },
        { "VIPProductsConfg", typeof(FBConfig.VIPProductsConfg) },
        { "VIPProductsConfgDictT", typeof(FBConfig.VIPProductsConfgDictT) },
        { "VIPProductsConfgT", typeof(FBConfig.VIPProductsConfgT) },
        { "VIPProductsMetaConfg", typeof(FBConfig.VIPProductsMetaConfg) },
        { "VIPProductsMetaConfgDictT", typeof(FBConfig.VIPProductsMetaConfgDictT) },
        { "VIPProductsMetaConfgT", typeof(FBConfig.VIPProductsMetaConfgT) },
        { "VisitCollectConfig", typeof(PBConfig.VisitCollectConfig) },
        { "WeekendTourScheduleConfig", typeof(PBConfig.WeekendTourScheduleConfig) },
        { "WeeklyLeaderboardConfig", typeof(FBConfig.WeeklyLeaderboardConfig) },
        { "WeeklyLeaderboardConfigDictT", typeof(FBConfig.WeeklyLeaderboardConfigDictT) },
        { "WeeklyLeaderboardConfigT", typeof(FBConfig.WeeklyLeaderboardConfigT) },
        { "WonderConfig", typeof(PBConfig.WonderConfig) },
        { "WorldCityGlobalSettingsConfig", typeof(PBConfig.WorldCityGlobalSettingsConfig) },
    };
}
