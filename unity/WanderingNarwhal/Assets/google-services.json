{"project_info": {"project_number": "103904711944", "project_id": "travel-crush", "storage_bucket": "travel-crush.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:103904711944:android:ad293648188b86bdb9c8bd", "android_client_info": {"package_name": "com.bebopbee.match3.travelcrush"}}, "oauth_client": [{"client_id": "103904711944-1d6amnap1g098n4bpfd721ue4468lcb4.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.bebopbee.match3.travelcrush", "certificate_hash": "ba08b1b48fef9891db88bfca850d18c92a750750"}}, {"client_id": "103904711944-190tfmorcf171qtqhbo51mc265dg1qe5.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDFaqQov59hiX4nic7D2s-SMy9_ll50sQk"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "103904711944-190tfmorcf171qtqhbo51mc265dg1qe5.apps.googleusercontent.com", "client_type": 3}, {"client_id": "103904711944-8stk6g9rls2b6mc0rh25ipt4l5c1oqel.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "travelcrush.oauth"}}]}}}], "configuration_version": "1"}