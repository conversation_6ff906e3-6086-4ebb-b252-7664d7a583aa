using System.Collections.Generic;
using System.Linq;
using UnityEditor;

namespace CleanupUtility.Editor
{
    public class AssetCollection
    {
        private readonly string _name;
        private readonly List<string> _folderPaths;
        private readonly List<string> _assetPaths = new List<string>();
        private const string StringFormat = "Asset {0} of {1} collection has dependency to asset {2}";
        //private const string StringFormat = "{0}^{1}^{2}";
        
        public AssetCollection(string name, IEnumerable<string> folderPaths)
        {
            _name = name;
            _folderPaths = folderPaths.ToList();
        }

        public void TryAddAssetAtPath(string assetPath)
        {
            if (_folderPaths.Any(assetPath.StartsWith))
            {
                var type = AssetDatabase.GetMainAssetTypeAtPath(assetPath);
                
                if (type == null || InsularityTesterConfiguration.ExcludedAssetTypes.Contains(type))
                    return;
                
                _assetPaths.Add(assetPath);
            }
        }

        public void CheckForInsularity(HashSet<string> issuesList, AssetCollection allowedOccuranceCollection = null)
        {
            int index = 0;
            foreach (var loadedAssetPath in _assetPaths)
            {
                EditorUtility.DisplayProgressBar("Insularity test", string.Format("Checking {0}", loadedAssetPath), (float)index/_assetPaths.Count);
                
                var dependenciesPaths = AssetDatabase.GetDependencies(loadedAssetPath);
                foreach (var dependencyPath in dependenciesPaths)
                {
                    var type = AssetDatabase.GetMainAssetTypeAtPath(dependencyPath);
                    if (type == null || InsularityTesterConfiguration.ExcludedAssetTypes.Contains(type))
                    {
                        index++;
                        continue;
                    }
                      
                   
                    if (!BelongsToMe(dependencyPath))
                    {
                        if (allowedOccuranceCollection != null &&
                            allowedOccuranceCollection.BelongsToMe(dependencyPath))
                        {
                            index++;
                            continue;
                        }


                        var issueStr = string.Format(StringFormat, loadedAssetPath, _name, dependencyPath);
                        issuesList.Add(issueStr);
                    }
                }

                index++;
            }
            
            EditorUtility.ClearProgressBar();
        }

        private bool BelongsToMe(string assetPath)
        {
            return _folderPaths.Any(assetPath.StartsWith);
        }
    }
}