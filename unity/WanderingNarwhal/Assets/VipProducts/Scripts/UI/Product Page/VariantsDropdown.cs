using System.Collections.Generic;
using BBB;
using UnityEngine;
using UnityEngine.UI;

namespace VipProducts.UI
{
    public class VariantsDropdown : BbbMonoBehaviour
    {
        public List<string> Options;
        public string OptionName;

        public List<VariantsDropdown> AllDropdowns { get; private set; }

        public Text OptionText;
        public Text OptionNameText;
        private string _selectedOption;

        public string SelectedOption
        {
            get => _selectedOption;
            set
            {
                _selectedOption = value;
                Alternate?.ForEach(x => x.OptionText.SetTextId(_selectedOption));
            }
        }

        public List<AlternateVariantsDropdown> Alternate;

        private AlternateVariantsDropdown _bigScreenAlternate;

        public AlternateVariantsDropdown BigScreenAlternate
        {
            get
            {
                if (_bigScreenAlternate != null) 
                    return _bigScreenAlternate;
                
                foreach (var item in Alternate)
                {
                    if (item.selectionManagerBigScreen == null) continue;
                    
                    return _bigScreenAlternate = item;
                }

                return null;
            }
        }

        public void Init(List<string> options, string optionName, List<VariantsDropdown> allDropdowns)
        {
            Options = options;
            OptionName = optionName;
            AllDropdowns = allDropdowns;
            SelectedOption = options.Count > 0 ? options[0] : null;
            OptionText.text = SelectedOption;
            if (optionName != null) OptionNameText.text = optionName.ToUpper();
            Alternate?.ForEach(x => x.OptionNameText.SetTextId(OptionNameText.text));
            Canvas.ForceUpdateCanvases(); // Resize the selector buttons to proper size
        }
    }
}