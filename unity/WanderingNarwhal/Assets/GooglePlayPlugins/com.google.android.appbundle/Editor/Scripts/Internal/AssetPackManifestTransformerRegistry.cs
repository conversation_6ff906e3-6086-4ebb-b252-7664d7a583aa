// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

using Google.Android.AppBundle.Editor.Internal.AndroidManifest;

namespace Google.Android.AppBundle.Editor.Internal
{
    /// <summary>
    /// Provides a <see cref="Registry"/> of <see cref="IAssetPackManifestTransformer"/>s.
    /// </summary>
    public class AssetPackManifestTransformerRegistry : Registry<IAssetPackManifestTransformer>
    {
        private static AssetPackManifestTransformerRegistry _instance;

        public static AssetPackManifestTransformerRegistry Registry
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new AssetPackManifestTransformerRegistry();
                }

                return _instance;
            }
        }
    }
}
