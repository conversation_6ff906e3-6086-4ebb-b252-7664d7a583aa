using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;

public class ButtonPositionEditor : EditorWindow
{
    private string _jsonInput = "";

    [Serializable]
    public class ButtonData
    {
        public float x;
        public float y;
    }

    [MenuItem("Tools/Button Position Editor")]
    public static void ShowWindow()
    {
        GetWindow<ButtonPositionEditor>("Button Position Editor");
    }

    void OnGUI()
    {
        GUILayout.Label("Paste JSON Here:", EditorStyles.boldLabel);
        _jsonInput = EditorGUILayout.TextArea(_jsonInput, GUILayout.Height(100));

        if (GUILayout.Button("Apply Positions"))
        {
            ApplyJsonPositions();
        }
    }

    void ApplyJsonPositions()
    {
        try
        {
            var data = JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, ButtonData>>>(_jsonInput)["buttons"];

            if (data == null)
            {
                Debug.LogError("Invalid JSON format.");
                return;
            }
            var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
            if (prefabStage == null)
            {
                Debug.LogWarning("No prefab is open in Prefab Mode.");
                return;
            }

            // Get the root GameObject of the prefab
            var rootPrefab = prefabStage.prefabContentsRoot;
            if (rootPrefab == null)
            {
                Debug.LogWarning("Failed to retrieve prefab root.");
                return;
            }

            const int sceneOffset = 4000;
            foreach (var buttonEntry in data)
            {
                var buttonName = buttonEntry.Key;
                var buttonData = buttonEntry.Value;
            
                var buttonTransform = FindDeepChild(rootPrefab.transform, buttonName);
                if (buttonTransform)
                {
                    Undo.RecordObject(buttonTransform, "Move Button");
                    buttonTransform.localPosition =
                        new Vector3(buttonData.x + sceneOffset - buttonTransform.parent.RectTransform().anchoredPosition.x, buttonData.y, buttonTransform.localPosition.z);
                    EditorUtility.SetDirty(buttonTransform);
                }
                else
                {
                    Debug.LogWarning($"Button '{buttonName}' not found in the prefab!");
                }
            }

            Debug.Log("Button positions updated successfully!");
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to parse JSON: {e.Message}");
        }
    }
    
    private Transform FindDeepChild(Transform parent, string name)
    {
        foreach (Transform child in parent)
        {
            if (child.name == name) return child;
            var result = FindDeepChild(child, name);
            if (result != null) return result;
        }
        return null;
    }
}