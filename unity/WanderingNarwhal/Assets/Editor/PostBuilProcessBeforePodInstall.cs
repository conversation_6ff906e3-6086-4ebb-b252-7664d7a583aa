
#if UNITY_IOS
using System;
using UnityEditor.iOS.Xcode.Extensions;
using UnityEngine;
using UnityEditor;
using UnityEditor.Callbacks;
using System.IO;
using System.Text.RegularExpressions;
using UnityEditor.iOS.Xcode;

#endif

public class PostBuilProcessBeforePodInstall
{
#if UNITY_IOS
    private const string InfoPlistFileRelativePath = "Info.plist";
    private const string NotificationServiceName = "NotificationService";
    // regex patterns used for finding specific parts of data from a provisioning profile
    private const string PatternUUID = "<key>UUID<\\/key>[\n\t]*<string>((\\w*\\-?){5})";
    
    [PostProcessBuild(45)]
    public static void OnPostprocessBuild(BuildTarget target, string buildPath)
    {
        if (target == BuildTarget.iOS)
        {
            Debug.Log("[EDITOR_BUILD] PostBuildNotificationServiceProcessor");
            // UpdateProvisioningProfile(buildPath);
            // AddServiceExtension(buildPath);
            // UpdateServiceInfoPlist(buildPath);
            UpdateFirebaseGoogleServicesPlistFile(buildPath);
            FixXcode15(buildPath);
            UpdatePodFile(buildPath);
            SetupFirebaseMessaging(buildPath);
        }
    }

    private static void UpdateProvisioningProfile(string buildPath)
    {
        
        string projPath = buildPath + "/Unity-iPhone.xcodeproj/project.pbxproj";
        PBXProject proj = new PBXProject ();
        proj.ReadFromFile (projPath);

        if (!PlayerSettings.iOS.appleEnableAutomaticSigning)
        {
            SetProvisioningProfileBuildProperty(proj, proj.GetUnityMainTargetGuid(), "APP_PROV_PATH",
                "Assets/UnityCloudBuild/Redlion_Adhoc.mobileprovision");
        }
    }

    private static void SetProvisioningProfileBuildProperty(PBXProject proj, string targetGuid,
        string environmentVariable, string defaultPath)
    {
        var provisioningProfilePath = Environment.GetEnvironmentVariable(environmentVariable);
        if (String.IsNullOrEmpty(provisioningProfilePath))
        {
            provisioningProfilePath = defaultPath;
        }

        var provisioningFileContents = File.ReadAllText(provisioningProfilePath);
        Match matchUUID = Regex.Match(provisioningFileContents, PatternUUID, RegexOptions.Singleline);
        if (matchUUID.Success)
        {
            proj.SetBuildProperty(targetGuid, "PROVISIONING_PROFILE", matchUUID.Groups[1].Value);
        }
    }

    private static void UpdateFirebaseGoogleServicesPlistFile(string buildPath)
    {
        // Go get pbxproj file
        string projPath = buildPath + "/Unity-iPhone.xcodeproj/project.pbxproj";
  
        // PBXProject class represents a project build settings file,
        // here is how to read that in.
        PBXProject proj = new PBXProject ();
        proj.ReadFromFile (projPath);
  
        // Copy plist from the project folder to the build folder
        proj.AddFileToBuild (proj.GetUnityMainTargetGuid(), proj.AddFile("GoogleService-Info.plist", "GoogleService-Info.plist"));
  
        // Write PBXProject object back to the file
        proj.WriteToFile (projPath);
    }

    private static void AddServiceExtension(string buildPath)
    {
        var srcfile = "../iOSFiles/NotificationService";
        var projPath = buildPath + "/Unity-iPhone.xcodeproj/project.pbxproj";
        var destFolder = Path.Combine(buildPath, NotificationServiceName);
        var proj = new PBXProject();
        proj.ReadFromFile(projPath);

        var target = proj.GetUnityMainTargetGuid();

        var extensionGUID = proj.AddAppExtension(target, NotificationServiceName, PlayerSettings.GetApplicationIdentifier(BuildTargetGroup.iOS) + "." + NotificationServiceName.ToLower(),
            NotificationServiceName + "/" + "Info.plist");
        
        
        var folderPath = Path.Combine(Application.dataPath, srcfile);
        Directory.CreateDirectory(destFolder);
        
        var buildPhaseId = proj.AddSourcesBuildPhase(extensionGUID);
        var files = Directory.GetFiles(folderPath, "*", SearchOption.TopDirectoryOnly);
        foreach (var file in files)
        {
            var filename = Path.GetFileName(file);
            var nativeFileRelativeDestination = NotificationServiceName + "/" + filename;
            FileUtil.CopyFileOrDirectory(file, Path.Combine(destFolder, filename));
            var sourceFileGuid = proj.AddFile(nativeFileRelativeDestination, nativeFileRelativeDestination);
            if (file.EndsWith(".h") || file.EndsWith(".m"))
                proj.AddFileToBuildSection(extensionGUID, buildPhaseId, sourceFileGuid);
            if (file.EndsWith(".m"))
            {
                var flags = proj.GetCompileFlagsForFile(extensionGUID, sourceFileGuid);
                flags.Add("-fobjc-arc");
                proj.SetCompileFlagsForFile(extensionGUID, sourceFileGuid, flags);
            }
        }

        proj.AddFrameworkToProject(extensionGUID, "UserNotifications.framework", true);
        proj.AddFrameworkToProject(extensionGUID, "NotificationCenter.framework", true);

        proj.SetBuildProperty(extensionGUID, "TARGETED_DEVICE_FAMILY", "1,2");
        proj.SetBuildProperty(extensionGUID, "IPHONEOS_DEPLOYMENT_TARGET", "13.0");

        proj.SetBuildProperty(extensionGUID, "ARCHS", "arm64");
        proj.SetBuildProperty(extensionGUID, "DEVELOPMENT_TEAM", PlayerSettings.iOS.appleDeveloperTeamID);

        if (!PlayerSettings.iOS.appleEnableAutomaticSigning)
        {
            proj.SetBuildProperty(extensionGUID, "CODE_SIGN_STYLE", "Manual");
            proj.SetBuildProperty(extensionGUID, "CODE_SIGN_IDENTITY", "iPhone Distribution");
            SetProvisioningProfileBuildProperty(proj, extensionGUID, "NOTIF_PROV_PATH", "Assets/UnityCloudBuild/Redlion_Notification_Adhoc.mobileprovision");
        }
        
        proj.SetBuildProperty(target, "ARCHS", "arm64");
        proj.WriteToFile(projPath);
    }

    private static void UpdateServiceInfoPlist(string buildPath)
    {
        var folderPath = Path.Combine(buildPath, "NotificationService");

        var infoPlistPath = Path.Combine(folderPath, InfoPlistFileRelativePath);
        var plist = new PlistDocument();
        plist.ReadFromFile(infoPlistPath);

        // Get root
        var rootDict = plist.root;

        rootDict.SetString("CFBundleShortVersionString", PlayerSettings.bundleVersion);
        rootDict.SetString("CFBundleVersion", PlayerSettings.iOS.buildNumber.ToString());
        // Save
        plist.WriteToFile(infoPlistPath);
    }
    
    private static void FixXcode15(string buildPath)
    {
        // https://github.com/firebase/firebase-ios-sdk/issues/11840
        var srcfile = "../iOSFiles/";
        var folderPath = Path.Combine(Application.dataPath, srcfile);
        var sourceFixBashFilePath = folderPath + "/fix-build-for-xcode15.sh";
        var fixBashFilePath = buildPath + "/fix-build-for-xcode15.sh";
        if (!File.Exists(fixBashFilePath))
        {
            FileUtil.CopyFileOrDirectory(sourceFixBashFilePath, fixBashFilePath);
        }
    }

    private static void UpdatePodFile(string buildPath)
    {
        var content = File.ReadAllText(buildPath + "/Podfile");
        content = content.Replace("target 'UnityFramework' do", "target 'UnityFramework' do\n  use_frameworks!");
        File.WriteAllText(buildPath + "/Podfile", content);
        using (var sw = File.AppendText(buildPath + "/Podfile"))
        {
            // BWJSONMatcher Dependency has DEPLOYMENT_TARGET=8 by Default, and that doesn't work with Xcode 14.3+
            /*
             *
             *  post_install do |installer|
	                installer.pods_project.targets.each do |target|
		                target.build_configurations.each do |config| 
			                config.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = ""
			                config.build_settings['CODE_SIGNING_REQUIRED'] = "NO"
			                config.build_settings['CODE_SIGNING_ALLOWED'] = "NO"
			                config.build_settings['ENABLE_BITCODE'] = "NO"
			                config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
		                end
	                end
	                installer.generated_projects.each do |project|
			                project.targets.each do |target|
				                if target.name == 'BWJSONMatcher'
					                target.build_configurations.each do |config|
						                config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
					                end
				                end
				                target.build_configurations.each do |config| 
                                    config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
                                end
			                end
		                end
                end
             */
            sw.WriteLine($"\npost_install do |installer|\n\tsystem('sh fix-build-for-xcode15.sh')\n\tinstaller.pods_project.targets.each do |target|\n\t\ttarget.build_configurations.each do |config| \n\t\t\tconfig.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = \"\"\n\t\t\tconfig.build_settings['CODE_SIGNING_REQUIRED'] = \"NO\"\n\t\t\tconfig.build_settings['CODE_SIGNING_ALLOWED'] = \"NO\"\n\t\t\tconfig.build_settings['ENABLE_BITCODE'] = \"NO\"\n\t\t\tconfig.build_settings[\"EXCLUDED_ARCHS[sdk=iphonesimulator*]\"] = \"arm64\"\n\t\tend\n\tend\n\tinstaller.generated_projects.each do |project|\n\t\t\tproject.targets.each do |target|\n\t\t\t\tif target.name == 'BWJSONMatcher'\n\t\t\t\t\ttarget.build_configurations.each do |config|\n\t\t\t\t\t\tconfig.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'\n\t\t\t\t\tend\n\t\t\t\tend\n\t\t\ttarget.build_configurations.each do |config|\n\t\t\t\tconfig.build_settings[\"EXCLUDED_ARCHS[sdk=iphonesimulator*]\"] = \"arm64\"\n\t\t\tend\n\t\t\tend\n\t\tend\nend");
            
            // sw.WriteLine($"\ntarget '{NotificationServiceName}' do\n  use_frameworks!\n  pod 'Firebase/Messaging', '9.3.0'\n  pod 'Firebase/Core', '9.3.0'\nend");
        }
    }

    private static void SetupFirebaseMessaging(string buildPath)
    {
        var infoPlistPath = Path.Combine(buildPath, InfoPlistFileRelativePath);
        var plist = new PlistDocument();
        plist.ReadFromFile(infoPlistPath);

        // Get root
        var rootDict = plist.root;
        rootDict.SetBoolean("FirebaseMessagingAutoInitEnabled", false);

        // Save
        plist.WriteToFile(infoPlistPath);
    }
#endif
}