using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEditor;

public class ParticleSystemCullingModeFileSetter
{
    private const string DefaultProperty = "cullingMode:";
    private const string OverrideProperty = "propertyPath: cullingMode";

    private const int CullingAutoValue = (int)ParticleSystemCullingMode.Automatic;
    private const int CullingPauseValue = (int)ParticleSystemCullingMode.Pause;
    
    [MenuItem("BebopBee/Tools/Particles/Set CullingMode to Automatic (Via Files)")]
    public static void SetParticleCullingModeInFiles()
    {
        Debug.Log("Starting updating ParticleSystem's culling modes in prefabs and scenes.");

        var guids = new List<string>();
        guids.AddRange(AssetDatabase.FindAssets("t:Prefab"));
        
        var scenes = EditorBuildSettings.scenes;
        foreach (var scene in scenes)
        {
            if (!scene.enabled) continue;
            guids.Add(scene.guid.ToString());
        }

        AssetDatabase.StartAssetEditing();
        foreach (var guid in guids)
        {
            var path = AssetDatabase.GUIDToAssetPath(guid);
            ProcessFile(path);
        }
        AssetDatabase.StopAssetEditing();
        AssetDatabase.Refresh();
        
        Debug.Log("Completed updating ParticleSystem's culling modes in prefabs and scenes.");
    }
    
    private static void ProcessFile(string filePath)
    {
        var lines = File.ReadAllLines(filePath);
        var modified = false;

        for (var i = 0; i < lines.Length; i++)
        {
            var line = lines[i];
            var trimmedLine = line.TrimStart();
            if (trimmedLine.StartsWith(DefaultProperty))
            {
                if (!TryGetChangedLine(line, out var newLine)) continue;
                
                lines[i] = newLine;
                modified = true;
                continue;
            }

            if (trimmedLine.StartsWith(OverrideProperty))
            {
                i++;
                if (!TryGetChangedLine(lines[i], out var newLine)) continue;
                
                lines[i] = newLine;
                modified = true;
            }
        }

        if (!modified) return;
        
        File.WriteAllLines(filePath, lines);
        Debug.Log($"Modified: {filePath}");
    }

    private static bool TryGetChangedLine(string line, out string newLine)
    {
        newLine = line;
        var propertyParts = line.Split(":");
        if (propertyParts.Length != 2) return false;
        
        var oldValue = int.Parse(propertyParts[1].Trim());
        if (oldValue is CullingAutoValue or CullingPauseValue) return false;
                
        newLine = $"{propertyParts[0]}: {CullingAutoValue}";
        return true;
    }
}
