using BBB.Match3.Renderer;
using UnityEditor;

[CustomEditor(typeof(TileAnimator))]
public class TileControllerDebugInstpector : Editor
{
    private TileAnimator Controller { get { return target as TileAnimator; } }

    private float _rotateZ;
    private float _rotateSubtleZ;

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        //GUILayout.BeginHorizontal();
        //_rotateZ = EditorGUILayout.FloatField(_rotateZ);
        //if(GUILayout.Button("RotateTile"))
        //    Controller.RotateTile(_rotateZ);
        //GUILayout.EndHorizontal();

        //GUILayout.BeginHorizontal();
        //_rotateSubtleZ = EditorGUILayout.FloatField(_rotateSubtleZ);
        //if (GUILayout.Button("RotateForSubtleTile"))
        //    Controller.RotateForSubtleTile(_rotateSubtleZ);
        //GUILayout.EndHorizontal();

        //GUILayout.BeginHorizontal();
        //_flipX = EditorGUILayout.FloatField(_flipX);
        //if (GUILayout.Button("FlipTile"))
        //    Controller.FlipTile(_flipX);
        //GUILayout.EndHorizontal();
    }
}
