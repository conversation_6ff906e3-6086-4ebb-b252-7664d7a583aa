using System.IO;
using Amazon;
using Amazon.S3;
using Amazon.S3.Transfer;
using BebopBee;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
using UnityEngine;

namespace BBB
{
    public class DebugSymbolsPostProcess : IPostprocessBuildWithReport
    {
        public int callbackOrder => 100;
        private static string CurrentCommit = "";
        
#if UNITY_CLOUD_BUILD
        public static void PreExport(UnityEngine.CloudBuild.BuildManifestObject manifest)
        {
            var commit = manifest.GetValue("scmCommitId", "");
            CurrentCommit = commit;
        }
#endif
        public void OnPostprocessBuild(BuildReport report)
        {
            Debug.Log($"[OnPostprocessBuild] DebugSymbolsPostProcess");
            if (!(AppDefinesConverter.UnityAndroid && AppDefinesConverter.UnityCloudBuild))
                return;
            var fileToUpload = "";
            foreach (var file in report.GetFiles())
            {
                if (file.role == "Symbols")
                {
                    fileToUpload = file.path;
                    break;
                }
            }

            if (!fileToUpload.IsNullOrEmpty())
            {
                var bucketRegion = RegionEndpoint.USWest2;
                var s3Client = new AmazonS3Client("********************", "huhBOn0h/qFLG/j29KxJsgX5ZxWvxv+Iu9wtmyzd", bucketRegion);
                var fileTransferUtility = new TransferUtility(s3Client);
                
                var filePath = $"{Application.dataPath}/google-services.json";
                var key = $"Android/{CurrentCommit}/google-services.json";
                var fileTransferUtilityRequest = new TransferUtilityUploadRequest
                {
                    BucketName = "ucb.builds.pnp.bebopbee.com",
                    FilePath = filePath,
                    Key = key
                };
                Debug.Log($"[OnPostprocessBuild] Uploading Android info to: {fileTransferUtilityRequest.BucketName}/{fileTransferUtilityRequest.Key} from: {fileTransferUtilityRequest.FilePath}" );
                fileTransferUtility.Upload(fileTransferUtilityRequest);
                
                key = $"Android/{CurrentCommit}/{CurrentBundleVersion.GetVersion()}-{CurrentBundleVersion.GetBuildCode()}-{CurrentCommit}_symbols.zip";
                fileTransferUtilityRequest = new TransferUtilityUploadRequest
                {
                    BucketName = "ucb.builds.pnp.bebopbee.com",
                    FilePath = fileToUpload,
                    Key = key
                };
                Debug.Log($"[OnPostprocessBuild] Uploading Symbols to: {fileTransferUtilityRequest.BucketName}/{fileTransferUtilityRequest.Key} from: {fileTransferUtilityRequest.FilePath}" );
                fileTransferUtility.Upload(fileTransferUtilityRequest);
            }
            else
            {
                Debug.Log($"[OnPostprocessBuild] No Symbols generated");
            }
        }
    }
}