using GameAssets.Scripts.Transitions;
using Unity.EditorCoroutines.Editor;
using UnityEditor;
using UnityEngine;

namespace Transitions
{
    [CustomEditor(typeof(FlipTextView))]
    public class FlipTextEditor : UnityEditor.Editor
    {
        private string _fromText = "Paris";
        private string _toText = "Cairo";
        private EditorCoroutine _editorCoroutine;
        
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            _fromText = EditorGUILayout.TextField("From Text", _fromText);
            _toText = EditorGUILayout.TextField("To Text", _toText);

            if (!_fromText.IsNullOrEmpty() && !_toText.IsNullOrEmpty() && GUILayout.Button("Apply"))
            {
                if (_editorCoroutine != null)
                {
                    EditorCoroutineUtility.StopCoroutine(_editorCoroutine);
                }
                _editorCoroutine = EditorCoroutineUtility.StartCoroutineOwnerless(((FlipTextView)target).EditorSetup(_fromText, _toText));
            }
        }
    }
}