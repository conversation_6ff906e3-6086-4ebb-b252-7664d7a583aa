using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using System.Linq;

public class ParticleSystemCullingModeSetter
{
    [MenuItem("BebopBee/Tools/Particles/Set CullingMode to Automatic (Top-Level Only)")]
    public static void SetParticleCullingModeTopLevelOnly()
    {
        UpdatePrefabs();
        UpdateScenes();
        Debug.Log("Done updating ParticleSystem CullingModes in prefabs and in build scenes.");
    }
    
    private static bool IsVariant(GameObject prefab)
    {
        var assetStatus = PrefabUtility.GetPrefabAssetType(prefab);
        return assetStatus == PrefabAssetType.Variant;
    }

    private static void UpdatePrefabs()
    {
        var prefabGuids = AssetDatabase.FindAssets("t:Prefab");
        var updatedCount = 0;

        foreach (var guid in prefabGuids)
        {
            var path = AssetDatabase.GUIDToAssetPath(guid);
            var prefabRoot = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            
            if (prefabRoot == null)
                continue;

            var particleSystems = prefabRoot.GetComponentsInChildren<ParticleSystem>(true);

            var changed = false;
            foreach (var ps in particleSystems)
            {
                var outermost = PrefabUtility.GetOutermostPrefabInstanceRoot(ps.gameObject);
                
                if (outermost != null && (outermost != prefabRoot || IsVariant(prefabRoot)))
                {
                    if (!HasCullingModeOverride(ps))
                        continue;
                }

                var main = ps.main;
                if (main.cullingMode is ParticleSystemCullingMode.Pause or ParticleSystemCullingMode.Automatic)
                    continue;

                Undo.RecordObject(ps, "Set Particle System Culling Mode");
                main.cullingMode = ParticleSystemCullingMode.Automatic;
                changed = true;
            }

            if (!changed)
                continue;
            
            EditorUtility.SetDirty(prefabRoot);
            PrefabUtility.SavePrefabAsset(prefabRoot);
            Debug.Log($"Updated ParticleSystems in top-level prefab: {path}", prefabRoot);
            updatedCount++;
        }
        
        Debug.Log($"Updated {updatedCount} prefab(s).");
    }
    
    private static bool HasCullingModeOverride(ParticleSystem ps)
    {
        var modifications = PrefabUtility.GetPropertyModifications(ps);
        return modifications != null && modifications.Any(mod => mod.propertyPath.Contains("cullingMode"));
    }

    private static void UpdateScenes()
    {
        var scenePaths = EditorBuildSettings.scenes
                           .Where(s => s.enabled)
                           .Select(s => s.path)
                           .ToArray();

        foreach (var scenePath in scenePaths)
        {
            var scene = EditorSceneManager.OpenScene(scenePath, OpenSceneMode.Single);
            
            var changed = false;
            var particleSystems = Object.FindObjectsOfType<ParticleSystem>(true);

            foreach (var ps in particleSystems)
            {
                var outermost = PrefabUtility.GetOutermostPrefabInstanceRoot(ps.gameObject);
                
                if (outermost != null && !HasCullingModeOverride(ps))
                    continue;
                
                var main = ps.main;
                if (main.cullingMode is ParticleSystemCullingMode.Pause or ParticleSystemCullingMode.Automatic)
                    continue;

                Undo.RecordObject(ps, "Set Particle System Culling Mode");
                main.cullingMode = ParticleSystemCullingMode.Automatic;
                changed = true;
            }

            if (!changed)
                continue;

            EditorSceneManager.MarkSceneDirty(scene);
            EditorSceneManager.SaveScene(scene);
            Debug.Log($"Updated ParticleSystems in scene: {scenePath}");
        }
    }
}