using BBB.Core;
using BBB.GameAssets.Scripts.Player;
using GameAssets.Scripts.Map.UI.Controllers;
using PBGame;

namespace BBB.UI.UI.Controllers
{
    public class DebugLevelStarter : LevelStarter
    {
        public override void StartLevel(string levelUid)
        {
            var levelConfigs = Config.Get<FBConfig.ProgressionLevelConfig>();

            if (!levelConfigs.TryGetValue(levelUid, out FBConfig.ProgressionLevelConfig levelConfig))
            {
                BDebug.LogError(LogCat.General, $"Level config {levelUid} not found");
                return;
            }

            var levelData = new LevelRemoteData(levelConfig, new LevelState { Stage = 0 }, false);
            ScreensBuilder.ShowScreen(ScreenType.LevelScreen, new LoadLevelAsyncCommand(levelData));
        }
    }
}