namespace BBB
{
    public class SystemRandom : IRandomService 
    {
        private System.Random   randomGen;
        
        public SystemRandom()
        {
            randomGen = new System.Random();
        }

        public SystemRandom(int seed)
        {
            randomGen = new System.Random(seed);
        }

        public int Next()
        {
            return randomGen.Next();
        }

        public int Next(int max)
        {
            return randomGen.Next(max);
        }

        public int Next(int min, int max)
        {
            return randomGen.Next(min, max);
        }

        public void SetSeed(int seed)
        {
            randomGen = new System.Random(seed);
        }
    }
}