using System;
using System.Collections.Generic;
using BBB.UI.Core;
using Cysharp.Threading.Tasks;
using PBGame;

namespace GameAssets.Scripts.DailyTask
{
    public interface IDailyTasksModalViewPresenter : IModalsViewPresenter
    {
        void Setup(Func<double> timeLeftFunc);
        void SetupStreak(int currentStreak, int lastStreak);
        UniTask SetupItems(List<TaskState> dailyTasks);
        void UpdateItem(string taskUid, TaskState dailyTask);
        void RefreshModalHeader();
        
        event Action<string> OnSwapped;
        event Action<string> OnClaimed;
        event Action<string> OnFlowRequested;
    }
}