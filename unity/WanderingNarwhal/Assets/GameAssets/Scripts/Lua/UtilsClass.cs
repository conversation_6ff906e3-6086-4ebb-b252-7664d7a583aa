using BBB;
using BBB.Core.Updater;
using BBB.UI.IAP.Controllers;
using JetBrains.Annotations;

namespace GameAssets.Scripts.Lua
{
    public class UtilsClass
    {
        private IPlayer _player;
        private readonly IEventDispatcher _eventDispatcher;

        private bool _shouldShowExtendedIapStore;

        public UtilsClass(IPlayer player, IEventDispatcher eventDispatcher)
        {
            _player = player;
            _eventDispatcher = eventDispatcher;
            Subscribe();
        }

        private void OnShowExtendedStoreIapEvent(ShowExtendedStoreIapEvent ev)
        {
            _shouldShowExtendedIapStore = ev.Arg0;
        }

        [UsedImplicitly]
        public string GetCurrentPlatform()
        {
            return PlatformUtil.GetPlatform();
        }

        [UsedImplicitly]
        public string GetCurrentVersion()
        {
            return _player.CurrentVersion;
        }

        [UsedImplicitly]
        public string GetPreviousVersion()
        {
            return _player.PreviousVersion;
        }

        [UsedImplicitly]
        public bool IsCurrentVersionBiggerThan(string version)
        {
            var parsedVersion = GameUpdateEvaluator.ParseVersion(version);
            var parsedCurrentVersion = GameUpdateEvaluator.ParseVersion(_player.CurrentVersion);

            return parsedCurrentVersion.CompareTo(parsedVersion) == 1;
        }

        [UsedImplicitly]
        public bool IsCurrentVersionEqualTo(string version)
        {
            var parsedVersion = GameUpdateEvaluator.ParseVersion(version);
            var parsedCurrentVersion = GameUpdateEvaluator.ParseVersion(_player.CurrentVersion);

            return parsedCurrentVersion.CompareTo(parsedVersion) == 0;
        }

        [UsedImplicitly]
        public bool IsCurrentVersionLowerThan(string version)
        {
            var parsedVersion = GameUpdateEvaluator.ParseVersion(version);
            var parsedCurrentVersion = GameUpdateEvaluator.ParseVersion(_player.CurrentVersion);

            return parsedCurrentVersion.CompareTo(parsedVersion) == -1;
        }

        [UsedImplicitly]
        public bool ShouldShowStoreExtendedVersion()
        {
            return _shouldShowExtendedIapStore;
        }

        private void Subscribe()
        {
            Unsubscribe();
            _eventDispatcher?.AddListener<ShowExtendedStoreIapEvent>(OnShowExtendedStoreIapEvent);
        }
        
        public void Unsubscribe()
        {
            _eventDispatcher?.RemoveListener<ShowExtendedStoreIapEvent>(OnShowExtendedStoreIapEvent);
        }
    }
}