using LuaInterface;

public interface ILuaExpressionParams
{
    string GetExpressionKey( string expression );
    string GetFuncArguments();
    object[] CallLuaFunction( LuaFunction luaFunc );
}

// NOTE: I added this on purpose as an example on how to define a lua expression params
//public class DailySnapSilConditionLuaParams : ILuaExpressionParams {
//    public string exhibitUid;
//    public string animalUid;
//    public bool zoom;
//    public bool goofball;
//    public int slot;

//    public string GetExpressionKey (string expression)
//    {
//        return "OCC_" + expression;
//    }

//    public string GetFuncArguments ()
//    {
//        return "(exhibitUid, animalUid, zoom, goofball, slot)";
//    }

//    public object[] CallLuaFunction (LuaFunction luaFunc)
//    {
//        return luaFunc.Call(exhibitUid, animalUid, zoom, goofball, slot);
//    }
//}
