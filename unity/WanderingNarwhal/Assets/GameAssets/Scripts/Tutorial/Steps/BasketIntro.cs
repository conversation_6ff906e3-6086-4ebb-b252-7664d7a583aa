using System.Collections;
using GameAssets.Scripts.IAP.Baskets;
using GameAssets.Scripts.Promotions;
using GameAssets.Scripts.Tutorial.Core;
using UnityEngine;

namespace GameAssets.Scripts.Tutorial.Steps
{
    public class BasketIntro : BaseStep
    {
        [SerializeField] private GameObject _dialogHolder;
        [SerializeField] private RectTransform _arrowHolder;
        [SerializeField] private Vector2 _arrowOffset;

        public override void Setup()
        {
            base.Setup();

            var initSubstep = new Substep(CoroutineExecutor) { isBlockingNonImmediatePopups = true };
            initSubstep.SetupExecutionCoroutine(WaitForTheLevelFXSequenceCompleted);

            var initSubstep2 = new Substep(CoroutineExecutor) { isBlockingMapScroll = true, isBlockingNonImmediatePopups = true, isForceShowingHUD = true };
            initSubstep2.SetupExecutionCoroutine(WaitForBottomBarReady);

            var dialog = new Substep(CoroutineExecutor) { isBlockingMapScroll = true, isBlockingNonImmediatePopups = true, isForceShowingHUD = true };
            dialog.SetupExecutionCoroutine(DialogCoroutine);

            var resetSubstep = new Substep(CoroutineExecutor) { isForceShowingHUD = false };

            SubstepsList.Add(initSubstep);
            SubstepsList.Add(initSubstep2);
            SubstepsList.Add(dialog);
            SubstepsList.Add(resetSubstep);

            _dialogHolder.SetActive(false);
        }

        private IEnumerator DialogCoroutine()
        {
            Promotion activePromotion = null;
            var activePromotions = PromotionManager.ActivePromotions;

            if (!activePromotions.ContainsKey("promo_baskets_nonpayer") && !activePromotions.ContainsKey("promo_baskets_payer"))
            {
                Debug.LogWarning($"No basket active promotion, not supported by basket tutorial");
                yield break;
            }

            if (activePromotions.TryGetValue("promo_baskets_nonpayer", out var promotion))
                activePromotion = promotion;
            else
                activePromotion = activePromotions["promo_baskets_payer"];

            TutorialElementsHighlighter.EnableHighlightElementsOfType("hud_promo_basket", true);
            var hudPromo = TutorialElementsHighlighter.GetElementOfType("hud_promo_basket");
            if (hudPromo == null)
                yield break;

            var followingCoroutine = StartCoroutine(FollowTransform(_arrowHolder, hudPromo.transform, _arrowOffset));
            _dialogHolder.SetActive(true);

            ResetTap();
            while (!ModalsManager.IsShowingModal(typeof(BasketController)) && !IsTapped)
                yield return null;

            StopCoroutine(followingCoroutine);
            _dialogHolder.SetActive(false);
            TutorialElementsHighlighter.EnableHighlightElementsOfType("hud_promo_basket", false);
            ModalsManager.HideAllModals();

            PromotionManager.InvokeHudActions(activePromotion);
        }
    }
}