using System.Collections;
using BBB;
using GameAssets.Scripts.SocialScreens.Teams;
using GameAssets.Scripts.Tutorial.Core;
using GameAssets.Scripts.Utils;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Tutorial.Steps
{
    public class AskForLivesIntro : BaseStep
    {
        private const string AskForLivesUid = "ask_for_lives";

        [SerializeField] private GameObject _dialogHolder;

        public override void Setup()
        {
            base.Setup();

            var dialog = new Substep(CoroutineExecutor);
            dialog.SetupExecutionCoroutine(DialogCoroutine);

            SubstepsList.Add(dialog);

            _dialogHolder.SetActive(false);
        }

        private IEnumerator DialogCoroutine()
        {
            var socialModalController = (SocialModalController)ModalsManager.CurrentActiveModal;

            while (socialModalController.IsLoading)
            {
                yield return null;
            }

            if (!AccountManager.IsInTeam || !ModalsManager.IsShowingModal(ModalsType.Social))
                yield break;

            SocialManager.SetForceCanAskForLives(true);

            TutorialElementsHighlighter.EnableHighlightElementsOfType(AskForLivesUid, true);
            _dialogHolder.SetActive(true);

            var askForLivesButton = TutorialElementsHighlighter.GetElementOfType(AskForLivesUid);
            var graphicRaycaster = askForLivesButton.GetComponent<GraphicRaycaster>();
            graphicRaycaster.enabled = false;

            _screenLocker.SetActive(true);
            yield return WaitCache.Seconds(0.3f);
            _screenLocker.SetActive(false);

            ResetTap();
            while (ModalsManager.IsShowingModal(ModalsType.Social) && AccountManager.IsInTeam && !IsTapped)
                yield return null;

            graphicRaycaster.enabled = true;
            askForLivesButton.GetComponent<Button>().onClick.Invoke();
            _dialogHolder.SetActive(false);
            TutorialElementsHighlighter.EnableHighlightElementsOfType(AskForLivesUid, false);
            yield return ButtonClick(askForLivesButton.gameObject);
        }
    }
}