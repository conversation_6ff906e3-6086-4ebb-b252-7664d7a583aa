using System;
using System.Collections;
using BBB;
using BBB.Audio;
using BebopBee.Core.Audio;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Utils;
using UnityEngine;
using UnityEngine.UI;
using Object = UnityEngine.Object;

public class GenericCurrencyDropable : BbbMonoBehaviour
{
    private static int _sign = 1;

    [SerializeField] private float _durationSeconds = 2.5f;
    [SerializeField] private Image _image;
    [SerializeField] private Button _imageButton;

    [SerializeField] private float _maxStartYSpeed = 10f;
    [SerializeField] private float _maxStartXSpeed = 5f;
    [SerializeField] private float _grav = -5f;
    [SerializeField] private float _baseGroundOffset = -5f;
    [SerializeField] private float _maxRandomGroundOffset = 5f;
    [SerializeField] private float _bounceRatio = 0.1f;
    [SerializeField] private int _numOfBounces = 3;
    [SerializeField] private float _coefOfSlowingDependsOnyVelocity;

    [SerializeField] private string _startFlySoundId = GenericSoundIds.CurrencyFlyStart;
    [SerializeField] private string _endFlySoundId = GenericSoundIds.CurrencyFlyEnd;
    private float _xSpeedMultiplier;

    private Action<Transform> _onFinish;
    private ICoroutineExecutor _coroutineExecutor;

    public bool PlayAnimation;
    private bool _onlyOneCurrency;
    
    private Coroutine _dropRoutine;

    private void Awake()
    {
        _imageButton.ReplaceOnClick(() =>
        {
            if (_dropRoutine != null)
            {
                if (_coroutineExecutor != null)
                {
                    _coroutineExecutor.StopCoroutine(_dropRoutine);
                }

                Finish();
            }
        });
    }

    public void StartDrop(ICoroutineExecutor coroutineExecutor, Action<Transform> onFinish, float xSpeedMultiplier, bool onlyOneCurrency)
    {
        _onFinish = onFinish;
        _xSpeedMultiplier = xSpeedMultiplier;
        _onlyOneCurrency = onlyOneCurrency;
        _coroutineExecutor = coroutineExecutor;

        _dropRoutine = _coroutineExecutor.StartCoroutine(DropAnimation());
        _coroutineExecutor.StartCoroutine(Waiter());
    }

    public void SetIconSprite(Sprite sprite)
    {
        _image.sprite = sprite;
    }

    void Update()
    {
        if (PlayAnimation)
        {
            if (_coroutineExecutor != null)
            {
                _coroutineExecutor.StartCoroutine(DropAnimation());
            }

            PlayAnimation = false;
        }
    }

    private IEnumerator Waiter()
    {
        yield return WaitCache.Seconds(_durationSeconds);
        if (_dropRoutine != null)
        {
            _coroutineExecutor?.StopCoroutine(_dropRoutine);

            Finish();
        }
    }

    private IEnumerator DropAnimation()
    {
        AudioProxy.PlaySound(_startFlySoundId);
        var startYSpeed = _maxStartYSpeed * UnityEngine.Random.Range(0.6f, 1f);
        float startXSpeed = 0;
        float xSpeedMultiplier = 0f;
        if (_onlyOneCurrency)
        {
            xSpeedMultiplier = UnityEngine.Random.Range(0.6f, 1f) * _sign;
            _sign *= -1;
        }
        else
        {
            xSpeedMultiplier = _xSpeedMultiplier * UnityEngine.Random.Range(0.8f, 1.2f);
        }

        startXSpeed = _maxStartXSpeed * xSpeedMultiplier;
        float ySpeed = startYSpeed;

        float groundYCoord = transform.localPosition.y + _baseGroundOffset + _maxRandomGroundOffset * UnityEngine.Random.Range(-1f, 1f);
        _numOfBounces = UnityEngine.Random.Range(_numOfBounces - 1, _numOfBounces + 2);

        _coefOfSlowingDependsOnyVelocity = 0.5f;

        while (_numOfBounces > 0)
        {
            if (transform == null)
                yield break;
            
            if (transform.localPosition.y < groundYCoord && ySpeed < 0)
            {
                _numOfBounces--;
                ySpeed = -ySpeed * _bounceRatio;
            }

            transform.localPosition += new Vector3(startXSpeed * ((1f - _coefOfSlowingDependsOnyVelocity) + _coefOfSlowingDependsOnyVelocity * Mathf.Abs(ySpeed) / -_grav), ySpeed, 0) * Time.deltaTime;
            ySpeed += _grav * Time.deltaTime * ((1f - _coefOfSlowingDependsOnyVelocity) + _coefOfSlowingDependsOnyVelocity * Mathf.Abs(ySpeed) / -_grav);
            yield return null;
        }
    }

    protected override void OnDisable()
    {
        if (_dropRoutine == null)
            return;
        
        if (_coroutineExecutor != null && (_coroutineExecutor as MonoBehaviour) != null)
        {
            _coroutineExecutor.StopCoroutine(_dropRoutine);
        }

        Finish();
    }

    private void Finish()
    {
        _onFinish?.Invoke(_image ? _image.transform : null);

        _onFinish = null;
        _dropRoutine = null;

        if (this == null)
            return;
        
        AudioProxy.PlaySound(_endFlySoundId);
        Destroy(gameObject);
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        _onFinish = null;
    }
}