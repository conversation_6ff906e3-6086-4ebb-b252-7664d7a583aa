using BBB.RaceEvents;
using TMPro;
using UnityEngine;

namespace GameAssets.Scripts.GenericModals.Info
{
    public class FoodEventIntroContent : RaceEventIntroContent
    {
        [SerializeField] private TextMeshProUGUI[] _stageNumber;
        [SerializeField] private TextMeshProUGUI _streakRaceStageBody;
        [SerializeField] private TextMeshProUGUI _streakRaceStageName;

        public void Setup(string raceStageUid, int raceStageIndex, RaceEventTypes.RaceEventType raceEventType, string introText, string raceStateNameText)
        {
            base.Setup(raceStageUid);
            var raceStageToDisplay = raceStageIndex + 1;

            if (raceStageToDisplay <= 0 || raceEventType != RaceEventTypes.RaceEventType.Streak)
                return;

            var raceStageText = raceStageToDisplay.ToString();
            foreach (var stageNumber in _stageNumber)
            {
                stageNumber.text = raceStageText;
            }

            _streakRaceStageBody.text = introText;
            _streakRaceStageName.text = raceStateNameText;
        }
    }
}