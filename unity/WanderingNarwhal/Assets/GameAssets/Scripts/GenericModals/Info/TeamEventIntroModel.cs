using System;
using BBB;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.RaceEvents.UI;
using BBB.TeamCoopEventAnalytic;
using BBB.TeamEvents;
using BebopBee;
using BebopBee.Core.UI;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.GenericModals.Info.Core;
using GameAssets.Scripts.SocialScreens.Teams;

namespace GameAssets.Scripts.GenericModals.Info
{
    public class TeamEventIntroModel : GenericInfoModel, IContextInitializable
    {
        private const string TeamCoopButtonContinueTextId = "TEAM_COOP_BUTTON_CONTINUE";
        private const string TeamCoopButtonJoinTextId = "TEAM_COOP_BUTTON_JOIN";
        private const string TeamCoopButtonPlayTextId = "TEAM_COOP_BUTTON_PLAY";
        private const string EventPictureStringFormat = "intro_event_picture";

        private ITeamEventManager _eventManager;
        private IAccountManager _accountManager;
        private ISocialManager _socialManager;
        private GameNotificationManager _notificationManager;
        private ILocalizationManager _localizationManager;

        private readonly TeamEvent _teamEvent;
        private bool _infoMode;

        private bool _startPlayingPressed;
        private bool _socialVisiting;

        public override string ConfigName => "GenericInfoModalConfig_TeamEventIntro";
        public override string Name  => "TeamEventIntroModel";
        public override bool CloseOnPrimaryButton => false;
        public override bool IsTimerFinished => _teamEvent.Finished;
        public override TimeSpan GetTimerRemainingTime => _teamEvent.GetTimeLeft();

        public TeamEventIntroModel(TeamEvent teamEvent, bool infoMode = false)
        {
            _infoMode = infoMode;
            _teamEvent = teamEvent;
            EventUid = teamEvent.Uid;
        }

        public void InitializeByContext(IContext context)
        {
            _eventManager = context.Resolve<ITeamEventManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _socialManager = context.Resolve<ISocialManager>();
            _notificationManager = context.Resolve<GameNotificationManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
        }

        public override void OnShow()
        {
            ModalView.SetTitle(_teamEvent.MainTitle);

            if (_infoMode)
            {
                ModalView.SetDescription(_teamEvent.GetInfoModeDescription());
                ModalView.SetButtonText(_localizationManager.getLocalizedText(TeamCoopButtonContinueTextId));
            }
            else
            {
                ModalView.SetDescription(_teamEvent.GetDescription(_accountManager.IsInTeam));
                ModalView.SetButtonText(_localizationManager.getLocalizedText(_accountManager.IsInTeam ? TeamCoopButtonPlayTextId : TeamCoopButtonJoinTextId));
            }

            var eventPictureRenderer = ModalView.ContentInstance.GetComponentInChildren<EventPictureRenderer>(true);
            if (eventPictureRenderer != null)
            {
                eventPictureRenderer.RefreshFromConfig<TeamCoopEventVisualConfig>(_teamEvent.EventResourceId, EventPictureStringFormat, GameEventResKeys.TeamCoopGameEventSettings);
            }
        }

        public override void OnHide()
        {
            LogTapEvent();
            _eventManager.ProcessEventStates();

            if (_socialVisiting)
                return;

            var prevJoined = _teamEvent.Joined;
            if (_startPlayingPressed && _accountManager.IsInTeam && !prevJoined)
            {
                _teamEvent.JoinIfLaunched();
            }

            var afterJoined = _teamEvent.Joined;

            if ((!prevJoined && afterJoined) || (_infoMode && !_startPlayingPressed) || (!_infoMode && _startPlayingPressed))
            {
                DauInteractions.TapOnAutoPopups.AwaitLogs(DauInteractions.TapOnAutoPopups.GameEventClick, DauInteractions.TapOnAutoPopups.GameEventClose);
                _teamEvent.OpenMainModal(ShowMode.Delayed);
            }
            else if (_infoMode)
            {
                _eventManager.EnterLevelFlow();
            }

            _infoMode = false;
            _startPlayingPressed = false;

            if (!_accountManager.IsInTeam)
            {
                var notifier = _notificationManager.GetTeamCoopEventNotifier(_teamEvent.Uid);
                notifier?.ResetNotifier();
            }
        }

        public override void PrimaryButtonHandler()
        {
            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                ModalController.ShowFloatingText(LocalizationManagerHelper.OfflineEventStartKey, _teamEvent.MainTitle);
                return;
            }

            if (_accountManager.IsInTeam)
            {
                _startPlayingPressed = true;
            }
            else
            {
                _socialVisiting = true;
                _socialManager.ShowSocialModal(SocialHiddenCallback);
            }

            ModalController.TriggerHide();
        }

        private void SocialHiddenCallback()
        {
            _socialVisiting = false;
            ModalController.Show(this, ShowMode.Delayed);
        }

        private void LogTapEvent()
        {
            if (!_infoMode)
            {
                var postFix = _accountManager.IsInTeam ? AnalyticNames.PlayPostfix : AnalyticNames.JoinPostfix;
                var logTapEventProductId = _teamEvent.Uid + postFix;

                DauInteractions.TapOnAutoPopups.LogIfExpected(ModalController.ClosedIntentionally ? DauInteractions.TapOnAutoPopups.GameEventClose : DauInteractions.TapOnAutoPopups.GameEventClick,
                    logTapEventProductId);
            }

            DauInteractions.TapOnAutoPopups.ClearAwaitLogs(DauInteractions.TapOnAutoPopups.GameEventClick,
                DauInteractions.TapOnAutoPopups.GameEventClose);
        }
    }
}