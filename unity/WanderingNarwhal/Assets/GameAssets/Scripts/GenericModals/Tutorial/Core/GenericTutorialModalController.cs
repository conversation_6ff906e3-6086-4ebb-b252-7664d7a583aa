using BebopBee.Core.UI;

namespace GameAssets.Scripts.GenericModals.Tutorial.Core
{
    public class GenericTutorialModalController : BaseGenericModalController<IGenericTutorialViewPresenter, GenericTutorialModel, GenericTutorialModalConfig>
    {
        public new GenericTutorialModalController SetupAndShow(GenericTutorialModel model, ShowMode showMode = ShowMode.Delayed)
        {
            return base.SetupAndShow(model, showMode) as GenericTutorialModalController;
        }
    }
}