using System;
using BBB;
using BBB.Core.UI;
using UnityEngine;

namespace GameAssets.Scripts.GenericModals.Tutorial
{
    public class CompetitionEventTutorialContent : BbbMonoBehaviour
    {
        [SerializeField] private GameEventReplaceableGo _replaceableGo;

        public void Setup(GameEventBase gameEvent, IGameEventResourceManager gameEventResourceManager, Action<GameObject> setupCallback)
        {
            _replaceableGo.Init(gameEventResourceManager);
            
            var prefabName = GameEventResKeys.InfoLayoutMain;
            _replaceableGo.Refresh(gameEvent.Uid, prefabName);
            _replaceableGo.InvokeOnGo(setupCallback);
        }
    }
}