using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.UI;
using BBB.UI.Core;
using BBB.Wallet;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.GenericModals.Tutorial
{
    public class CompetitionEventLeagueRewardsContent : ContextedUiBehaviour
    {
        [SerializeField] private LeagueTopRewardItem[] _top3Places;
        [SerializeField] private Image[] _leagueIcons;
        [SerializeField] private UIRewardComponent[] _leagueTopRewards;

        private IGameEventResourceManager _gameEventResourceManager;
        
        protected override void InitWithContextInternal(IContext context)
        {
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();

            foreach (var uiRewardComponent in _leagueTopRewards)
            {
                uiRewardComponent.Init(context);
            }
        }
        
        public void Setup(CompetitionGameEvent gameEvent)
        {
            LazyInit();
            
            RefreshLeagueIcons(gameEvent.Uid);
            RefreshRewards(gameEvent);

            for (var i = 0; i < _top3Places.Length; i++)
            {
                var rewards = gameEvent.GetOrderedRewardForPlace(League.Fire, i, out _);
                var hasSpecialReward = false;

                foreach (var key in rewards.Keys)
                {
                    if (!RewardsUtility.IsSpecialReward(key)) continue;
                    
                    hasSpecialReward = true;
                    break;
                }

                if (hasSpecialReward)
                {
                    _top3Places[i].Setup(gameEvent, _gameEventResourceManager, i + 1);
                    _top3Places[i].gameObject.SetActive(true);
                }
                else
                {
                    _top3Places[i].gameObject.SetActive(false);
                }
            }
        }

        private void RefreshLeagueIcons(string eventUid)
        {
            var leagueValues = Enum.GetValues(typeof(League));
            if (leagueValues.Length > _leagueIcons.Length)
            {
                Debug.LogError($"There are {leagueValues.Length} leagues and only {_leagueIcons.Length} image holders");
            }

            var counter = 0;
            foreach (League league in leagueValues)
            {
                var index = counter;
                var spriteName = league.ToBadgeSpriteName();

                _gameEventResourceManager
                    .GetSpriteAsync(eventUid, spriteName)
                    .ContinueWith(sprite =>
                    {
                        if (sprite == null)
                        {
                            BDebug.LogError(LogCat.Resources,$"Couldn't load league badge '{spriteName}' for event {eventUid}");
                        }
                        _leagueIcons[index].sprite = sprite;
                    });

                counter++;
            }
        }

        private void RefreshRewards(CompetitionGameEvent gameEvent)
        {
            var leagueValues = Enum.GetValues(typeof(League));
            if (leagueValues.Length > _leagueTopRewards.Length)
            {
                Debug.LogError($"There are {leagueValues.Length} leagues and only {_leagueTopRewards.Length} image holders");
            }

            var counter = 0;
            foreach (League league in leagueValues)
            {
                // workaround of aggregating
                if (league == League.Fire)
                {
                    var rewardDictionary = gameEvent.GetOrderedRewardForPlace(league, 0, out var ordering);
                    var rewardDictionaryClone = new Dictionary<string, int>(rewardDictionary);
                    foreach (var currencyUid in rewardDictionary.Keys)
                    {
                        if (InventoryItems.OrderOfPhysicalRewards.Contains(currencyUid))
                            rewardDictionaryClone.Remove(currencyUid);
                    }

                    _leagueTopRewards[counter].SetupReward(rewardDictionaryClone, ordering);
                }
                else
                {
                    var rewardDictionary = gameEvent.GetOrderedRewardForPlace(league, 0, out var ordering);
                    _leagueTopRewards[counter].SetupReward(rewardDictionary, ordering);
                }

                counter++;
            }
        }
    }
}