using System;
using System.Collections.Generic;
using System.Text;
using BBB;
using BBB.Audio;
using BBB.Core;
using BBB.DI;
using BBB.EndGameEvents;
using BebopBee;
using BebopBee.Core.Audio;
using BebopBee.Social;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.GenericModals.Tutorial.Core;
using GameAssets.Scripts.SocialScreens.Teams;
using RPC.Teams;
using UnityEngine;

namespace GameAssets.Scripts.GenericModals.Tutorial
{
    public class CompetitionEventTutorialModel : GenericTutorialModel, IContextInitializable
    {
        private const string PlaceLoc = "PLACE_LOC";

        private FakeUsersManager _fakeUsersManager;
        private IAccountManager _accountManager;
        private ISocialManager _socialManager;
        private ILocalizationManager _localizationManager;
        private IGameEventResourceManager _gameEventResourceManager;
        private IPlayerManager _playerManager;
        private TimeManager _timeManager;

        private readonly List<string> _avatarUrls = new();
        private readonly List<TeamMemberData> _teamMatesCached = new();

        private readonly GameEventBase _gameEvent;
        private readonly string _gameEventUid;
        private readonly bool _skipCompetitors;

        public override string ConfigName => "GenericTutorialModalConfig_CompetitionEvent";
        public override string Name => "CompetitionEventTutorialModel";

        public CompetitionEventTutorialModel(GameEventBase gameEvent, bool skipCompetitors = false)
        {
            _gameEvent = gameEvent;
            _gameEventUid = gameEvent.Uid;
            _skipCompetitors = skipCompetitors;
            EventUid = gameEvent.Uid;
        }

        public void InitializeByContext(IContext context)
        {
            _fakeUsersManager = context.Resolve<FakeUsersManager>();
            _socialManager = context.Resolve<ISocialManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            _timeManager = context.Resolve<TimeManager>();
        }

        public override void OnSetupContent(GameObject contentInstance)
        {
            var content = contentInstance.GetComponent<CompetitionEventTutorialContent>();
            content.Setup(_gameEvent, _gameEventResourceManager, SetupContent);
        }

        public override void OnShow()
        {
            const int avatarUrlListCount = 4;

            _avatarUrls.Clear();

            if (_teamMatesCached.IsNullOrEmpty() || _teamMatesCached.Count != _socialManager.TeamMates.Count)
            {
                _teamMatesCached.Clear();
                _teamMatesCached.AddRange(_socialManager.TeamMates);
                _teamMatesCached.Shuffle();
            }

            if (!_skipCompetitors)
            {
                _avatarUrls.Add(_accountManager.Profile.Avatar);

                var minLength = Math.Min(_teamMatesCached.Count, avatarUrlListCount);
                for (var i = 0; i < minLength; i++)
                {
                    _avatarUrls.Add(_teamMatesCached[i].Avatar);
                }

                if (_avatarUrls.Count <= avatarUrlListCount)
                {
                    foreach (var fakeUserProfile in _fakeUsersManager.GetFakeUsersPublicProfiles())
                    {
                        _avatarUrls.Add(fakeUserProfile.Avatar);

                        if (_avatarUrls.Count > avatarUrlListCount)
                            break;
                    }
                }
            }

            AudioProxy.PlaySound(GenericSoundIds.QuestBlueprintReward_Outro);
            _playerManager.Player.UpdateLastInteractionTimestamp(_gameEventUid, _timeManager.CurrentTimeStamp());
        }

        private void SetupContent(GameObject go)
        {
            var infoLayout = go.GetComponent<CompetitionInfoLayout>();
            if (infoLayout == null)
                return;

            var avatarImages = infoLayout.GetAvatarImages();
            if (avatarImages.Count > 0)
            {
                if (_avatarUrls.Count != avatarImages.Count)
                {
                    BDebug.LogError(LogCat.General, $"Count mismatch while trying " +
                                                    $"to initialize avatars for {_gameEventUid}; images count = {avatarImages.Count} " +
                                                    $"urls count = {_avatarUrls.Count}");
                }
                else
                {
                    for (var i = 0; i < _avatarUrls.Count; i++)
                    {
                        var currentImage = avatarImages[i];
                        currentImage.Setup(new AvatarInfo(_avatarUrls[i]));
                    }
                }
            }

            var textGraphics = infoLayout.GetPlacesTextGraphics();
            var placeLocalized = _localizationManager.getLocalizedText(PlaceLoc);

            var stringBuilder = new StringBuilder();
            for (var i = 0; i < textGraphics.Count; i++)
            {
                stringBuilder.Clear();
                stringBuilder.Append((i + 1).ToOrdinal()).Append(" ").Append(placeLocalized);
                textGraphics[i].text = stringBuilder.ToString();
            }

            var competitionInfoLayoutGenericInitializable = go.GetComponent<CompetitionInfoLayoutGenericInitializable>();
            if (competitionInfoLayoutGenericInitializable != null)
            {
                competitionInfoLayoutGenericInitializable.Init(_gameEventUid);
            }
        }

        public override void OnHide()
        {
            if (_gameEvent is SideMapGameEvent sideMapGameEvent)
            {
                sideMapGameEvent.MarkIntroDone();
                sideMapGameEvent.MarkClean();
            }

            if (_gameEvent is CompetitionGameEvent competitionGameEvent)
            {
                competitionGameEvent.MarkInfoShown();
            }

            _avatarUrls.Clear();
        }
    }
}