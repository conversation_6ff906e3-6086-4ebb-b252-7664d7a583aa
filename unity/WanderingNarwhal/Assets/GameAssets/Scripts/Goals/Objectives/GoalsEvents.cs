using FBConfig;
using GameAssets.Scripts.Messages;
using P<PERSON><PERSON>ame;

namespace BBB.Quests.Objectives
{
    public class ObjectiveData
    {
        public QuestObjectiveConfig ObjectiveConfig { get; private set; }
        public QuestConfig QuestConfig { get; private set; }
        public PBObjectiveProgress Progress { get; private set; }

        public ObjectiveData(QuestObjectiveConfig objectiveConfig, QuestConfig questConfig, PBObjectiveProgress progress)
        {
            ObjectiveConfig = objectiveConfig;
            QuestConfig = questConfig;
            Progress = progress;
        }
    }

    public class ObjectiveCompletedEvent : Message<ObjectiveData>
    {
    }

    public class ObjectiveClaimedEvent : Message<QuestObjectiveConfig>
    {
    }

    public class QuestData
    {
        public QuestConfig QuestConfig { get; private set; }

        public QuestData(QuestConfig questConfig)
        {
            QuestConfig = questConfig;
        }
    }

    public class QuestStartedEvent : Message<QuestData>
    {
    }

    // should be subscribed by scripting executor
    public class QuestCompletedEvent : Message<QuestData>
    {
    }
}

