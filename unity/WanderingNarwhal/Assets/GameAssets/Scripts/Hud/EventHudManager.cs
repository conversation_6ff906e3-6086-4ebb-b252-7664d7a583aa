using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.UI;
using Core.Configs;
using Cysharp.Threading.Tasks;
using FBConfig;
using UnityEngine;

namespace BBB
{
    public class EventHudManager : CustomHudManager
    {
        [SerializeField] private Transform _container;

        private readonly List<UniTask> _iconToPreloadTasks = new();
        private readonly List<IGenericHudDrivenAssetReference> _hudAssets = new();
        private readonly Dictionary<string, GenericHudComponent> _instantiatedHudElements = new();
        private IContext _context;
        private IAssetsManager _assetsManager;
        private int _assetsToLoad;
        private IDictionary<string, HudAssetReferenceConfig> _hudAssetReferenceConfigs;
        private IConfig _config;
        private const string HudNameMask = "{0}_hud";

        private static readonly Type[] RequiredConfigs =
        {
            typeof(HudAssetReferenceConfig)
        };

        private static class AssetReferences
        {
            public const string DailyTrivia = "DailyTriviaHudAssetReference";
            public const string Didi = "DidiHudAssetReference";
            public const string RoyaleEvent = "RoyaleEventHudAssetReference";
            public const string CompetitionEvent = "UnifiedCompetitionEventHudAssetReference";
            public const string TeamEvent = "TeamEventHudAssetReference";
            public const string RaceEvent = "UnifiedRaceEventHudAssetReference";
            public const string DailyTask = "DailyTaskHudAssetReference";
            public const string DailyLogin = "DailyLoginHudAssetReference";
        }

        private void Setup(string uid, string handler, GameEventGameplayType gameplayType)
        {
            IGenericHudDrivenAssetReference hudAssetReference = handler switch
            {
                AssetReferences.DailyTrivia => new DailyTriviaHudAssetReference { AssetName = uid },
                AssetReferences.DailyTask => new DailyTaskHudAssetReference { AssetName = uid },
                AssetReferences.DailyLogin => new DailyLoginHudAssetReference { AssetName = uid },
                AssetReferences.Didi => new DidiHudAssetReference { AssetName = uid },
                AssetReferences.RoyaleEvent => new RoyaleEventHudAssetReference { AssetName = uid },
                AssetReferences.CompetitionEvent => new UnifiedCompetitionEventHudAssetReference { AssetName = uid },
                AssetReferences.TeamEvent => new TeamEventHudAssetReference { AssetName = string.Format(HudNameMask, uid), TeamEventUid = uid },
                AssetReferences.RaceEvent => new UnifiedRaceEventHudAssetReference { AssetName = string.Format(HudNameMask, uid), RaceEventUid = uid },
                _ => null
            };

            if (hudAssetReference != null)
            {
                _hudAssets.Add(hudAssetReference);
            }
        }

        private void SetupConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            _hudAssetReferenceConfigs = config.Get<HudAssetReferenceConfig>();
            if (_hudAssetReferenceConfigs != null)
            {
                _hudAssets.Clear();
                foreach (var hudAssetReferenceConfig in _hudAssetReferenceConfigs.Values)
                {
                    Setup(hudAssetReferenceConfig.Uid, hudAssetReferenceConfig.Handler, (GameEventGameplayType)hudAssetReferenceConfig.GameplayType);
                }
            }
            else
            {
                Debug.LogError("HudAssetReferenceConfig is missing");
            }
        }

        public override void Init(IContext context)
        {
            _context = context;
            _assetsManager = context.Resolve<IAssetsManager>();
            _config = context.Resolve<IConfig>();
            SetupConfig(_config);
            Config.OnConfigUpdated -= SetupConfig;
            Config.OnConfigUpdated += SetupConfig;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            foreach (var hudAsset in _hudAssets)
            {
                if (hudAsset == null || (!hudAsset.AssetName.IsNullOrEmpty() && !_instantiatedHudElements.ContainsKey(hudAsset.AssetName)))
                    continue;

                _assetsManager.UnloadAsset(hudAsset.AssetName);
            }

            foreach (var instantiatedHudElement in _instantiatedHudElements.Values)
            {
                Destroy(instantiatedHudElement.gameObject);
            }

            _instantiatedHudElements.Clear();
            Config.OnConfigUpdated -= SetupConfig;
        }

        public override void Refresh()
        {
            LoadHudElements();
        }

        private void LoadHudElements()
        {
            if (IsLoading)
            {
                BDebug.LogError(LogCat.Hud, "Attempt to LoadHudElements while loading is in process");
                return;
            }

            _assetsToLoad = 0;
            foreach (var hudAsset in _hudAssets)
            {
                if (hudAsset == null || (!hudAsset.AssetName.IsNullOrEmpty() && _instantiatedHudElements.TryGetValue(hudAsset.AssetName, out var instantiatedHudElement)) || !hudAsset.ShouldBeInstantiated(_context))
                    continue;

                _assetsToLoad++;
                IsLoading = true;
                PreloadHudIconAsync(hudAsset.AssetName).ContinueWith(() =>
                {
                    _assetsToLoad--;
                    if (_assetsToLoad <= 0)
                    {
                        IsLoading = false;
                    }

                    if (_instantiatedHudElements.TryGetValue(hudAsset.AssetName, out var instantiatedHudElement))
                    {
                        instantiatedHudElement.TryToShow();
                    }
                }).Forget();
            }
        }

        public override List<UniTask> GetHudIconsToPreloadTasks(string screenBeingLoaded, ScreenType screenType)
        {
            if ((screenType & ScreenType.Map) == 0)
                return null;

            _iconToPreloadTasks.Clear();

            foreach (var hudAsset in _hudAssets)
            {
                if (hudAsset == null || (!hudAsset.AssetName.IsNullOrEmpty() && _instantiatedHudElements.ContainsKey(hudAsset.AssetName)) || !hudAsset.ShouldBeInstantiated(_context))
                    continue;

                _iconToPreloadTasks.Add(PreloadHudIconAsync(hudAsset.AssetName));
            }

            return _iconToPreloadTasks;
        }

        private async UniTask PreloadHudIconAsync(string assetName)
        {
            await _assetsManager.LoadAsync<GameObject>(assetName).ContinueWith(instantiatedPrefab =>
            {
                if (instantiatedPrefab == null)
                {
                    BDebug.LogErrorFormat(LogCat.Hud, "Can't find hud element for : {0}", assetName);
                    return;
                }

                if (_instantiatedHudElements.ContainsKey(assetName))
                {
                    BDebug.LogWarning(LogCat.Hud, $"Hud element is already instantiated for : {assetName}");
                    _assetsManager.UnloadAsset(assetName);
                    return;
                }

                var instantiatedHudElement = Instantiate(instantiatedPrefab.Get(), _container, false).GetComponent<GenericHudComponent>();
                instantiatedHudElement.Init(_context);
                _instantiatedHudElements[assetName] = instantiatedHudElement;
            });
        }
    }
}