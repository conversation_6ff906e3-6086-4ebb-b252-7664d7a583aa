using BBB;
using UnityEngine;

public class CopyCoordsComponent : BbbMonoBehaviour
{
    [SerializeField] private Transform _target;
    [SerializeField] private bool _copyX;
    [SerializeField] private bool _copyY;

    private void Awake()
    {
        Refresh();
    }

    [ContextMenu("Refresh")]
    public void Refresh()
    {
        var currentPosition = transform.position;
        var targetPosition = _target.position;

        if (_copyX)
            currentPosition.x = targetPosition.x;
        if (_copyY)
            currentPosition.y = targetPosition.y;

        transform.position = currentPosition;
    }
}