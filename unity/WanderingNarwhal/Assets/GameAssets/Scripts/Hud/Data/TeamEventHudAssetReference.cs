using BBB.DI;
using BBB.TeamEvents;

namespace BBB.UI
{
    public class TeamEventHudAssetReference : IGenericHudDrivenAssetReference
    {
        public string AssetName { get; set; }

        public string TeamEventUid;

        public bool ShouldBeInstantiated(IContext context)
        {
            var currentScreen = context.Resolve<IScreensManager>().GetCurrentScreenType();

            if ((currentScreen & ScreenType.FullHudScreen) == 0)
                return false;

            var teamCoopEventManager = context.Resolve<ITeamEventManager>();

            var teamCoopEvent = teamCoopEventManager.GetEvent(TeamEventUid);
            return teamCoopEvent != null && teamCoopEvent.ShouldShowIcon();
        }
    }
}