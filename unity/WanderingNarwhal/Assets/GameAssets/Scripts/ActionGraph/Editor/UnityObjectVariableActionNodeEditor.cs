using BBB.ActionGraph.VariableNodes;
using UnityEditor;

namespace BBB.ActionGraph.Editor
{
    [CustomNodeEditor(typeof(UnityObjectVariableActionNode))]
    public class UnityObjectVariableActionNodeEditor : VariableActionNodeEditor
    {
        public override void OnCreate()
        {
            base.OnCreate();
            //provide the node context for ExposedProperties
            serializedObject = new SerializedObject(target, target);
        }
    }
}