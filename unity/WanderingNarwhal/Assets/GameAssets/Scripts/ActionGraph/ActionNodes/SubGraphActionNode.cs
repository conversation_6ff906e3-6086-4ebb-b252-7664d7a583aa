using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using Cysharp.Threading.Tasks;
using BBB.ActionGraph.VariableNodes;
using UnityEngine;
using BBB.Core;

namespace BBB.ActionGraph.ActionNodes
{
    [CreateNodeMenu("Macro")]
    public class SubGraphActionNode : FlowInActionNode, IVariableResolveable
    {
        [SerializeField] private ActionGraph _graph;
        [SerializeField] public string GraphName;

        private ActionGraph _runningGraph;
        private ActionGraph RunningGraph
        {
            get
            {
                if (_graph != null)
                {
                    return _runningGraph ??= _graph.Clone();
                }

                return null;
            }
        }

        protected override async UniTask ExecuteInternal(ActionGraphContext graphContext, CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                return;

            if (RunningGraph == null)
            {
                BDebug.LogError(LogCat.Graph, "Graph is null with NOT cancelled token, when starting flow");
                return;
            }
                
            RunningGraph.SortNodes();
            
            foreach (var node in RunningGraph.nodes)
            {
                if (node is VariableActionNode variableActionNode)
                {
                    var variableName = variableActionNode.VariableName;
                    if (string.IsNullOrEmpty(variableName))
                    {
                        BDebug.LogError(LogCat.Graph, $"VariableName is empty in {GetType().Name}");
                        continue;
                    }

                    var inputPort = GetInputPort(variableName);
                    if (inputPort == null)
                    {
                        // Internal constant for graph
                        continue;
                    }

                    variableActionNode.SetVariable(inputPort.GetInputValue());
                }
            }
            
            RunningGraph.GraphName = $"sub_{(GraphName.IsNullOrEmpty() ? RunningGraph.name : GraphName)}_{graphContext.EntryNode.FlowName}";
            RunningGraph.OnCallOutput += OnCallOutputHandler;
            await RunningGraph.Execute(graphContext.Context, null);
            RunningGraph.OnCallOutput -= OnCallOutputHandler;

            void OnCallOutputHandler(string outputName)
            {
                ExecuteFlowOutPort(outputName, graphContext, cancellationToken).Forget();
            }
        }

        private void OnDestroy()
        {
            var graphName = string.Empty;
            if (_graph != null)
            {
                graphName = _graph.GraphName;
            }
            BDebug.Log(LogCat.Graph, $"Destroying SubGraph {graphName}");
        }

        protected override void OnCanceled()
        {
            if (RunningGraph != null)
            {
                RunningGraph.Cancel();
            }
        }

        public void Resolve(IExposedPropertyTable resolver)
        {
            //cache resolver to pass it to graph 
        }
        
#if UNITY_EDITOR
        [SerializeField, HideInInspector] private List<string> _variableInputs = new(); 
        [SerializeField, HideInInspector] private List<string> _dynamicList = new(); 
        private void OnValidate()
        {
            if (_graph == null) return;
            foreach (var graphNode in _graph.nodes)
            {
                if (graphNode is OutputPortNode outputPortNode)
                {
                    if(_dynamicList.Contains(outputPortNode.OutputName)) continue;
                    _dynamicList.Add(outputPortNode.OutputName);
                    AddDynamicOutput(typeof(int), fieldName: outputPortNode.OutputName);
                    continue;
                }

                if (graphNode is not VariableActionNode variableNode ||
                    string.IsNullOrEmpty(variableNode.VariableName) ||
                    variableNode.VariableName.StartsWith("_") ||
                    _variableInputs.Contains(variableNode.VariableName)) continue;
                var variableInfo = variableNode.GetType().GetField("_variable", BindingFlags.NonPublic | BindingFlags.Instance);

                if (variableInfo == null)
                {
                    var baseType = variableNode.GetType().BaseType;
                    while (baseType != null && baseType != typeof(object))
                    {
                        variableInfo = baseType.GetField("_variable", BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.DeclaredOnly);
                        if (variableInfo != null)
                        {
                            break;
                        }
                        baseType = baseType.BaseType;
                    }
                }

                if (variableInfo == null) continue;
                var type = variableInfo.GetType();
                if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(ExposedReference<>))
                {
                    type = type.GetGenericArguments()[0];
                }
                AddDynamicInput(type, ConnectionType.Override, TypeConstraint.None, variableNode.VariableName);
                _variableInputs.Add(variableNode.VariableName);
            }
        }
#endif
    }
}
