using System.Collections.Generic;
using System.Threading;
using BebopBee.Core.Extensions;
using Cysharp.Threading.Tasks;
using UnityEngine;
using XNode;

namespace BBB.ActionGraph.VariableNodes.Nodes
{
    [CreateNodeMenu("Variables/Task Appearance")]
    public class TaskAppearanceActionNode : Node
    {
        [Input(ShowBackingValue.Never, ConnectionType.Override)]
        public int GameObject;

        [SerializeField]
        List<string> SpineAnimations;

        [SerializeField]
        public float _Order;

        [Output] public int Object;
        [Output] public int Animations;
        [Output] public float Order;

        public override object GetValue(NodePort port)
        {
            switch (port.fieldName)
            {
                case nameof(Object):
                    return GetInputValue<GameObject>(nameof(GameObject));
                case nameof(Animations):
                    return SpineAnimations;
                case nameof(Order):
                    return _Order;
            }

            return base.GetValue(port);
        }
    }
}
