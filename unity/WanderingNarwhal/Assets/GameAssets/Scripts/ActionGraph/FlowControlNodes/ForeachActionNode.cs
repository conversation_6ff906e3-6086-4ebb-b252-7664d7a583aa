using System.Collections;
using System.Threading;
using Cysharp.Threading.Tasks;
using XNode;

namespace BBB.ActionGraph.ActionNodes
{
    [CreateNodeMenu("Flow Control/Foreach")]
    public class ForeachActionNode : FlowInActionNode, IEntryActionNode
    {
        [Input(ShowBackingValue.Never, ConnectionType.Override)]
        public int Collection;

        [Input(ShowBackingValue.Unconnected, ConnectionType.Override)]
        public bool Parallel;

        [Output] public int Loop;
        [Output] public int Element;

        public string FlowName { get; set; }

        private ActionGraphContext _graphContext;

        private object _currentElement;
        private int _cyclesBeingExecuted;

        protected override async UniTask ExecuteInternal(ActionGraphContext graphContext,
            CancellationToken cancellationToken)
        {
            var collection = GetInputValue<IEnumerable>(nameof(Collection));
            if (collection == null)
            {
                return;
            }

            _graphContext = graphContext;

            var foreachGraphContext = new ActionGraphContext
            {
                Context = graphContext.Context,
                EntryNode = this,
            };

            if (Parallel)
            {
                await RunForeachInParallel(foreachGraphContext, collection, cancellationToken);
            }
            else
            {
                await RunForeachSequentially(foreachGraphContext, collection, cancellationToken);
            }
        }

        private async UniTask RunForeachSequentially(ActionGraphContext foreachGraphContext, IEnumerable collection,
            CancellationToken cancellationToken)
        {
            foreach (var element in collection)
            {
                await ExecuteSingleLoop(element, foreachGraphContext, cancellationToken);
            }
        }

        private async UniTask ExecuteSingleLoop(object element, ActionGraphContext foreachGraphContext, CancellationToken cancellationToken)
        {
            _cyclesBeingExecuted = 1;
            _currentElement = element;
            ExecuteFlowOutPort(nameof(Loop), foreachGraphContext, cancellationToken).Forget();
            await UniTask.WaitUntil(this,state => state.AreCyclesCompleted(), cancellationToken: cancellationToken);
        }

        private async UniTask RunForeachInParallel(ActionGraphContext foreachGraphContext, IEnumerable collection,
            CancellationToken cancellationToken)
        {
            foreach (var element in collection)
            {
                var foreachClone = this.CloneOutputGraph(nameof(Loop));
                foreachClone._graphContext = _graphContext;

                _cyclesBeingExecuted++;
                foreachClone.ExecuteLoop(element, foreachGraphContext, cancellationToken).Forget();
            }

            await UniTask.WaitUntil(this, state => state.AreCyclesCompleted(), cancellationToken: cancellationToken);
        }

        private bool AreCyclesCompleted()
        {
            return _cyclesBeingExecuted == 0;
        }

        private async UniTask ExecuteLoop(object element, ActionGraphContext graphContext, CancellationToken cancellationToken)
        {
            var foreachGraphContext = new ActionGraphContext
            {
                Context = graphContext.Context,
                EntryNode = this,
            };

            Parallel = false;
            await ExecuteSingleLoop(element, foreachGraphContext, cancellationToken);
            graphContext.EntryNode.FlowCompleted();
        }

        public override object GetValue(NodePort port)
        {
            return port.fieldName == nameof(Element) ? _currentElement : base.GetValue(port);
        }

        protected override void OnCanceled()
        {
            _currentElement = null;
        }

        public void FlowCompleted()
        {
            _cyclesBeingExecuted--;
        }

        public void CallOutput(string outputName)
        {
            _graphContext.EntryNode.CallOutput(outputName);
        }
    }
}
