using System;
using System.Collections;
using UnityEngine;

namespace BBB
{
    /// <summary>
    /// Coroutine runner.
    /// </summary>
    /// <remarks>
    /// todo:
    /// Problem: if implementation of this interface is unity object (almost always it is),
    /// and if reference to this object is stored in interface variable,
    /// then it is possible that object could be destroyed and there will be no easy way to check it,
    /// because if we use c# compare operators '==' or 'ReferenceEquals', then they all will return true (because object is still exist in memory, even if it's destroyed on scene),
    /// and only way to avoid this is to cast interface to unity base type and then use compare operator (which is overriden for unity objects). -VK
    ///
    /// Some null reference exception already occured in builds, but, fortunately, only on app closing.
    /// </remarks>
    public interface ICoroutineExecutor
    {
        Coroutine StartCoroutine(IEnumerator routine);

        void StopCoroutine(Coroutine routine);
    }

    public interface IUnityContainer
    {
        GameObject gameObject { get; }
    }

    public interface IUpdateDispatcher
    {
        event Action OnUpdate;
        event Action OnFixedUpdate;
        event Action OnLateUpdate;
    }
}