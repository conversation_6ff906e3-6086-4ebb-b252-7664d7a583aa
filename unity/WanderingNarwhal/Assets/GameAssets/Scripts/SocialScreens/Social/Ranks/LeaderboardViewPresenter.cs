using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.BrainCloud;
using BBB.Core;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.Navigation;
using BBB.Screens;
using BBB.Social.Ranks;
using BBB.UI;
using BBB.UI.LoopScrollRect;
using BebopBee;
using BebopBee.Core;
using BebopBee.Core.Audio;
using Bebopbee.Core.Extensions.Unity;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts;
using GameAssets.Scripts.SocialScreens.Teams;
using GameAssets.Scripts.SocialScreens.Teams.NoTeam;
using GameAssets.Scripts.UI.OverlayDialog;
using UnityEngine;
using UnityEngine.Serialization;

namespace BBB
{
    public class LeaderboardViewPresenter : ModalsViewPresenter, ILeaderboardViewPresenter, ICoroutineExecutor
    {
        public event Action<string> TabCategorySelected;

        [Header("Tab buttons")]
        [SerializeField] private TabButton _weeklyTab;
        [SerializeField] private TabButton _playersTab;
        [SerializeField] private TabButton _teamsTab;

        [SerializeField] private GameObject[] _subTabsHolder;
        [SerializeField] private GameObject _weeklyTop3Banner;
        [SerializeField] private TabButton _worldTab;
        [SerializeField] private TabButton _countryTab;
        [SerializeField] private GameObject _playerItemPrefab;
        [SerializeField] private RankUiItem[] _topBanners;
        [SerializeField] private GameObject _teamItemPrefab;

        [SerializeField] private Vector2 _scrollSizeDeltaWithoutSubTabs = Vector2.zero;
        [SerializeField] private Vector2 _scrollSizeDeltaWithSubTabs = new(0f, -105f);
        [SerializeField] private LoopScroll _loopScrollRect;
        [SerializeField] private LoopScrollAnimaionController _loopScrollRectAnimator;
        [SerializeField] private LoopScrollSnappedItemVisibilitySwitcher _loopScrollSnapItem;

        [SerializeField] private NoConnectionView _noConnectionView;

        [SerializeField] private ClockCountdownText _weeklyCountdownText;

        [SerializeField] private string _soundUidAnimatedOtherRankItemMoveDown = GenericSoundIds.ScrollTick;
        [SerializeField] private string _soundUidAnimatedPlayerItemScaleUp = GenericSoundIds.LevelSuccessPopupAppearing;
        [SerializeField] private string _soundUidAnimatedPlayerItemScaleDown = GenericSoundIds.GenericBurst;

        [SerializeField] private NotifierWidget _teamsNotifier;
        [SerializeField] private NotifierWidget _playersNotifier;
        [SerializeField] private NotifierWidget _weeklyNotifier;

        [SerializeField] private NotifierWidget _worldNotifier;
        [SerializeField] private NotifierWidget _countryNotifier;
        [FormerlySerializedAs("_speechBubbleConfig")] [SerializeField] private OverlayDialogConfig _overlayDialogConfig;
        [SerializeField] private Transform _floatingTextAnchor;

        private WeeklyLeaderboardRewardsManager _weeklyRewardsManager;
        private IAssetsManager _assetsManager;
        private ILevelsOrderingManager _levelsOrderingManager;
        private ILocalizationManager _localizationManager;
        private WeeklyLeaderboardManager _weeklyLeaderboardManager;
        private GameNotificationManager _notificationManager;
        private IAccountManager _accountManager;
        private ISocialManager _socialManager;
        private FlagsLoader _flagsLoader;
        private BrainCloudManager _brainCloudManager;
        private IOverlayDialogManager _overlayDialogManager;

        private readonly List<LeaderboardManager.Score> _scoresInfo = new();

        private string _lastTeamsCategory = LeaderboardFilterTypes.TeamsWorld;
        private string _lastPlayersCategory = LeaderboardFilterTypes.PlayersCountry;
        private string _currentCategory;

        private LeaderboardManager.FilteredScoresData _scores;
        private int _playerItemIndex;
        private bool _rewardSpeechBubbleShown;

        private bool _isInputLocked;
        private Action _debugCallback;

        public Transform FloatingTextAnchor => _floatingTextAnchor ?? transform;

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();

            _teamsTab.ReplaceOnClick(() => TabSelectedHandler(_lastTeamsCategory));
            _playersTab.ReplaceOnClick(() => TabSelectedHandler(_lastPlayersCategory));
            _weeklyTab.ReplaceOnClick(() => TabSelectedHandler(LeaderboardFilterTypes.PlayersWeekly));

            _worldTab.ReplaceOnClick(() =>
            {
                switch (_currentCategory)
                {
                    case LeaderboardFilterTypes.PlayersCountry or LeaderboardFilterTypes.PlayersWorld:
                        TabSelectedHandler(LeaderboardFilterTypes.PlayersWorld);
                        break;
                    case LeaderboardFilterTypes.TeamsCountry or LeaderboardFilterTypes.TeamsWorld:
                        TabSelectedHandler(LeaderboardFilterTypes.TeamsWorld);
                        break;
                }
            });

            _countryTab.ReplaceOnClick(() =>
            {
                switch (_currentCategory)
                {
                    case LeaderboardFilterTypes.PlayersCountry or LeaderboardFilterTypes.PlayersWorld:
                        TabSelectedHandler(LeaderboardFilterTypes.PlayersCountry);
                        break;
                    case LeaderboardFilterTypes.TeamsCountry or LeaderboardFilterTypes.TeamsWorld:
                        TabSelectedHandler(LeaderboardFilterTypes.TeamsCountry);
                        break;
                }
            });

            _weeklyRewardsManager = context.Resolve<WeeklyLeaderboardRewardsManager>();
            _assetsManager = context.Resolve<IAssetsManager>();
            _levelsOrderingManager = context.Resolve<ILevelsOrderingManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _weeklyLeaderboardManager = context.Resolve<WeeklyLeaderboardManager>();
            _notificationManager = context.Resolve<GameNotificationManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _socialManager = context.Resolve<ISocialManager>();
            _flagsLoader = context.Resolve<FlagsLoader>();
            _brainCloudManager = context.Resolve<BrainCloudManager>();

            _loopScrollRect.onItemSpawnedEvent -= ItemSpawnedHandler;
            _loopScrollRect.onItemSpawnedEvent += ItemSpawnedHandler;
            _loopScrollRect.onItemReleasedEvent -= ItemReleasedHandler;
            _loopScrollRect.onItemReleasedEvent += ItemReleasedHandler;
            _loopScrollRect.onDragStarted -= _overlayDialogManager.HideAllOverlayDialogs;
            _loopScrollRect.onDragStarted += _overlayDialogManager.HideAllOverlayDialogs;

            _teamsNotifier.Init(_notificationManager.GetNotifierByType(NotifierType.TeamsLeaderboardsNotifier));
            _playersNotifier.Init(_notificationManager.GetNotifierByType(NotifierType.PlayerLeaderboardsNotifier));
            _weeklyNotifier.Init(_notificationManager.GetNotifierByType(NotifierType.WeeklyLeaderboardsNotifier));
        }

        private void TabSelectedHandler(string category)
        {
            if (_isInputLocked) return;

            TabCategorySelected?.Invoke(category);
        }

        public void ShowNoConnection(string category)
        {
            ChangeCategory(category);
            
            ClearScrollList();
            _noConnectionView.Show();
        }

        public void ShowCategory(string category, LeaderboardManager.FilteredScoresData filteredScoresData, bool clear = true)
        {
            ChangeCategory(category, filteredScoresData);
            
            FillScrollList(clear);
            _loopScrollRect.SetVelocity(0f);
        }

        private void ChangeCategory(string category, LeaderboardManager.FilteredScoresData filteredScoresData = null)
        {
            _currentCategory = category;
            _scores = filteredScoresData;
            RefreshCategoryView(category);
            _noConnectionView.Hide();
        }

        /// <summary>
        /// UI for Weekly Leaderboard is a bit different as other leaderboards. Instead of shwoing all players in a list,
        /// it shows top 3 players in banners and then the rest of the players in a list, which is why the index needs
        /// to be adjusted to account for the users in the banners.
        /// </summary>
        /// <param name="currentCategory"></param>
        /// <returns></returns>
        public static int GetPlayerIndexOffsetForLeaderboard(string currentCategory)
        {
            switch (currentCategory)
            {
                case LeaderboardFilterTypes.PlayersWeekly:
                    return 3;
                default:
                    return 0;
            }
        }

        public void ShowDeltaAnimated(LeaderboardManager.FilteredScoresData filteredScoresData, int animatedDelta, Action completedCallback)
        {
            var playerIndex = -1;
            for (var i = 0; i < filteredScoresData.ScoresCount(); i++)
            {
                if (!filteredScoresData[i].IsOwnPlayer) continue;

                playerIndex = i;
                break;
            }

            if (!_loopScrollRect.gameObject.activeInHierarchy)
            {
                completedCallback?.Invoke();
                return;
            }

            var currentCategoryOffset = GetPlayerIndexOffsetForLeaderboard(_currentCategory);
            var currentIndex = playerIndex - animatedDelta;
            if (currentIndex < currentCategoryOffset)
            {
                RankUiItem rankUiItem = _topBanners[currentIndex];
                rankUiItem.PlayLevelUpAnimation();
                PlaySoundOnItemScaleUp();
            }

            _loopScrollRectAnimator.AnimatedMoveItemHighlightedWithAutofocus(
                playerIndex - currentCategoryOffset,
                playerIndex - animatedDelta - currentCategoryOffset,
                onBeforeItemScaleUp: OnItemStartScaleUpCallback,
                onBeforeItemScaleBack: OnItemStartScaleBackCallback,
                onOtherItemDidMoveDown: OnOtherItemDidMoveCallback,
                onDone: completedCallback);

            

            void OnItemStartScaleUpCallback(ILoopScrollListItem item)
            {
                if (item != null && item.isAlive)
                {
                    var uiItem = item.gameObject.GetComponent<RankUiItem>();
                    uiItem?.DecrementPositionInstant(animatedDelta);
                }

                PlaySoundOnItemScaleUp();
            }

            void OnItemStartScaleBackCallback(ILoopScrollListItem item)
            {
                if (item != null && item.isAlive)
                {
                    var uiItem = item.gameObject.GetComponent<RankUiItem>();
                    uiItem?.SetPlayRankProgressFx(true);
                }

                PlaySoundOnItemScaleBack();
            }

            void OnOtherItemDidMoveCallback(ILoopScrollListItem otherItem)
            {
                PlaySoundOnOtherItemMoved();
            }
        }

        public void FocusOnListItem(int listItemIndex)
        {
            var normPos = _loopScrollRect.CalcNormalizedItemPosition(listItemIndex - GetPlayerIndexOffsetForLeaderboard(_currentCategory));
            _loopScrollRect.SetContentNormalizedPosition(normPos);
        }

        public void SetInputLocked(bool isLocked)
        {
            _isInputLocked = isLocked;
        }

        public void SetSnappingEnabled(bool isSnappingEnabled)
        {
            _loopScrollSnapItem.IsPaused = !isSnappingEnabled;
        }

        private void ClearScrollList()
        {
            _loopScrollRect.contentModel.Clear();
            _loopScrollRect.ClearAllVisibleListItems();
            _loopScrollSnapItem.Clear();
            _playerItemIndex = -1;
        }

        private void FillScrollList(bool clear)
        {
            if (clear)
            {
                ClearScrollList();
            }

            _loopScrollSnapItem.SetPrefab(_currentCategory is LeaderboardFilterTypes.TeamsCountry or LeaderboardFilterTypes.TeamsWorld ? _teamItemPrefab : _playerItemPrefab);
            BDebug.Log(LogCat.Leaderboards, $"Filling leaderboard with {_scores.GetScores().Count} items");
            _scoresInfo.Clear();
            _scoresInfo.AddRange(_scores.GetScores());

            var prefab = _playerItemPrefab;
            if (_currentCategory is LeaderboardFilterTypes.TeamsCountry or LeaderboardFilterTypes.TeamsWorld)
            {
                if (_socialManager.CurrentTeam != null)
                {
                    _scores.FindPlayerInScoresWithIndex(_socialManager.CurrentTeam.TeamUid, out _playerItemIndex);
                }

                prefab = _teamItemPrefab;
            }
            else
            {
                _scores.FindPlayerInScoresWithIndex(_brainCloudManager.ProfileId, out _playerItemIndex);
                if (_playerItemIndex == -1)
                {
                    BDebug.LogWarning(LogCat.Leaderboards, $"Couldn't find own index with braincloud id: {_brainCloudManager.ProfileId} in scores: {_currentCategory}");
                    _scores.FindOwnScore(out _playerItemIndex);
                }
            }

            _loopScrollRect.contentModel.Clear();
            BDebug.Log(LogCat.Leaderboards, $"Player index: {_playerItemIndex} adding {_scoresInfo.Count} items to leaderboard");
            
            if(_currentCategory == LeaderboardFilterTypes.PlayersWeekly)
            {
                var currentCategoryOffset = GetPlayerIndexOffsetForLeaderboard(_currentCategory);

                var weeklyTop3Players = _scoresInfo.GetRange(0, Math.Min(currentCategoryOffset, _scoresInfo.Count));
                PopulateTop3Players(weeklyTop3Players);

                if (_scoresInfo.Count >= currentCategoryOffset)
                {
                    var remainingPlayers = _scoresInfo.GetRange(currentCategoryOffset, _scoresInfo.Count - currentCategoryOffset);

                    foreach (var info in remainingPlayers)
                    {
                        _loopScrollRect.contentModel.Add(new ListItemData()
                        {
                            prefab = prefab.GetComponent<ILoopScrollListItem>(),
                            model = info,
                        });
                    }
                }
            }
            else
            {
                foreach (var info in _scoresInfo)
                {
                    _loopScrollRect.contentModel.Add(new ListItemData()
                    {
                        prefab = prefab.GetComponent<ILoopScrollListItem>(),
                        model = info,
                    });
                }
            }
            

            _loopScrollRect.RefreshVisibleLayout();
            _loopScrollRect.ReinitializeVisibleListItems();
            if (_playerItemIndex >= GetPlayerIndexOffsetForLeaderboard(_currentCategory))
            {
                _loopScrollRect.ForceSpawnItem(_playerItemIndex - GetPlayerIndexOffsetForLeaderboard(_currentCategory), reSpawnIfAlreadyExists: false);
            }
            Rx.InvokeNextFrame(() => _loopScrollSnapItem.RecalculateContentPositionThresholds());
        }

        private void ItemSpawnedHandler(ILoopScrollListItem item, object model, int index)
        {
            var itemModel = (LeaderboardManager.Score)model;
            var isPlayerItem = item is RankUiItem;
            var indexWithOffset = index + GetPlayerIndexOffsetForLeaderboard(_currentCategory);
            if (indexWithOffset == _playerItemIndex && _playerItemIndex >= GetPlayerIndexOffsetForLeaderboard(_currentCategory))
            {

                _loopScrollSnapItem.SetTargetListItemsIndex(index, true, true);

                if (_loopScrollSnapItem.TopSnappedItemInstance != null)
                {
                    if (isPlayerItem)
                    {
                        InitPlayerItem(_loopScrollSnapItem.TopSnappedItemInstance.GetComponent<RankUiItem>(), _scoresInfo[indexWithOffset]);
                    }
                    else
                    {
                        InitTeamItem(_loopScrollSnapItem.TopSnappedItemInstance.GetComponent<TeamLeaderboardItem>(), _scoresInfo[indexWithOffset]);
                    }
                }

                if (_loopScrollSnapItem.BottomSnappedItemInstance != null)
                {
                    if (isPlayerItem)
                    {
                        InitPlayerItem(_loopScrollSnapItem.BottomSnappedItemInstance.GetComponent<RankUiItem>(), _scoresInfo[indexWithOffset]);
                    }
                    else
                    {
                        InitTeamItem(_loopScrollSnapItem.BottomSnappedItemInstance.GetComponent<TeamLeaderboardItem>(), _scoresInfo[indexWithOffset]);
                    }
                }
            }

            if (item is RankUiItem playerItem)
            {
                InitPlayerItem(playerItem, itemModel);
            }
            else if (item is TeamLeaderboardItem teamItem)
            {
                InitTeamItem(teamItem, _scoresInfo[indexWithOffset]);
            }
        }

        private void ItemReleasedHandler(ILoopScrollListItem item)
        {
            if (item is RankUiItem playerItem)
            {
                playerItem.RewardButtonClicked -= RewardButtonClickedHandler;
            }
        }

        private void InitPlayerItem(RankUiItem playerItem, LeaderboardManager.Score itemModel)
        {
            playerItem.Init(itemModel, Localization, _levelsOrderingManager, _assetsManager, _currentCategory, _weeklyRewardsManager, _loopScrollSnapItem);

            playerItem.RewardButtonClicked -= RewardButtonClickedHandler;
            playerItem.RewardButtonClicked += RewardButtonClickedHandler;
        }

        private void InitTeamItem(TeamLeaderboardItem teamItem, LeaderboardManager.Score itemModel)
        {
            teamItem.InitAsync(itemModel, _flagsLoader).Forget();
        }

        private void RewardButtonClickedHandler(string userId, Transform targetTransform)
        {
            var reward = userId == _accountManager.Profile.Uid
                ? _weeklyRewardsManager.GetBestOwnReward()
                : _weeklyRewardsManager.GetBestRewardFor(userId);
            _overlayDialogConfig.TargetTransform = targetTransform;
            _overlayDialogConfig.DisplayType = DisplayType.RewardSpeechBubble;
            _overlayDialogConfig.RewardToDisplay = reward;
            _overlayDialogManager.ToggleOverlayDialog(_overlayDialogConfig);
        }

        private void RefreshCategoryView(string category)
        {
            _overlayDialogManager.HideAllOverlayDialogs();
            var enableTeams = false;
            var enablePlayers = false;
            var enableWeekly = false;
            var showSubTabs = true;
            var showTop3Banners = false;

            switch (category)
            {
                case LeaderboardFilterTypes.PlayersWeekly:
                    enableWeekly = true;
                    showSubTabs = false;
                    showTop3Banners = true;
                    break;
                case LeaderboardFilterTypes.PlayersWorld:
                    _worldTab.Select();
                    _countryTab.Deselect();
                    _lastPlayersCategory = LeaderboardFilterTypes.PlayersWorld;
                    enablePlayers = true;
                    break;
                case LeaderboardFilterTypes.PlayersCountry:
                    _worldTab.Deselect();
                    _countryTab.Select();
                    _lastPlayersCategory = LeaderboardFilterTypes.PlayersCountry;
                    enablePlayers = true;
                    break;
                case LeaderboardFilterTypes.TeamsWorld:
                    _worldTab.Select();
                    _countryTab.Deselect();
                    _lastTeamsCategory = LeaderboardFilterTypes.TeamsWorld;
                    enableTeams = true;
                    break;
                case LeaderboardFilterTypes.TeamsCountry:
                    _worldTab.Deselect();
                    _countryTab.Select();
                    _lastTeamsCategory = LeaderboardFilterTypes.TeamsCountry;
                    enableTeams = true;
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(category), category, null);
            }

            _loopScrollRect.RectTransform().sizeDelta = enableWeekly ? _scrollSizeDeltaWithoutSubTabs : _scrollSizeDeltaWithSubTabs;
            _weeklyTab.Select(enableWeekly);
            _playersTab.Select(enablePlayers);
            _teamsTab.Select(enableTeams);

            _subTabsHolder.Enable(showSubTabs);
            _weeklyTop3Banner.SetActive(showTop3Banners);

            if (enablePlayers)
            {
                _worldNotifier.Init(_notificationManager.GetLeaderboardNotifier(LeaderboardNotifier.NotifierType.PlayersWorld));
                _countryNotifier.Init(_notificationManager.GetLeaderboardNotifier(LeaderboardNotifier.NotifierType.PlayersCountry));
            }
            else if (enableTeams)
            {
                _worldNotifier.Init(_notificationManager.GetLeaderboardNotifier(LeaderboardNotifier.NotifierType.TeamsWorld));
                _countryNotifier.Init(_notificationManager.GetLeaderboardNotifier(LeaderboardNotifier.NotifierType.TeamsCountry));
            }
        }

        public void SetupTimerView(bool weeklyLeaderBoardsEnded)
        {
            if (weeklyLeaderBoardsEnded)
            {
                _weeklyCountdownText.SetAsFinished(_localizationManager);
            }
            else
            {
                _weeklyCountdownText.Init(_localizationManager, _weeklyLeaderboardManager.GetTimeLeft);
            }
        }

        [ContextMenu("InvokeDebugAnimation")]
        private void InvokeDebugAnimation()
        {
            _debugCallback?.Invoke();
        }

        public void SetupDebugAnimationCallback(Action debugCallback)
        {
            _debugCallback = debugCallback;
        }

        private void PlaySoundOnItemScaleUp()
        {
            AudioProxy.PlaySound(_soundUidAnimatedPlayerItemScaleUp);
        }

        private void PlaySoundOnItemScaleBack()
        {
            AudioProxy.PlaySound(_soundUidAnimatedPlayerItemScaleDown);
        }

        private void PlaySoundOnOtherItemMoved()
        {
            AudioProxy.PlaySound(_soundUidAnimatedOtherRankItemMoveDown);
        }

        public void PopulateTop3Players(List<LeaderboardManager.Score> top3Players)
        {
            if (_weeklyTop3Banner != null)
            {
                for (int i = _topBanners.Length - 1; i >= 0; i--)
                {
                    RankUiItem rankUiItem = _topBanners[i];

                    if (rankUiItem != null)
                    {
                        if (top3Players.Count <= i)
                        {
                            rankUiItem.gameObject.SetActive(false);
                            continue;
                        }

                        rankUiItem.Init(
                            top3Players[i],
                            _localizationManager,
                            _levelsOrderingManager,
                            _assetsManager,
                            LeaderboardFilterTypes.PlayersWeekly,
                            _weeklyRewardsManager,
                            null
                        );

                        rankUiItem.RewardButtonClicked -= RewardButtonClickedHandler;
                        rankUiItem.RewardButtonClicked += RewardButtonClickedHandler;
                    }
                }
            }
        }
    }
}