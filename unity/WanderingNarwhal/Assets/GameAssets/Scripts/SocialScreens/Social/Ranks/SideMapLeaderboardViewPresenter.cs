using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.UI;
using BebopBee.UnityEngineExtensions;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.UI.OverlayDialog;
using TMPro;
using UnityEngine;
using UnityEngine.Profiling;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace GameAssets.Scripts.SocialScreens.Social.Ranks
{
    public class SideMapLeaderboardViewPresenter : EventLeaderboardViewPresenter
    {
        [SerializeField] private Image _bgImage;
        [SerializeField] private Transform _chestHolder;
        [SerializeField] private TextMeshProUGUI _subtitleText;
        [SerializeField] private Button _infoButton;
        [SerializeField] private TextMeshProUGUI[] _rankTexts;
        [SerializeField] private string _rankTextLocalisation = "LEADERBOARD_RANK";
        [Header("Won")]
        [SerializeField] private GameObject _wonHolder;
        [SerializeField] private Image _rewardImage;
        [SerializeField] private UIRewardComponent _rewardComponent;
        [SerializeField] private Button _rewardBoxButton;
        [Header("Lose")]
        [SerializeField] private GameObject _loseHolder;
        [FormerlySerializedAs("_speechBubbleConfig")] [SerializeField] private OverlayDialogConfig _overlayDialogConfig;

        private IGameEventResourceManager _gameEventResourceManager;
        protected override string WonFinalTextLoc => "GE_NEW_POPUP_CITY_WON";
        protected override string LostFinalTextLoc => "GE_NEW_POPUP_CITY_FAILURE";

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);

            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _infoButton.ReplaceOnClick(InfoButtonHandler);
            AllButtons.Add(_infoButton);
            if (_rewardBoxButton != null)
            {
                _rewardBoxButton.ReplaceOnClick(ShowCurrentReward);
            }
        }

        private void ShowCurrentReward()
        {
            var placeIndex = ViewModel.GetOwnPlace() - 1;
            var rewards = ViewModel.GetRewards(ViewModel.League, placeIndex);
            _overlayDialogConfig.DisplayType = DisplayType.RewardSpeechBubble;
            _overlayDialogConfig.RewardToDisplay = rewards;
            OverlayDialogManager.ToggleOverlayDialog(_overlayDialogConfig);
        }

        protected override void EnableButtons()
        {
            base.EnableButtons();
            if (ViewModel.IsEnded)
            {
                if (!ViewModel.IsWon())
                {
                    _claimButton.gameObject.SetActive(false);
                    _okayButton.gameObject.SetActive(true);
                }
                _centralOkayButton.gameObject.SetActive(false);
            }
            _subtitleText.gameObject.SetActive(!ViewModel.IsEnded);
        }

        public override void Refresh(EventLeaderboardViewModel viewModel)
        {
            EventDispatcher.AddListener<EventLeaderboardIntroComplete>(IntroCompleteHandler);

            ViewModel = viewModel;
            _leaderboardScrollView.Refresh(viewModel, null, OverlayDialogManager);

            _clockCountdownText.Init(LocalizationManager, viewModel.TimeLeftGetter);

            DisableButtons();
            _finalTextLocalized.gameObject.SetActive(false);
            _subtitleText.gameObject.SetActive(false);
            _wonHolder.SetActive(false);
            _loseHolder.SetActive(false);

            var gameEventSettings = _gameEventResourceManager.GetSettings(viewModel.EventUid);
            ApplyPalette(gameEventSettings.Palette);

            if (_eventName != null)
            {
                _eventName.text = viewModel.EventName;
            }

            if (_eventNames != null)
            {
                foreach (var eventName in _eventNames)
                {
                    eventName.text = viewModel.EventName;
                }
            }
            _subtitleText.text = viewModel.EventDescription;
            
            _gameEventResourceManager
                .GetSpriteAsync(viewModel.EventUid, GameEventResKeys.ModalBackgroundImage)
                .ContinueWith(sprite =>
                {
                    if (sprite == null)
                    {
                        BDebug.LogError(LogCat.Resources,$"Couldn't load ModalBackgroundImage for event {viewModel.EventUid}");
                    }
                    
                    _bgImage.sprite = sprite;
                });
            
            var chestPrefab = _gameEventResourceManager.GetGenericAsset<GameObject>(viewModel.EventUid, GameEventResKeys.LeaderboardRewardPrefab);
            if (_chestHolder.childCount == 0 || !_chestHolder.GetChild(0).name.StartsWith(chestPrefab.name))
            {
                _chestHolder.DestroyChildren();
                Profiler.BeginSample($"Instantiate[{chestPrefab.name}]");
                Instantiate(chestPrefab, _chestHolder, false);
                Profiler.EndSample();
            }

            OnFetchingDone(viewModel.Items, viewModel.League);
            var isEnded = viewModel.IsEnded;
            _chestHolder.gameObject.SetActive(!isEnded);//using for Chest for normal popup events and for Fennec in new popup event
        }

        protected override void RefreshHeaderForLeague(League league)
        {
            //have only one league and shouldn't hide/show anything
        }
        
        private void OnFetchingDone(List<EventLeaderboardItemBase> _, League __)
        {
            var place = ViewModel.GetOwnPlace();
            var rankText = LocalizationManager.getLocalizedTextWithArgs(_rankTextLocalisation, place);
            foreach (var rankTextField in _rankTexts)
            {
                rankTextField.text = rankText;
            }
            
            var isWon = ViewModel.IsWon();
            var isEnded = ViewModel.IsEnded;
            _wonHolder.SetActive(isEnded && isWon);
            _loseHolder.SetActive(isEnded && !isWon);
            if (isWon && isEnded)
            {
                if (_rewardImage != null)
                {
                    var spriteKey = EventLeaderboardItemView.PlaceToRibbonSpriteName(place);
                    _gameEventResourceManager
                        .GetSpriteAsync(ViewModel.EventUid, spriteKey)
                        .ContinueWith(sprite =>
                        {
                            if (sprite == null)
                            {
                                BDebug.LogError(LogCat.Resources,$"Couldn't load ribbon sprite '{spriteKey}' for event {ViewModel.EventUid}");
                            }
                            _rewardImage.sprite = sprite;
                        });                }
                
                if (_rewardComponent != null)
                {
                    var placeIndex = place - 1;
                    var rewards = ViewModel.GetRewards(ViewModel.League, placeIndex);
                    if (rewards.Count > 1) //multiple rewards, leave rewardImage box
                    {
                        _rewardComponent.gameObject.SetActive(false);
                    }
                    else
                    {
                        _rewardComponent.SetupReward(ViewModel.GetRewards(ViewModel.League, placeIndex));
                        _rewardComponent.gameObject.SetActive(true);
                        if (_rewardImage != null)
                        {
                            _rewardImage.gameObject.SetActive(false);
                        }
                    }
                }
            }
        }
    }
}