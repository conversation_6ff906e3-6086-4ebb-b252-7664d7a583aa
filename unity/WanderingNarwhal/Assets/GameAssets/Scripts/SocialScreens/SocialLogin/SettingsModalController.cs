using UnityEngine;
using BBB.UI;
using BBB.DI;
using BebopBee;
using System.Collections;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.Crash;
using BBB.Generic.Modal;
using BebopBee.Core.Audio;
using BBB.Quests;
using BebopBee.Core.UI;
using Core.RPC;
using GameAssets.Scripts.Core;

#if UNITY_IOS
using UnityEngine.iOS;
#endif

namespace BBB
{
    // TODO consider itroducing and using genralized variant of SocialScreenTabManager for this tab-based view too
    public class SettingsModalController : BaseModalsController<ISocialLoginViewPresenter>
    {
        private IAccountManager _accountManager;
        private ILocalizationManager _localizationManager;
        private SystemLanguage _currentLanguage;
        private UserSettings _userSettings;
        private IRestartable _restarter;
        private GenericModalFactory _genericModalFactory;
        private IAP.IapManager _iapManager;
        private ILivesManager _livesManager;
        private IExternalLinksOpener _externalLinksOpener;
        private IHelpDeskManager _helpDeskManager;

        private Coroutine _delayRoutine;
        private ChangeNameModalController _changeNameModalController;

        //We can put some callbacks for the screen tabs here in Init args
        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);

            _accountManager = context.Resolve<IAccountManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _userSettings = context.Resolve<UserSettings>();
            _restarter = context.Resolve<IRestartable>();
            _genericModalFactory = context.Resolve<GenericModalFactory>();
            _iapManager = context.Resolve<IAP.IapManager>();
            _livesManager = context.Resolve<ILivesManager>();
            _externalLinksOpener = context.Resolve<IExternalLinksOpener>();
            _helpDeskManager = context.Resolve<IHelpDeskManager>();
        }

        private void Setup()
        {
            _currentLanguage = _localizationManager.getCurrentLanguage();
            var languages = new List<SocialLoginViewPresenter.LanguageData>();
            var languagesDict = LanguageHelper.GetRLLanguages();
            foreach (var item in languagesDict)
            {
                var languageItem = new SocialLoginViewPresenter.LanguageData(null, item.Key);
                languages.Add(languageItem);
            }

            DoWhenReady(() =>
            {
                View.Setup(_accountManager.Profile, languages);
                View.InitViews(OnSelect, _currentLanguage);
                BDebug.Log("Settings panel is active.");
            });
        }

        protected override void OnShow()
        {
            base.OnShow();
            Subscribe();
            Setup();
        }

        protected override void OnHide()
        {
            base.OnHide();
            Unsubscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();
            View.OnCloseClicked += OnCloseClickedHandler;
            View.OnCustomExitButton += ViewOnOnCustomExitButton;
            View.OnNextLanguage += OnLanguageChanged;
            View.OnHelpClicked += ViewOnOnHelpClicked;
            View.OnJoinNowClicked += ViewOnOnJoinNowClicked;
            
            View.OnPlatformLogoutClicked += HandleLogoutClicked;

            View.OnResetAccount += ViewOnResetAccountClicked;

            View.OnSocialFacebookGroupClicked += SocialFacebookGroupClickedHandler;
            View.OnSocialFacebookClicked += ViewOnOnSocialFacebookClicked;
            View.OnPrivacyPolicyClicked += ViewOnOnPrivacyPolicyClicked;
            View.OnSocialInstagramClicked += ViewOnOnSocialInstagramClicked;

            View.RestoreIAPClicked += RestoreIAPClickedHandler;
            View.OnTermsServiceButton += ViewOnTermsServiceClicked;
            View.ChangeNameButtonClicked += ShowChangeNameModal;
        }

        private void ShowChangeNameModal()
        {
            if (TryHandleConnectionProblem()) return;

            _changeNameModalController = ModalsBuilder.CreateModalView<ChangeNameModalController>(ModalsType.ChangeName);

            _changeNameModalController.OnClose -= ChangeNameModalCloseHandler;
            _changeNameModalController.OnClose += ChangeNameModalCloseHandler;

            _changeNameModalController.ShowModal(ShowMode.Delayed);
            Hide();
        }

        private void ChangeNameModalCloseHandler()
        {
            _changeNameModalController.OnClose -= ChangeNameModalCloseHandler;
            ShowModal(ShowMode.Delayed);
        }

        private void ViewOnTermsServiceClicked()
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Terms, string.Empty));
            PlayButtonTapSfx();

            if (TryHandleConnectionProblem()) return;
            
            _externalLinksOpener.OpenTermsOfService();
        }

        private async void RestoreIAPClickedHandler()
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Restore, string.Empty));
            PlayButtonTapSfx();

            if (TryHandleConnectionProblem()) return;

            var alreadyRestoreLifeLifter = _livesManager.MaxLives >= 5;
            var onResolved = await _iapManager.RestorePurchasesAsync();
            
            if (onResolved) return;

            const string alreadyRestored = "IAP_ALREADY_RESTORED_PRODUCTS";
            const string nothingRestored = "IAP_NOTHING_RESTORED_PRODUCTS";

            View.DisplayFloatingText(alreadyRestoreLifeLifter ? alreadyRestored : nothingRestored);
        }

        private void ViewOnOnCustomExitButton()
        {
            _userSettings.Save();
            Hide();
        }

        private void OnSelect(SystemLanguage language)
        {
            _currentLanguage = language;
            _localizationManager.setCurrentLanguage(_currentLanguage);
            View.SetLanguage(_currentLanguage);
            CrashLoggerService.Log("Restart due to language change in modal");
            _restarter.Restart();
        }

        private void OnLanguageChanged()
        {
            var languagesDict = LanguageHelper.GetRLLanguages();
            var languageList = new List<SystemLanguage>(languagesDict.Keys);

            var nextKey = SystemLanguage.English;
            var foundCurrent = false;

            for (var i = 0; i < languageList.Count; i++)
            {
                if (languageList[i] != _currentLanguage) continue;

                nextKey = i + 1 < languageList.Count ? languageList[i + 1] : languageList[0];
                foundCurrent = true;
                break;
            }

            if (!foundCurrent && languageList.Count > 0)
            {
                nextKey = languageList[0];
            }

            _currentLanguage = nextKey;
            PlayButtonTapSfx();
        }

        private void ViewOnOnSocialInstagramClicked()
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Insta, string.Empty));
            PlayButtonTapSfx();
            if (_delayRoutine != null)
                return;

            _delayRoutine = CoroutineExecutor.StartCoroutine(DelayRoutine(1f));
            Application.OpenURL(SocialLinks.GameInstagramUrl);
        }


        private void ViewOnOnPrivacyPolicyClicked()
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Privacy, string.Empty));
            PlayButtonTapSfx();

            if (_delayRoutine != null || TryHandleConnectionProblem()) return;

            _delayRoutine = CoroutineExecutor.StartCoroutine(DelayRoutine(1f));
            
            _externalLinksOpener.OpenPrivacyPolicyPage();
        }

        private void SocialFacebookGroupClickedHandler()
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.FbGroup, string.Empty));
            PlayButtonTapSfx();
            if (_delayRoutine != null)
                return;

            _externalLinksOpener.OpenFacebookGroup();
            _delayRoutine = CoroutineExecutor.StartCoroutine(DelayRoutine(1f));
        }

        private void ViewOnOnSocialFacebookClicked()
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Fb, string.Empty));
            PlayButtonTapSfx();
            if (_delayRoutine != null)
                return;

            _externalLinksOpener.OpenFacebook();
            _delayRoutine = CoroutineExecutor.StartCoroutine(DelayRoutine(1f));
        }

        private IEnumerator DelayRoutine(float time)
        {
            while (time > 0)
            {
                time -= Time.deltaTime;
                yield return null;
            }

            _delayRoutine = null;
        }

        private bool TryHandleConnectionProblem()
        {
            if (ConnectivityStatusManager.ConnectivityReachable) return false;

            _genericModalFactory.ShowNoConnectionModal();
            return true;
        }
        
        private void HandleLogoutClicked()
        {
            PlayButtonTapSfx();

            if (TryHandleConnectionProblem()) return;

            const string titleLoc = "GENERIC_LOGOUT_TITLE";
            const string descLoc = "GENERIC_LOGOUT_CONFIRMATION";

            _genericModalFactory.ShowWithOkCloseButton(_localizationManager.getLocalizedText(titleLoc),
                _localizationManager.getLocalizedText(descLoc),
                idx =>
                {
                    if (idx != 1 || TryHandleConnectionProblem()) return;

                    _accountManager.LogoutFromPlatforms();
                });
        }

        private void ViewOnResetAccountClicked()
        {
            PlayButtonTapSfx();

            if (TryHandleConnectionProblem()) return;

            const string titleLoc = "RESET_ACCOUNT_TITLE";
            const string descLoc = "RESET_ACCOUNT_MESSAGE";

            _genericModalFactory.ShowWithOkCloseButton(_localizationManager.getLocalizedText(titleLoc),
                _localizationManager.getLocalizedText(descLoc),
                idx =>
                {
                    if (idx != 1 || TryHandleConnectionProblem()) return;

                    _accountManager.ResetProgression();
                });
        }

        private void ViewOnOnJoinNowClicked()
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Community, string.Empty));
            PlayButtonTapSfx();
            _externalLinksOpener.OpenCommunityPage();
        }

        private async void ViewOnOnHelpClicked()
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Faq, string.Empty));
            PlayButtonTapSfx();

            await _helpDeskManager.ShowTicketOrHelp();
        }

        private void PlayButtonTapSfx()
        {
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
        }

        private void Unsubscribe()
        {
            View.OnCloseClicked -= OnCloseClickedHandler;
            View.OnCustomExitButton -= ViewOnOnCustomExitButton;
            View.OnNextLanguage -= OnLanguageChanged;
            View.OnHelpClicked -= ViewOnOnHelpClicked;
            View.OnJoinNowClicked -= ViewOnOnJoinNowClicked;
            
            View.OnPlatformLogoutClicked -= HandleLogoutClicked;

            View.OnResetAccount -= ViewOnResetAccountClicked;

            View.OnSocialFacebookGroupClicked -= SocialFacebookGroupClickedHandler;
            View.OnSocialFacebookClicked -= ViewOnOnSocialFacebookClicked;
            View.OnPrivacyPolicyClicked -= ViewOnOnPrivacyPolicyClicked;
            View.OnSocialInstagramClicked -= ViewOnOnSocialInstagramClicked;

            View.RestoreIAPClicked -= RestoreIAPClickedHandler;
            View.OnTermsServiceButton -= ViewOnTermsServiceClicked;
            View.ChangeNameButtonClicked -= ShowChangeNameModal;
        }

        private void OnCloseClickedHandler()
        {
            _userSettings.Save();
            Hide();
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            Unsubscribe();
            if (_changeNameModalController != null)
            {
                _changeNameModalController.OnClose -= ChangeNameModalCloseHandler;
            }
        }
    }
}