using System;
using BBB;
using Bebopbee.Core.Extensions.Unity;
using DG.Tweening;
using GameAssets.Scripts.SocialScreens.Teams.IceBreaker;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.SocialScreens.Teams.Screens
{
    public class IceBreakerPanel : BbbMonoBehaviour
    {
        [SerializeField] private LocalizedTextPro _titleText;
        [SerializeField] private LocalizedTextPro _questionText;
        [SerializeField] private GameObject[] _highlightHolders;
        [SerializeField] private Button _button;
        [SerializeField] private NotifierWidget _notifierWidget;
        [SerializeField] private GameObject _alreadyAnsweredHolder;
        [SerializeField] private AsyncLoadableImage _characterIcon;
        [SerializeField] private GameObject[] _alreadyAnsweredHolders;
        [SerializeField] private GameObject[] _notAnsweredHolders;
        [SerializeField] private Animator _animator;

        private Action _clickCallback;
        private Tweener _autoHideTweener;
        private static readonly int QuestionTrigger = Animator.StringToHash("Question");
        private static readonly int AnswerTrigger = Animator.StringToHash("Answer");
        private static readonly int HideTrigger = Animator.StringToHash("Hide");

        private void Start()
        {
            _button.ReplaceOnClick(ClickHandler);
        }

        private void ClickHandler()
        {
            _clickCallback?.Invoke();
        }
        
        public void ShowIceBreakerQuestionScreenAnimation()
        {
            _animator.SetTrigger(QuestionTrigger);
        }
        
        public void CloseIceBreakerAnswersScreenAnimation()
        {
            _animator.SetTrigger(HideTrigger);
        }
        
        public void ShowIceBreakerAnswersScreenAnimation()
        {
            _animator.SetTrigger(AnswerTrigger);
        }

        public void Setup(IceBreakerQuestion question, Action clickCallback, GameNotificationManager notificationManager)
        {
            _titleText.SetTextId(question.Title);
            _questionText.SetTextId(question.Question);
            _characterIcon.Show(question.CharacterIcon);
            _clickCallback = clickCallback;
            _notifierWidget.Init(notificationManager.GetIceBreakerNotifier());
        }

        public void ShowHighlight(bool show)
        {
            _highlightHolders.Enable(show);
        }
        
        public void ToggleGiftBoxReward(bool show)
        {
            _alreadyAnsweredHolders.Enable(!show);
            _notAnsweredHolders.Enable(show);
        }

        public void Show()
        {
            _autoHideTweener?.Kill();
            _alreadyAnsweredHolder.SetActive(false);

            gameObject.SetActive(true);
        }

        public void Hide()
        {
            gameObject.SetActive(false);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _clickCallback = null;
        }
    }
}