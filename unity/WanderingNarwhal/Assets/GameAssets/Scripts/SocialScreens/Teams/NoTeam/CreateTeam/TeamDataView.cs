using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.Navigation;
using BBB.UI;
using BBB.UI.Core;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.SocialScreens.Teams.Screens.LogoSelectionScreen;
using GameAssets.Scripts.SocialScreens.Teams.TeamInfo;
using GameAssets.Scripts.UI.OverlayDialog;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Object = System.Object;

namespace GameAssets.Scripts.SocialScreens.Teams.NoTeam.CreateTeam
{
    public class TeamDataView : ContextedUiBehaviour
    {
        [SerializeField] private TeamLogoSelectionScreen _teamLogoSelectionScreen;
        [SerializeField] private Button[] _chooseLogoButton;
        [SerializeField] private TeamLogoView _teamLogoView;
        [SerializeField] private string _defaultTeamLogo = "logo_1";

        [SerializeField] private int _minCharacters = 5;
        [SerializeField] private int _maxCharacters = 30;
        [SerializeField] private TextMeshProUGUI _minCharactersLimitText;

        [SerializeField] private TMP_InputField _teamNameInputField;
        [SerializeField] private TMP_InputField _teamDescriptionInputField;
        [SerializeField] private SettingsSwitcherWidget _teamTypeSwitcher;
        [SerializeField] private SettingsSwitcherWidget _requiredLevelSwitcher;
        [SerializeField] private Transform _overlayAnchor;
        [SerializeField] private string _comingSoonId;
        [SerializeField] private string _requiredLevelExceededId;

        private ILocalizationManager _localizationManager;
        private string _logoUid;
        private ILocationManager _locationManager;
        private ILevelsOrderingManager _levelsOrderingManager;
        private IOverlayDialogManager _overlayDialogManager;
        
        private readonly OverlayDialogConfig _overlayDialogConfig = new();

        private readonly Dictionary<TeamType, string> _teamTypeIds = new()
        {
            { TeamType.Public, TeamPublicInfo.CreateTeamTypePublic },
            { TeamType.Private, TeamPublicInfo.CreateTeamTypePrivate }
        };

        public string TeamName => _teamNameInputField.text;
        public string TeamDescription => _teamDescriptionInputField.text;
        public string LogoUid => _logoUid;
        public int RequiredLevel => _requiredLevelSwitcher.CurrentValue;
        public TeamType TeamType => TeamType.Public;//todo change to _teamTypeSwitcher.CurrentValue

        protected override void InitWithContextInternal(IContext context)
        {
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();
            _locationManager = context.Resolve<ILocationManager>();
            _levelsOrderingManager = context.Resolve<ILevelsOrderingManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _chooseLogoButton.ReplaceOnClick(ChooseLogoHandler);
        }

        private void Start()
        {
            LazyInit();
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            
            _teamLogoSelectionScreen.Hide();
        }

        private void TeamLogoSelectedHandler(string logoUid)
        {
            if (logoUid.IsNullOrEmpty())
                logoUid = _defaultTeamLogo;
            
            _teamLogoView.Setup(logoUid);
            _logoUid = logoUid;
        }

        private void ChooseLogoHandler()
        {
            _teamLogoSelectionScreen.Setup(TeamLogoSelectedHandler);
            _teamLogoSelectionScreen.Show();
        }

        public void SetFields(string teamName, string teamDescription, string logoUid, int requiredLevel, TeamType teamType)
        {
            _teamNameInputField.text = teamName;
            _teamDescriptionInputField.text = teamDescription;
            _teamLogoView.Setup(logoUid);
            _logoUid = logoUid;
            SetupRequiredLevelSwitcher(requiredLevel);
            SetupTeamTypeSwitcher(teamType);
            _minCharactersLimitText.gameObject.SetActive(false);
        }

        private void SetupTeamTypeSwitcher(TeamType teamType)
        {
            _teamTypeSwitcher.CurrentValue = (int)teamType;
            _teamTypeSwitcher.Setup(0, Enum.GetValues(typeof(TeamType)).Length - 1);
            SetTeamTypeText(teamType);
            SubscribeToTeamTypeSwitcher();
        }

        private void SubscribeToTeamTypeSwitcher()
        {
            UnsubscribeFromTeamTypeSwitcher();
            _teamTypeSwitcher.OnSwitched += TeamTypeSwitchHandler;
        }

        private void UnsubscribeFromTeamTypeSwitcher()
        {
            if (_teamTypeSwitcher != null)
            {
                _teamTypeSwitcher.OnSwitched -= TeamTypeSwitchHandler;
            }
        }

        private void TeamTypeSwitchHandler(int teamType)
        {
            var type = (TeamType)teamType;
            SetTeamTypeText(type);
            
            //todo remove after adding support for closed type
            if (type == TeamType.Private)
            {
                _overlayDialogConfig.DisplayType = DisplayType.FloatingText;
                _overlayDialogConfig.TargetTransform = _overlayAnchor;
                _overlayDialogConfig.ShowBackground = true;
                _overlayDialogConfig.TextToDisplay = _comingSoonId;
                _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
                SwitchTypeBackAfterDelayAsync().Forget();
            }
        }

        //todo remove after adding support for closed type
        private async UniTaskVoid SwitchTypeBackAfterDelayAsync()
        {
            await UniTask.Delay(TimeSpan.FromSeconds(0.5f));
            SetTeamTypeText(TeamType.Public);
            _teamTypeSwitcher.CurrentValue = (int)TeamType.Public;
        }

        private void SetTeamTypeText(TeamType teamType)
        {
            if (!_teamTypeIds.TryGetValue(teamType, out var typeId))
            {
                BDebug.LogError(LogCat.Social,$"Couldn't find localization id for team type: {teamType}");
                typeId = _teamTypeIds[TeamType.Public];
            }
            _teamTypeSwitcher.SetLocalizedTextId(typeId);
        }

        private void SetupRequiredLevelSwitcher(int requiredLevel)
        {
            _requiredLevelSwitcher.CurrentValue = requiredLevel;
            var highestPassedLevelUid = _locationManager.MainProgressionLocation.GetHighestPassedLevelUid();
            const int minCap = 0;
            if (!_levelsOrderingManager.TryParseLevelUid(highestPassedLevelUid, out var _, out var number))
            {
                BDebug.LogError(LogCat.Player,$"Couldn't find level state with uid: {highestPassedLevelUid}");
                number = minCap;
            }
            _requiredLevelSwitcher.Setup(minCap, number);
            _requiredLevelSwitcher.SetText(requiredLevel.ToString());
            
            SubscribeToLevelSwitcher();
        }

        private void SubscribeToLevelSwitcher()
        {
            UnsubscribeFromLevelSwitcher();
            _requiredLevelSwitcher.OnSwitched += RequiredLevelSwitchHandler;
            _requiredLevelSwitcher.OnMaxCapExceeded += RequiredLevelCapExceededHandler;
            
        }

        private void UnsubscribeFromLevelSwitcher()
        {
            if (_requiredLevelSwitcher != null)
            {
                _requiredLevelSwitcher.OnSwitched -= RequiredLevelSwitchHandler;
                _requiredLevelSwitcher.OnMaxCapExceeded -= RequiredLevelCapExceededHandler;
            }
        }

        private void RequiredLevelCapExceededHandler(int nextThreshold)
        {
            _overlayDialogConfig.DisplayType = DisplayType.FloatingText;
            _overlayDialogConfig.TargetTransform = _overlayAnchor;
            _overlayDialogConfig.ShowBackground = true;
            _overlayDialogConfig.TextToDisplay = _requiredLevelExceededId;
            _overlayDialogConfig.TextArgs = new object[] { nextThreshold };
            _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
        }

        private void RequiredLevelSwitchHandler(int level)
        {
            _requiredLevelSwitcher.SetText(level.ToString());
        }

        public bool TryValidate()
        {
            var teamName = _teamNameInputField.text.Trim();
            if (teamName.Length < _minCharacters || teamName.Length > _maxCharacters)
            {
                _minCharactersLimitText.text = _localizationManager.getLocalizedTextWithArgs("CREATE_TEAM_NAME_LIMIT_TEXT", _minCharacters, _maxCharacters);
                _minCharactersLimitText.gameObject.SetActive(true);
                return false;
            }

            return true;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            UnsubscribeFromLevelSwitcher();
            UnsubscribeFromTeamTypeSwitcher();
        }
    }
}