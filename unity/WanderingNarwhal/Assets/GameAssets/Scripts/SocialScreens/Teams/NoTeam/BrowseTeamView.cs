using System;
using System.Collections.Generic;
using BBB;
using Bebopbee.Core.Extensions.Unity;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.SocialScreens.Teams.NoTeam.CreateTeam;
using GameAssets.Scripts.SocialScreens.Teams.TeamInfo;
using UnityEngine;

namespace GameAssets.Scripts.SocialScreens.Teams.NoTeam
{
    public class BrowseTeamView : BbbMonoBehaviour
    {
        public enum NoTeamTabs
        {
            Join,
            Search,
            Create
        }

        [SerializeField] private GameObject[] _rootHolder;

        [SerializeField] private TabButton _joinTeamTabButton;
        [SerializeField] private TabButton _searchTeamTabButton;
        [SerializeField] private TabButton _createTeamTabButton;

        [SerializeField] private JoinTeamView _joinTeamView;
        [SerializeField] private SearchTeamView _searchTeamView;
        [SerializeField] private CreateTeamView _createTeamView;

        private Action<Action, Action<List<TeamPublicInfo>>> _browseTeamsRequestCallback;
        private Action<string, Action, Action<List<TeamPublicInfo>>> _searchTeamsRequestCallback;
        private Action<TeamCreationData> _createTeamRequestCallback;
        private Action<TeamPublicInfo> _showTeamInfoCallback;
        private NoTeamTabs _currentTab = NoTeamTabs.Join;
        private bool _refreshJoinTeamView = true;
        private readonly List<TeamPublicInfo> _teams = new ();

        private void Awake()
        {
            _joinTeamTabButton.ReplaceOnClick(() => SelectTab(NoTeamTabs.Join));
            _searchTeamTabButton.ReplaceOnClick(() => SelectTab(NoTeamTabs.Search));
            _createTeamTabButton.ReplaceOnClick(() => SelectTab(NoTeamTabs.Create));
        }

        public void Setup(Action<Action, Action<List<TeamPublicInfo>>> browseTeamsRequestCallback, Action<string, Action, Action<List<TeamPublicInfo>>> searchTeamsRequestCallback,
            Action<TeamCreationData> createTeamRequestCallback, Action<TeamPublicInfo> showTeamInfoCallback, List<TeamPublicInfo> teams)
        {
            _browseTeamsRequestCallback = browseTeamsRequestCallback;
            _searchTeamsRequestCallback = searchTeamsRequestCallback;
            _createTeamRequestCallback = createTeamRequestCallback;
            _showTeamInfoCallback = showTeamInfoCallback;
            RefreshTeamsList(teams);
        }

        public void Show()
        {
            _rootHolder.Enable(true);
            _refreshJoinTeamView = true;
            SelectTab(_currentTab, true);
        }

        public void Hide()
        {
            _rootHolder.Enable(false);

            _joinTeamView.Hide();
            _searchTeamView.Hide();
            _createTeamView.Hide();
            if (ConnectivityStatusManager.ConnectivityReachable)
            {
                FetchAndSetupTeams(false);
            }
        }

        public void SelectTab(NoTeamTabs tab, bool force = false)
        {
            if (!force && tab == _currentTab)
                return;

            _joinTeamView.Hide();
            _searchTeamView.Hide();
            _createTeamView.Hide();

            _joinTeamTabButton.Deselect();
            _searchTeamTabButton.Deselect();
            _createTeamTabButton.Deselect();

            _currentTab = tab;
            switch (tab)
            {
                case NoTeamTabs.Join:
                    _joinTeamView.Show();
                    _joinTeamTabButton.Select();
                    if (_refreshJoinTeamView)
                    {
                        if (_teams.Count > 0)
                        {
                            SetupTeamsView();
                        }
                        else
                        {
                            FetchAndSetupTeams();
                        }
                    }
                    break;
                case NoTeamTabs.Search:
                    _searchTeamView.SetupTeams(_searchTeamsRequestCallback, _showTeamInfoCallback);
                    _searchTeamView.Show();
                    _searchTeamTabButton.Select();
                    break;
                case NoTeamTabs.Create:
                    _createTeamView.Setup(TeamCreationRequestHandler);
                    _createTeamView.Show();
                    _createTeamTabButton.Select();
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(tab), tab, null);
            }
        }

        private void RefreshTeamsList(List<TeamPublicInfo> teams)
        {
            if (teams.Count == 0) 
                return;
            
            _teams.Clear();
            foreach (var teamPublicInfo in teams)
            {
                _teams.Add(teamPublicInfo);
            }
        }

        private void SetupTeamsView()
        {
            _joinTeamView.SetupTeams(_teams, _showTeamInfoCallback);
        }

        private void FetchAndSetupTeams(bool setupView = true)
        {
            _browseTeamsRequestCallback?.Invoke(() =>
            {
                if (!setupView) 
                    return;
                
                _refreshJoinTeamView = false;
                _joinTeamView.StartLoading();
            }, teams =>
            {
                RefreshTeamsList(teams);
                if (setupView)
                {
                    SetupTeamsView();
                }
            });
        }

        private void TeamCreationRequestHandler(TeamCreationData teamCreationData)
        {
            _createTeamRequestCallback?.Invoke(teamCreationData);
        }

        public void RemoveTeamFromCachedList(string teamUid)
        {
            _teams.RemoveAll(team => team.Id == teamUid);
        }
    }
}