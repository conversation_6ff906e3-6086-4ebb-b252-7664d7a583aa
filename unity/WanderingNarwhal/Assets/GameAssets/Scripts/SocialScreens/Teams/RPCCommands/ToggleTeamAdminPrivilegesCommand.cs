using Bebopbee.Core.Systems.RpcCommandManager;
using RPC.Teams;

namespace GameAssets.Scripts.SocialScreens.Teams.RPCCommands
{
    public class ToggleTeamAdminPrivilegesCommand : RpcCommand<ToggleTeamAdminPrivileges>
    {
        protected override void Parse(object[] args)
        {
            base.Parse(args);
            Dto.TeamUid = (string)args[0];
            Dto.UserId = (string)args[1];
        }

        protected override int GetExpectedArgumentsCount()
        {
            return 2;
        }
    }
}