using System;
using BBB;
using Cysharp.Threading.Tasks;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages
{
    public class CollapsableMessageText : BbbMonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI _messageText;
        [SerializeField] private bool _collapseTextOnOverflowing;
        [SerializeField] private Button _expandButton;
        [SerializeField] private Button _collapseButton;
        [SerializeField] private GameObject _expandButtonHolder;
        [SerializeField] private GameObject _collapseButtonHolder;

        private Action _layoutRefreshCallback;

        public void Init(Action layoutRefreshCallback)
        {
            _layoutRefreshCallback = layoutRefreshCallback;

            _expandButtonHolder.SetActive(false);
            _collapseButtonHolder.SetActive(false);

            if (_collapseTextOnOverflowing)
            {
                _messageText.enableWordWrapping = false;
                _expandButton.ReplaceOnClick(ExpandButtonHandler);
                _collapseButton.ReplaceOnClick(CollapseButtonHandler);
            }
        }

        public async void CheckCollapseState()
        {
            if (!_collapseTextOnOverflowing)
                return;

            await UniTask.NextFrame();
            if (_messageText.isTextTruncated || _messageText.isTextOverflowing)
            {
                Collapse(true);
            }
        }

        private void ExpandButtonHandler()
        {
            Collapse(false);
        }

        private void CollapseButtonHandler()
        {
            Collapse(true);
        }

        private void Collapse(bool collapse)
        {
            _expandButtonHolder.SetActive(collapse);
            _collapseButtonHolder.SetActive(!collapse);

            var shouldWordWrap = !collapse;
            if (_messageText.enableWordWrapping != shouldWordWrap)
            {
                _messageText.enableWordWrapping = shouldWordWrap;
                _layoutRefreshCallback?.Invoke();
            }
        }
    }
}