using BBB.Core.ResourcesManager.Asset;
using BBB.DI;
using BBB.Screens;
using UnityEngine;

namespace BBB.UI.BottomBar.Views
{
    public interface IBaseNavigationViewPresenter
    {
        Camera ScreenCamera { get; }
    }

    public class BaseTopNavigationViewPresenter : ScreensViewPresenter
    {
        protected IAssetLoaded<GameObject> _assetLoaded;

        protected void ReleaseAsset()
        {
            if (_assetLoaded != null)
            {
                _assetLoaded.Dispose();
                _assetLoaded = null;
            }
        }
    }

    public abstract class BaseNavigationViewPresenter : BaseTopNavigationViewPresenter, IBaseNavigationViewPresenter
    {
        public abstract Camera ScreenCamera { get; }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            ReleaseAsset();
        }
    }
}