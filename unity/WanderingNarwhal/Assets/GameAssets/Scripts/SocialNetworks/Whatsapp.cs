namespace BebopBee.Messaging
{
	public interface IWhatsapp
	{
		bool isAvailable { get; }
		void sendMessage (string message);
		void sharePicture (string path);
	}

	public static class Whatsapp {
		private static bool _initialized = false;
		private static IWhatsapp _instance = null;

		public static void Initialize()
		{
		#if UNITY_EDITOR
			_instance = new WhatsappEditor();
		#elif UNITY_IPHONE
			_instance = new WhatsappiOS();

			(_instance as WhatsappiOS).Initialize();
        #elif UNITY_ANDROID
            _instance = new WhatsappAndroid();

            (_instance as WhatsappAndroid).Initialize();
		#endif

			_initialized = _instance != null;
		}

		public static IWhatsapp instance {
			get {
				if (!_initialized)
					Initialize ();
				
				return _instance;
			}
		}
	}
}
