namespace BebopBee.Messaging
{
	public interface IInstagram
	{
		bool isAvailable { get; }
		void sharePicture (string path);
	}

	public static class Instagram {
		private static bool _initialized = false;
		private static IInstagram _instance = null;

		public static void Initialize()
		{
		#if UNITY_IPHONE
			_instance = new InstagramiOS();
			(_instance as InstagramiOS).Initialize();
        #elif UNITY_ANDROID
            _instance = new InstagramAndroid();
            (_instance as InstagramAndroid).Initialize();
		#else
			_instance = new InstagramFallback();
        #endif

			_initialized = _instance != null;
		}

		public static IInstagram instance {
			get {
				if (!_initialized)
					Initialize ();

				return _instance;
			}
		}
	}
}
