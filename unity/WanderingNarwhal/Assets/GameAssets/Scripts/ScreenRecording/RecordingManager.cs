using System.Collections.Generic;
using Newtonsoft.Json;

namespace BBB.ScreenRecording
{
    public static class RedordingManager
    {
        private const string SdkKey = "";

        private const string LevelStartedEventName = "level_started";
        private const string LevelEndedEventName = "level_ended";

        private static bool _initialized;
        //remove when the new Recording SDK will be integrated
        private const bool Enabled = false;

        private static void Setup(string mdUserId, string bcProfileId)
        {
            if(!Enabled)
                return;
            
            if (!_initialized)
            {
                _initialized = true;
            }

            var props = !string.IsNullOrEmpty(bcProfileId) ? new BCUserCustomProperties(mdUserId, bcProfileId) : new UserCustomProperties(mdUserId);
            var customProperties = UnityEngine.JsonUtility.ToJson(props);
        }

        public static void StartRecording(string mdUserId = "", string bcProfileId = "")
        {
            Setup(mdUserId, bcProfileId);
        }

        public static void StopRecording()
        {
            
        }

        public static void SendLevelStartedEvent(string levelId, Dictionary<string, object> properties = null)
        {
            if (_initialized)
            {
                SendCustomEvent(new RecordingEvent($"{LevelStartedEventName}_{levelId}", properties));
            }
        }

        public static void SendLevelEndedEvent(string levelId, Dictionary<string, object> properties = null)
        {
            if (_initialized)
            {
                SendCustomEvent(new RecordingEvent($"{LevelEndedEventName}_{levelId}", properties));
            }
        }

        private static void SendCustomEvent(RecordingEvent customEvent)
        {
            if (!_initialized) 
                return;
        }

        private class UserCustomProperties
        {
            public string gameUserId;

            public UserCustomProperties(string gameUserId)
            {
                this.gameUserId = gameUserId;
            }
        }

        private class BCUserCustomProperties : UserCustomProperties
        {
            public string bcProfileId;

            public BCUserCustomProperties(string gameUserId, string bcProfileId) : base(gameUserId)
            {
                this.bcProfileId = bcProfileId;
            }
        }

        private record RecordingEvent
        {
            public string Name { private set; get; }
            public string Properties { private set; get; }

            public RecordingEvent(string name, Dictionary<string, object> properties = null)
            {
                Name = name;
                Properties = properties == null ? string.Empty : JsonConvert.SerializeObject(properties);
            }
        }
    }
}
