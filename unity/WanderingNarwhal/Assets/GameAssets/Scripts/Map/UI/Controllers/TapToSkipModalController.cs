using System;
using BBB.Core;
using GameAssets.Scripts.Map.UI.Views;

namespace GameAssets.Scripts.Map.UI.Controllers
{
    public class TapToSkipModalController : BaseModalsController<ITapToSkipModalViewPresenter>
    {
        public event Action OnSkipButtonPressed;

        public override bool CanBypassTransition()
        {
            return true;
        }

        protected override void OnShow()
        {
            base.OnShow();
            DoWhenReady(() =>
            {
                View.OnSkipButtonPressed -= OnSkipButtonPressed;
                View.OnSkipButtonPressed += OnSkipButtonPressed;
            });
        }

        protected override void OnHide()
        {
            base.OnHide();
            Clear();
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            Clear();
        }

        private void Clear()
        {
            if (View != null)
            {
                View.OnSkipButtonPressed -= OnSkipButtonPressed;
            }
            OnSkipButtonPressed = null;
        }
    }
}
