using BBB;
using BBB.DI;
using BBB.RaceEvents;
using BBB.RaceEvents.UI;
using BBB.UI.Core;
using BBB.Wallet;
using Bebopbee.Core.Extensions.Unity;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.SocialScreens.Teams;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Map.UI.Controllers
{
    public sealed class StartLevelEventPanelPlayerView : ContextedUiBehaviour
    {
        [SerializeField] private TextMeshProUGUI _scoreText;
        [SerializeField] private TextMeshProUGUI _placeText;
        [SerializeField] private TextMeshProUGUI _nameText;
        [SerializeField] private AsyncAvatar _asyncAvatar;
        [SerializeField] private Image _eventIcon;
        [SerializeField] private Image _placeImage;
        [SerializeField] private Sprite[] _placeSprites;
        [SerializeField] private GameObject _ownPlayerAvatarBg;
        [SerializeField] private GameObject[] _friendHolders;
        [SerializeField] private Color _normalNicknameColor;
        [SerializeField] private Color _friendNickNameColor;

        private ISocialManager _socialManager;
        private CurrencyIconsLoader _currencyIconsLoader;

        protected override void InitWithContextInternal(IContext context)
        {
            _socialManager = context.Resolve<ISocialManager>();
            _currencyIconsLoader = context.Resolve<CurrencyIconsLoader>();
        }

        public void Setup(PlayerEventLeaderboardItem leaderboardItem, bool isOwn)
        {
            Setup(leaderboardItem.Name, leaderboardItem.AvatarUrl, leaderboardItem.Country, leaderboardItem.Score, leaderboardItem.Place, isOwn, leaderboardItem.Uid);
            _placeImage.sprite = _placeSprites[Mathf.Clamp(leaderboardItem.Place - 1, 0, _placeSprites.Length - 1)];
        }

        public void Setup(PlayerRowViewData playerRowViewData, int place, RaceEvent raceEvent, RaceEventVisualConfig raceEventVisualConfig)
        {
            Setup(playerRowViewData.Name, playerRowViewData.Avatar, playerRowViewData.Country, playerRowViewData.CurrentScore, place, playerRowViewData.IsOwn, playerRowViewData.Uid);

            if (raceEventVisualConfig.ShowEventIcon)
            {
                _currencyIconsLoader.LoadAndGetCurrencySpriteAsync(InventoryItems.GetRaceEventStageCurrency(raceEvent.GetRaceStageUid())).ContinueWith(sprite => _eventIcon.sprite  = sprite);
                _eventIcon.gameObject.SetActive(true);
            }

            if (raceEvent.RaceEventType != RaceEventTypes.RaceEventType.Collect)
            {
                _scoreText.text += "/" + raceEvent.GetCurrentRaceScoreGoal();
            }

            _placeImage.sprite = raceEvent.GetCurrentRaceStagePrizeCount() - place >= 0 ? _placeSprites[Mathf.Clamp(place - 1, 0, _placeSprites.Length - 1)] : _placeSprites[^1];
        }

        private void Setup(string playerName, string avatarUrl, string country, int score, int place, bool isOwn, string playerUid)
        {
            LazyInit();

            var isTeamMate = _socialManager.IsTeamMate(playerUid);
            _nameText.text = playerName;

            _asyncAvatar.Setup(new AvatarInfo(avatarUrl));

            _scoreText.text = score.ToString();
            _placeText.text = place.ToString();
            _eventIcon.gameObject.SetActive(false);

            _ownPlayerAvatarBg.SetActive(isOwn);

            var isFriend = !isOwn && isTeamMate;
            _friendHolders.Enable(isFriend);
            _nameText.color = isFriend ? _friendNickNameColor : _normalNicknameColor;
        }

        protected override void OnDestroy()
        {
            _eventIcon.sprite = null;
            base.OnDestroy();
        }
    }
}