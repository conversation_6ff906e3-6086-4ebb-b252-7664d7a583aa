using BBB.DI;
using System;
using System.Collections.Generic;
using BBB.GameAssets.Scripts.Player;
using BBB.Map;
using BBB.Screens;
using BBB.Wallet;
using Bebopbee.Core.Extensions.Unity;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Promotions.Banners;
using GameAssets.Scripts.SocialScreens.Teams;
using PBGame;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI.Map.Views
{
    public class NoMoreLivesViewPresenter : ModalsViewPresenter, INoMoreLivesViewPresenter
    {
        public event Action BuyButtonClicked;
        public event Action AdButtonClicked;
        public event Action GetGiftsButtonClicked;

        [SerializeField] private GameObject[] _noLivesHolder;
        [SerializeField] private GameObject[] _hasLivesHolder;
        [SerializeField] private Button _buyButton;
        [SerializeField] private Button _adButton;
        [SerializeField] private GameObject[] _adEnabledHolders;
        [SerializeField] private GameObject[] _adDisabledHolders;
        [SerializeField] private Button _getGiftsButton;
        [SerializeField] private GameObject[] _getGiftsHolders;
        [SerializeField] private TextMeshProUGUI _livesToFillText;
        [SerializeField] private UICurrencyInsufficientComponent _priceComponent;
        [SerializeField] private PopupStagePaletteApplier _popupStagePaletteApplier;
        [SerializeField] private BannerContainer _bannerContainer;
        [SerializeField] private RectTransform _mainPanelRoot;
        [SerializeField] private Vector2 _anchoredOffsetDefault;
        [SerializeField] private Vector2 _anchoredOffsetCenter;

        private ISocialManager _socialManager;
        private ILivesManager _livesManager;
        private CurrencyIconsLoader _currencyIconsLoader;
        private IWalletManager _walletManager;
        private BannerManager _bannerManager;
        private VideoAdManager _videoAdManager;
        private IDictionary<string, ProgressionLevelConfig> _levelConfig;

        protected override void Awake()
        {
            base.Awake();
            _buyButton.ReplaceOnClick(() => BuyButtonClicked?.Invoke());
            _getGiftsButton.ReplaceOnClick(() => GetGiftsButtonClicked?.Invoke());
            _adButton.ReplaceOnClick(() => AdButtonClicked?.Invoke());
        }

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _livesManager = context.Resolve<ILivesManager>();
            _socialManager = context.Resolve<ISocialManager>();
            _currencyIconsLoader = context.Resolve<CurrencyIconsLoader>();
            _walletManager = context.Resolve<IWalletManager>();
            _bannerManager = context.Resolve<BannerManager>();
            _videoAdManager = context.Resolve<VideoAdManager>();
            _levelConfig = context.Resolve<IConfig>().Get<ProgressionLevelConfig>();
        }

        public void Setup(Dictionary<string, long> livesPrice, LevelState levelState)
        {
            foreach (var key in livesPrice.Keys)
            {
                var priceAmount = livesPrice[key];

                _getGiftsHolders.Enable(false);
                _mainPanelRoot.anchoredPosition = _anchoredOffsetDefault;

                _currencyIconsLoader.LoadAndGetCurrencySpriteAsync(key)
                    .ContinueWith(sprite => _priceComponent.Setup(sprite, priceAmount.ToInt()));
                
                _priceComponent.SetupInsufficient(_walletManager.Balance.GetBalance(), key);
                _livesToFillText.text = "+" + _livesManager.MaxLives;
                _popupStagePaletteApplier.Apply(levelState != null && !levelState.SourceUid.IsNullOrEmpty()
                    ? _levelConfig[levelState.SourceUid].GetPaletteStage()
                    : Stage.Good);
                break;
            }
        }

        public void RefreshAd()
        {
            var adEnabled = _videoAdManager.IsAdAvailable(VideoAdManager.FreeLife, VideoAdManager.FreeLife);
            _adEnabledHolders.Enable(adEnabled);
            _adDisabledHolders.Enable(!adEnabled);
        }

        protected override void OnShow()
        {
            base.OnShow();

            var noLives = _livesManager.NumberOfLives <= 0;
            _noLivesHolder.Enable(noLives);
            _hasLivesHolder.Enable(!noLives);

            _bannerManager.FillContainerWithBanners(_bannerContainer);

            if (_bannerContainer.BannerItemsCount == 0)
            {
                var shouldSocialGifts = _socialManager.IsSocialUnlocked();
                _getGiftsHolders.Enable(shouldSocialGifts);
                _mainPanelRoot.anchoredPosition = shouldSocialGifts ? _anchoredOffsetDefault : _anchoredOffsetCenter;
            }
            else
            {
                _getGiftsHolders.Enable(false);
                _mainPanelRoot.anchoredPosition = _anchoredOffsetDefault;
            }
        }

        protected override void OnHide()
        {
            base.OnHide();
            _bannerContainer.Clear();
        }
    }
}