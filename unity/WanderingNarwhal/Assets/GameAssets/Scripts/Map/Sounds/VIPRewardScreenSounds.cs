using UnityEngine;
using BebopBee.Core.Audio;
using BBB.Audio;

namespace BBB.UI
{
    public class VIPRewardScreenSounds : BbbMonoBehaviour
    {
        public void PlayVisitedSound()
        {
            AudioProxy.PlaySound(GenericSoundIds.QuestVIPReward_Visited);
        }

        public void PlayPinDropSound()
        {
            AudioProxy.PlaySound(GenericSoundIds.QuestVIPReward_PinDrop);
        }

        public void PlayBoxOpenSound()
        {
            AudioProxy.PlaySound(GenericSoundIds.QuestVIPReward_BoxOpen);
        }
    }
}