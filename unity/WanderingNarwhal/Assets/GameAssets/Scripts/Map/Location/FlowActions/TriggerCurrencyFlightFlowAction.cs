using System;
using System.Collections;
using BBB;
using BBB.Core;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Modals;
using BBB.RaceEvents;
using BBB.Social;
using BBB.TeamEvents;
using BBB.UI;
using BBB.Wallet;
using DG.Tweening;

namespace GameAssets.Scripts.Map.Location.FlowActions
{
    public sealed class TriggerCurrencyFlightFlowAction : FlowAction
    {
        private readonly IWalletManager _walletManager;
        private readonly IUIWalletManager _uiWalletManager;
        private readonly IRaceEventManager _raceEventManager;
        private readonly ITeamEventManager _teamEventManager;
        private readonly IPlayerManager _playerManager;
        private readonly ChallengeTriviaManager _challengeTriviaManager;
        private readonly IModalsManager _modalsManager;

        private readonly LastLevelPlayedData _lastLevelPlayedData;

        private IWalletTransactionController WalletTransactionController => _walletManager.TransactionController;

        private Tweener _tweener;

        public TriggerCurrencyFlightFlowAction(IContext context, LastLevelPlayedData lastLevelPlayedData)
        {
            _walletManager = context.Resolve<IWalletManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            _raceEventManager = context.Resolve<IRaceEventManager>();
            _teamEventManager = context.Resolve<ITeamEventManager>();
            _challengeTriviaManager = context.Resolve<ChallengeTriviaManager>();
            _modalsManager = context.Resolve<IModalsManager>();

            _lastLevelPlayedData = lastLevelPlayedData;
        }

        public override IEnumerator Execute(Action breakCallback = null)
        {
            BDebug.Log(LogCat.Flow, $"TriggerCurrencyFlightFlowAction Execute start");
            _lastLevelPlayedData.CurrencyDropAnimationPlayed();

            foreach (var lastDeltaScoreTuple in _raceEventManager.GetLastDeltaScoreTuples())
            {
                var raceEventTx = new Transaction()
                    .AddTag(TransactionTag.LevelReward)
                    .Earn(InventoryItems.GetRaceEventStageCurrency(lastDeltaScoreTuple.stageUid), lastDeltaScoreTuple.score);

                WalletTransactionController.MakeOnlyVisualTransaction(raceEventTx);
            }

            _raceEventManager.ClearLastDeltaScores();

            foreach (var lastDeltaScoreTuple in _teamEventManager.GetLastDeltaScoreTuples())
            {
                var eventTx = new Transaction()
                    .AddTag(TransactionTag.LevelReward)
                    .Earn(InventoryItems.GetTeamEventCurrency(lastDeltaScoreTuple.eventUid), lastDeltaScoreTuple.score);

                WalletTransactionController.MakeOnlyVisualTransaction(eventTx);
            }

            _teamEventManager.ClearLastDeltaScores();

            var challengeTriviaScoreToShow = Math.Abs(_challengeTriviaManager.DeltaScore);
            if (challengeTriviaScoreToShow > 0)
            {
                var sdbTx = new Transaction()
                    .AddTag(TransactionTag.LevelReward)
                    .Earn(InventoryItems.ChallengeTriviaScore, challengeTriviaScoreToShow);

                WalletTransactionController.MakeOnlyVisualTransaction(sdbTx);
            }

            if (_lastLevelPlayedData.ShouldShowLevelsProgression)
            {
                // var stage = _lastLevelPlayedData.LevelStage;
                // var starUid = WalletResources.GetCurrencyByStage(stage);
                // hardcoded to one type of star now
                const string starUid = WalletResources.HardCurrency;

                var plusOneTransaction = new Transaction()
                    .AddTag(TransactionTag.LevelReward)
                    .Earn(starUid, 1);

                WalletTransactionController.MakeOnlyVisualTransaction(plusOneTransaction);
            }

            while (_modalsManager.IsShowingModal(ModalsType.LevelSuccess, true)
                   || _modalsManager.IsShowingModal(ModalsType.LevelSuccessNoLB, true)
                   || _modalsManager.IsShowingModal(ModalsType.LevelSuccessVerticalLB, true))
            {
                yield return null;
            }


            _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.LevelReward);

            BDebug.Log(LogCat.Flow, $"TriggerCurrencyFlightFlowAction Execute finish");
            yield break;
        }
    }
}