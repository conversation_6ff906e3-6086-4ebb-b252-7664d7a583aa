using System;
using System.Collections.Generic;
using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using Bebopbee.Core.Extensions.Unity;
using BrainCloud;
using BrainCloud.Common;
using BrainCloud.Editor;
using GameAssets.Scripts.Core.NewtonSoftJsonExtensions;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace BBB.BrainCloud.Editor
{
    public class BrainCloudControlPanelEditorWindow : BrainCloudBaseEditorWindow
    {
        private const int DaysFromNowToCheckScheduledJobs = 14;

        private string _scriptNameToRunFieldText = "Script Name";
        private string _scriptDataToRunFieldText = "{}";
        
        private string _scriptNameToScheduleFieldText = "Script Name";
        private string _scriptDataToScheduleFieldText = "{}";
        private string _dateTimeToScheduleFieldText;
        private string _dateTimeToSeeScheduledSinceFieldText;

        [MenuItem("brainCloud/Custom/Control Panel")]
        public static void ShowWindow()
        {
            var window = GetWindow<BrainCloudControlPanelEditorWindow>("Control Panel");
            window.InitDev();
        }

        [MenuItem("brainCloud/Custom/Invalidate Session")]
        public static void InvalidateSession()
        {
            var manager = FindObjectOfType<BrainCloudManager>();
            manager.InvalidateSession();
        }

        [MenuItem("brainCloud/Custom/Invalidate Session", true)]
        public static bool InvalidateSessionCheck()
        {
            return Application.isPlaying;
        }
        protected override void InitDev()
        {
            _dateTimeToScheduleFieldText = DateTime.UtcNow.AddDays(DaysFromNowToCheckScheduledJobs).ToUniversalIso8601();
            _dateTimeToSeeScheduledSinceFieldText = _dateTimeToScheduleFieldText;
            
            base.InitDev();
        }
        
        protected override void InitTest()
        {
            _dateTimeToScheduleFieldText = DateTime.UtcNow.AddDays(DaysFromNowToCheckScheduledJobs).ToUniversalIso8601();
            _dateTimeToSeeScheduledSinceFieldText = _dateTimeToScheduleFieldText;

            base.InitTest();
        }
        
        private void OnGUI()
        {
            DrawAppTitle();
            
            EditorGUILayout.BeginHorizontal();
            
            _scriptNameToRunFieldText = EditorGUILayout.TextField(_scriptNameToRunFieldText, GUILayout.ExpandWidth(true));
            _scriptDataToRunFieldText = EditorGUILayout.TextField(_scriptDataToRunFieldText, GUILayout.ExpandWidth(true));
            
            // Two fixed-width buttons on the right side
            if (GUILayout.Button("Run Script", GUILayout.Width(120)))
            {
                GUI.enabled = false;
                RunScript<DebugLogDto>(_scriptNameToRunFieldText, _scriptDataToRunFieldText, dto =>
                {
                    GUI.enabled = true;
                });
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space(10);
            
            EditorGUILayout.BeginHorizontal();
        
            // Input field that stretches with the window
            _scriptNameToScheduleFieldText = EditorGUILayout.TextField(_scriptNameToScheduleFieldText, GUILayout.ExpandWidth(true));
            _scriptDataToScheduleFieldText = EditorGUILayout.TextField(_scriptDataToScheduleFieldText, GUILayout.ExpandWidth(true));
            _dateTimeToScheduleFieldText = EditorGUILayout.TextField(_dateTimeToScheduleFieldText, GUILayout.ExpandWidth(true));
        
            if (GUILayout.Button("Schedule Script", GUILayout.Width(120)))
            {
                GUI.enabled = false;
                ScheduleScript(_scriptNameToScheduleFieldText, _scriptDataToScheduleFieldText, _dateTimeToScheduleFieldText, () =>
                {
                    GUI.enabled = true;
                });
            }
        
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space(10);
            
            EditorGUILayout.BeginHorizontal();
            
            _dateTimeToSeeScheduledSinceFieldText = EditorGUILayout.TextField(_dateTimeToSeeScheduledSinceFieldText, GUILayout.ExpandWidth(true));

            if (GUILayout.Button("List Scheduled", GUILayout.Width(120)))
            {
                GUI.enabled = false;
                ListScheduledScripts(_dateTimeToSeeScheduledSinceFieldText, () =>
                {
                    GUI.enabled = true;
                });
            }
            
            EditorGUILayout.EndHorizontal();

            // Space between rows
            EditorGUILayout.Space(10);

            // Button on the second row

            DrawProdDevSwitchButton();
        }

        private void ListScheduledScripts(string dateTimeStr, Action callback)
        {
            var dateTime = DateTime.Parse(dateTimeStr, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal | DateTimeStyles.AssumeUniversal);

            BrainCloudWrapper.ScriptService.GetScheduledCloudScripts(dateTime, SuccessCallback, FailureCallback);
            return;

            void FailureCallback(int status, int code, string error, object cbObject)
            {
                Debug.LogError($"BC Control Panel: ListScheduledScripts Failed | {status}  {code}  {error}");
                callback?.Invoke();
            }

            void SuccessCallback(string response, object cbObject)
            {
                Debug.Log($"BC Control Panel: ListScheduledScripts Success | {response}");
                var dto = JsonConvertExtensions.TryDeserializeObject<ListScheduledJobsDto>(response);
                Debug.Log($"ListScheduledScripts DEBUG LOG: {dto.DebugLog}");
                var anyListed = false;
                if (dto.Data?.ScheduledJobs != null)
                {
                    anyListed = dto.Data.ScheduledJobs.Count > 0;
                    foreach (var job in dto.Data.ScheduledJobs)
                    {
                        Debug.Log($"Script {job.ScriptName} Description: {job.Description} is scheduled at {DateTimeExtensions.FromUnixTimeSecondsUtc(job.ScheduledStartTime / 1000.0)}");
                    }
                }

                if (!anyListed)
                {
                    Debug.Log("BC Control Panel: ListScheduledScripts: no jobs found");
                }

                callback?.Invoke();
            }
        }
    }
}