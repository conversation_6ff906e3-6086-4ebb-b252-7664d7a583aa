using System;
using System.Collections.Generic;
using System.Linq;
using BrainCloud;
using BrainCloud.Editor;
using Newtonsoft.Json;
using UnityEditor;
using UnityEngine;
using BBB.BrainCloud.Editor.Model;
using BBB.Core;

namespace BBB.BrainCloud.Editor
{
    public class BrainCloudSchedulerEditorWindow : BrainCloudBaseEditorWindow
    {
        private const string CompetitionGameEventConfigName = "CompetitionGameEventConfig";
        private const string GameEventMetaConfigName = "GameEventMetaConfig";
        
        private static readonly List<ValueTuple<string, bool>> Weekdays = new ()
        {
            new ValueTuple<string, bool>("monday", false),
            new ValueTuple<string, bool>("tuesday", false),
            new ValueTuple<string, bool>("wednesday", false),
            new ValueTuple<string, bool>("thursday", false),
            new ValueTuple<string, bool>("friday", false),
            new ValueTuple<string, bool>("saturday", false),
            new ValueTuple<string, bool>("sunday", false),
        };
        
        private string[] _eventDropdownOptions;
        private int _selectedEventOption;
        private int _numberOfPeriodsToSchedule = 2;
        private int _periodLengthInMin = 5;
        private int _timeStartOffsetFromNow;
        private CompEventSchedulingType _schedulingType;
        private static MetaConfigScheduleData _scheduleData;

        [MenuItem("brainCloud/Custom/Scheduler")]
        public static void ShowWindow()
        {
            var window = GetWindow<BrainCloudSchedulerEditorWindow>("Scheduler");
            window.InitDev();
        }

        protected override void InitDev()
        {
            base.InitDev();
            _eventDropdownOptions = null;
            FetchProperties();
        }

        protected override void InitTest()
        {
            base.InitTest();
            _eventDropdownOptions = null;
            FetchProperties();
        }
        
        private void FetchProperties()
        {
            ReadCompEventProperties(dto =>
            {
                if (dto.Data == null) 
                    return;
                
                if (dto.Data.TryGetValue(CompetitionGameEventConfigName, out var eventConfigDict))
                {
                    var valueStr = (string)eventConfigDict["value"];
                    var valueDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(valueStr);
                    _eventDropdownOptions = valueDict.Keys.ToArray();
                }

                if (dto.Data.TryGetValue(GameEventMetaConfigName, out var metaConfigDict))
                {
                    var valueStr = (string)metaConfigDict["value"];
                    var valueDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(valueStr);
                    var defaultConfig = valueDict["default"];
                    var serializedDefaultConfig = JsonConvert.SerializeObject(defaultConfig);
                    var scheduleData = JsonConvert.DeserializeObject<MetaConfigScheduleData>(serializedDefaultConfig);
                    _scheduleData = scheduleData;
                }
            }, null);
        }

        private void ReadCompEventProperties(Action<ReadPropertiesDto> success, Action failure)
        {
            if (_eventDropdownOptions != null) 
                return;
            
            string[] propertyNames = { CompetitionGameEventConfigName, GameEventMetaConfigName };

            BrainCloudWrapper.GlobalAppService.ReadSelectedProperties(propertyNames, SuccessCallback, FailureCallback);
            return;

            void FailureCallback(int status, int code, string error, object cbObject)
            {
                Debug.LogError($"Failed | {status}  {code}  {error}");
                failure?.Invoke();
            }

            void SuccessCallback(string response, object cbObject)
            {
                Debug.Log($"Success | {response}");
                var dto = JsonConvert.DeserializeObject<ReadPropertiesDto>(response);
                success?.Invoke(dto);
            }
        }

        private void OnGUI()
        {
            DrawAppTitle();

            if (_eventDropdownOptions is { Length: > 0 })
            {
                _selectedEventOption = EditorGUILayout.Popup("Select an event:", _selectedEventOption, _eventDropdownOptions);

                _schedulingType = (CompEventSchedulingType)EditorGUILayout.EnumPopup("Select scheduling type:", _schedulingType);

                switch (_schedulingType)
                {
                    case CompEventSchedulingType.Weekday:
                    {
                        if (_scheduleData != null)
                        {
                            GUILayout.Label("Select weekdays: ", EditorStyles.boldLabel);
                            for (var i = 0; i < Weekdays.Count; i++)
                            {
                                var weekdayTuple = Weekdays[i];
                                Weekdays[i] = new ValueTuple<string, bool>(weekdayTuple.Item1, EditorGUILayout.Toggle(weekdayTuple.Item1, weekdayTuple.Item2));
                            }
                        }
                        else
                        {
                            EditorGUILayout.BeginVertical();
                            GUILayout.Label("Schedule data not found", EditorStyles.boldLabel);
                            EditorGUILayout.EndVertical();
                        }
                        break;
                    }
                    case CompEventSchedulingType.Minutes:
                    {
                        GUILayout.BeginHorizontal();
                        GUILayout.Label("Period length in min:", EditorStyles.boldLabel);
                        var periodLengthInMinStr = EditorGUILayout.TextField(_periodLengthInMin.ToString(), EditorStyles.textField, GUILayout.Width(200));
                        if (int.TryParse(periodLengthInMinStr, out var lengthParseResult))
                        {
                            _periodLengthInMin = lengthParseResult;
                        }
                        else
                        {
                            _numberOfPeriodsToSchedule = 0;
                        }
                        GUILayout.EndHorizontal();
                        break;
                    }
                }
                
                GUILayout.BeginHorizontal();
                GUILayout.Label("Periods number:", EditorStyles.boldLabel);
                var numberOfPeriodsStr = EditorGUILayout.TextField(_numberOfPeriodsToSchedule.ToString(), EditorStyles.textField, GUILayout.Width(200));
                _numberOfPeriodsToSchedule = int.TryParse(numberOfPeriodsStr, out var periodNumberParseResult) ? periodNumberParseResult : 0;
                GUILayout.EndHorizontal();
                
                GUILayout.BeginHorizontal();
                GUILayout.Label("Minutes offset from now:", EditorStyles.boldLabel);
                var offsetStr = EditorGUILayout.TextField(_timeStartOffsetFromNow.ToString(), EditorStyles.textField, GUILayout.Width(200));
                _timeStartOffsetFromNow = int.TryParse(offsetStr, out var offsetFromNowResult) ? offsetFromNowResult : 0;
                GUILayout.EndHorizontal();
  
                GUILayout.BeginHorizontal();
                if (GUILayout.Button("Schedule Periods", GUILayout.Width(120)))
                {
                    var parsePeriods = ParsePeriods();
                    if (parsePeriods != null)
                    {
                        GUI.enabled = false;
                    
                        SchedulePeriods(parsePeriods, dto =>
                        {
                            LogPeriods(dto);
                            GUI.enabled = true;
                        });
                    }
                }
                
                if (GUILayout.Button("List Periods", GUILayout.Width(120)))
                {
                    GUI.enabled = false;

                    var eventId = _eventDropdownOptions[_selectedEventOption];
                    ListPeriods(eventId, dto =>
                    {
                        LogPeriods(dto);
                        GUI.enabled = true;
                    });
                }
                
                if (GUILayout.Button("Remove Periods", GUILayout.Width(120)))
                {
                    GUI.enabled = false;
                    RemovePeriods(dto =>
                    {
                        LogPeriods(dto);
                        GUI.enabled = true;
                    });
                }
                GUILayout.EndHorizontal();
            }
            else
            {
                var timePeriod = (int)(Time.time % .33f);
                var loadingLabel = "Loading.";
                for (var i = 0; i < timePeriod; i++)
                {
                    loadingLabel += ".";
                }
                GUILayout.Label(loadingLabel, EditorStyles.boldLabel);
            }
            
            EditorGUILayout.Space(10);
            DrawProdDevSwitchButton();
        }

        private void LogPeriods(PeriodsDto dto)
        {
            if (dto.ScheduledPeriods != null)
            {
                var periodsStr = string.Empty;
                foreach (var period in dto.ScheduledPeriods)
                {
                    periodsStr += period + "; ";
                }
                BDebug.Log($"Periods scheduled: {periodsStr}");
            }
            else
            {
                BDebug.Log("Periods not found");
            }
        }

        private ScheduleEventsInputDto ParsePeriods()
        {
            switch (_schedulingType)
            {
                case CompEventSchedulingType.Weekday:
                {
                    if (_scheduleData == null)
                    {
                        BDebug.LogError(LogCat.General, "Schedule data not found, failed to parse weekday schedule");
                        return null;
                    }
                    
                    var dto = new ScheduleEventsInputDto
                    {
                        EventId = _eventDropdownOptions[_selectedEventOption],
                        TConfigCode = "CompetitiveEvents",
                        ScheduleDataList = new List<PeriodScheduleData>()
                    };
                    
                    var activeWeekdays = new HashSet<DayOfWeek>();
                    foreach (var weekday in Weekdays)
                    {
                        if (!weekday.Item2) 
                            continue;
                        
                        if (Enum.TryParse(weekday.Item1, true, out DayOfWeek dayOfWeek))
                        {
                            activeWeekdays.Add(dayOfWeek);
                        }
                    }

                    var utcNow = DateTime.UtcNow;
                    utcNow = utcNow.AddMinutes(_timeStartOffsetFromNow);
                    var startScheduleTime = new DateTime(utcNow.Year, utcNow.Month, utcNow.Day, _scheduleData.UtcHourToStart,
                        _scheduleData.UtcMinuteToStart, 0);
                    if (startScheduleTime < utcNow)
                    {
                        //if todays time passed the hour and minute to start then start checking tomorrow
                        startScheduleTime = startScheduleTime.AddDays(1);
                    }

                    var weekLength = Enum.GetValues(typeof(DayOfWeek)).Length;

                    DateTime? previousDay = null;
                    var currentDay = startScheduleTime;
                    long startMilliseconds = -1;
                    long lengthInDays = 0;
                    for (var i = 0; i < _numberOfPeriodsToSchedule; i++)
                    {
                        for (var j = 0; j < weekLength; j++)
                        {
                            if (activeWeekdays.Contains(currentDay.DayOfWeek))
                            {
                                if (previousDay.HasValue && activeWeekdays.Contains(previousDay.Value.DayOfWeek))
                                {
                                    //proceed with period
                                    lengthInDays += 1;
                                }
                                else
                                {
                                    //start new period
                                    startMilliseconds = currentDay.ToUnixTimeMilliseconds();
                                    lengthInDays = 1;
                                }
                            }
                            else
                            {
                                if (previousDay.HasValue && activeWeekdays.Contains(previousDay.Value.DayOfWeek))
                                {
                                    //end current period
                                    if (startMilliseconds != -1)
                                    {
                                        dto.ScheduleDataList.Add(new PeriodScheduleData
                                        {
                                            StartingAt = startMilliseconds,
                                            DurationDays = lengthInDays
                                        });

                                        startMilliseconds = -1;
                                        lengthInDays = 0;
                                    }
                                }
                            }

                            previousDay = currentDay;
                            currentDay = currentDay.AddDays(1);
                        }
                    }

                    if (startMilliseconds != -1)
                    {
                        dto.ScheduleDataList.Add(new PeriodScheduleData
                        {
                            StartingAt = startMilliseconds,
                            DurationDays = lengthInDays
                        });
                    }

                    return dto;
                }
                case CompEventSchedulingType.Minutes:
                {
                    var utcNow = DateTime.UtcNow;
                    var firstStartTime = utcNow.AddMinutes(1);
                    firstStartTime = firstStartTime.AddMinutes(_timeStartOffsetFromNow);
                    var dto = new ScheduleEventsInputDto
                    {
                        EventId = _eventDropdownOptions[_selectedEventOption],
                        TConfigCode = "CompetitiveEvents",
                        ScheduleDataList = new List<PeriodScheduleData>()
                    };

                    var workingStartTime = firstStartTime;
                    for (var i = 0; i < _numberOfPeriodsToSchedule; i++)
                    {
                        var startTime = workingStartTime;
                        var endTime = startTime.AddMinutes(_periodLengthInMin);
                        workingStartTime = endTime.AddSeconds(5);
                        
                        dto.ScheduleDataList.Add(new PeriodScheduleData
                        {
                            StartingAt = startTime.UTCDateTimeToUTCMillis(),
                            DurationMinutes = (long)(endTime-startTime).TotalMinutes,
                        });
                    }
                    return dto;
                }
            }

            return null;
        }

        private void SchedulePeriods(ScheduleEventsInputDto parsePeriods, Action<PeriodsDto> callback)
        {
            var paramsObj = JsonConvert.SerializeObject(parsePeriods);
            RunScript("/comp_event/GameEventSchedule", paramsObj, callback);
        }

        private void ListPeriods(string eventId, Action<PeriodsDto> callback)
        {
            var paramsObj = JsonConvert.SerializeObject(new Dictionary<string, string> {{ "eventId", eventId},{"justList", "true"}});
            RunScript("/comp_event/GameEventSchedule", paramsObj, callback);
        }

        private void RemovePeriods(Action<PeriodsDto> callback)
        {
            var paramsObj = JsonConvert.SerializeObject(new ScheduleEventsInputDto{ EventId = _eventDropdownOptions[_selectedEventOption]});
            RunScript("/comp_event/GameEventSchedule", paramsObj, callback);
        }
    }
}