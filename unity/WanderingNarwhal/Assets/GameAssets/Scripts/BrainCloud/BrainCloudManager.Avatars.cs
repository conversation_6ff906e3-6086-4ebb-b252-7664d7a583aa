using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace BBB.BrainCloud
{
    public partial class BrainCloudManager
    {
        public void FetchUserAvatars(string leaderboardIdSource, 
            Action<BCFetchUserAvatarsResponse> success, Action failure)
        {
            var scriptData = new Dictionary<string, object>()
            {
                {"leaderboardIdSource", leaderboardIdSource }
            };
            
            _brainCloudWrapper.RunScript("/avatars/FetchUserAvatars", JsonConvert.SerializeObject(scriptData), success, failure);
        }
    }
}