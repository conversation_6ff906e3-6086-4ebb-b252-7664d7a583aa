using System.Collections.Generic;
using Newtonsoft.Json;

namespace BBB.BrainCloud
{
    public class TScheduled
    {
        [JsonProperty("startingAt")]
        public long StartingAt { get; set; }

        [JsonProperty("endingAt")]
        public long EndingAt { get; set; }

        [JsonProperty("tConfigs")]
        public Dictionary<string, TConfig> TConfigs { get; set; }

        [JsonProperty("tStates")]
        public TStates TStates { get; set; }

        [JsonProperty("tAutoJoin")]
        public bool TAutoJoin { get; set; }

        [JsonProperty("tAutoClaim")]
        public bool TAutoClaim { get; set; }
    }
}