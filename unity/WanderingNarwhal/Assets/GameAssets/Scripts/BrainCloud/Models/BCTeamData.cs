using System;
using System.Collections.Generic;
using BebopBee.Social;
using RPC.Teams;

namespace BBB.BrainCloud
{
    [Serializable]
    public class BCTeamDataResponse : BCRunScriptResponse<BCBasicResponseData<BCTeamData>> {}

    [Serializable]
    public class BCTeamData
    {
        private const string Owner = "OWNER";
        private const string Admin = "ADMIN";

        public string groupId;
        public string name;
        public string groupType;
        public BCGroupSummaryData summaryData;
        public Dictionary<string, BCTeamMemberData> groupMembers;

        public TeamData ToTeamData()
        {
            if (!Enum.TryParse<TeamType>($"TeamType{groupType}", out var teamType))
            {
                teamType = TeamType.TeamTypePublic;
            }

            var teamMembers = new List<TeamMemberData>();
            var score = 0;
            if (groupMembers != null)
            {
                foreach(var (uId, member) in groupMembers)
                {
                    teamMembers.Add(new TeamMemberData {
                        Name = member.playerName,
                        Avatar = member.pic,
                        Country = member.summaryFriendData?.Country ?? ProfileUtils.DefaultCountry,
                        Uid = uId,
                        Trophies = member.summaryFriendData?.Trophies ?? 0,
                        IsAdmin = Admin.Equals(member.role),
                        IsOwner = Owner.Equals(member.role),
                        LastActivityTimestamp = member.summaryFriendData?.LastActivityTimestamp ?? 0,
                        HelpCount = member.summaryFriendData?.HelpCount ?? 0,
                    });
                    score += member.summaryFriendData?.Trophies ?? 0;
                }
            }

            var teamData = new TeamData
            {
                TeamUid = groupId,
                Name = name,
                Description = summaryData.Description,
                Icon = summaryData.Icon,
                Country = summaryData.Country,
                TeamType = teamType,
                Members = teamMembers,
                Score = score,
                Activity = summaryData.GroupActivity,
                RequiredLevel = summaryData.RequiredLevel
            };

            return teamData;
        }
    }
}