using System;
using Newtonsoft.Json;

namespace BBB.BrainCloud
{
    [Serializable]
    public class BCChallengeData
    {
        [JsonProperty("challengeId")]
        public string ChallengeId;
        [JsonProperty("ownerId")]
        public string OwnerId;
        //0 - in progress, 1 - ended, 2 - failed
        [JsonProperty("status")]
        public int Status;
        // nudged player id
        [JsonProperty("nudged")]
        public string Nudged;
        [JsonProperty("roundNumber")]
        public int RoundNumber;
        // next turn player id
        [JsonProperty("nextTurnPlayer")]
        public string NextTurnPlayer;
        [JsonProperty("lastUpdatedTimestamp")]
        public long LastUpdatedTimestamp;
        [JsonProperty("opponent")]
        public BCPlayerProfileData Opponent;
        [JsonProperty("createdAt")]
        public long CreatedAt;
        [JsonProperty("claims")]
        public string[] Claims;
    }
}