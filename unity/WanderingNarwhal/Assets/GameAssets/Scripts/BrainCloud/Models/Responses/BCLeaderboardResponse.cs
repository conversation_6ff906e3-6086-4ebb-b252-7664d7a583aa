using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Realms;

namespace BBB.BrainCloud
{
    [System.Serializable]
    public class GetCompEventRemoteDataResponse : BCRunScriptResponse<GetCompEventRemoteData>
    {
    }

    [System.Serializable]
    public class GetCompEventRemoteData : BCBasicResponseData
    {
        [JsonProperty("gameEventData")] public BCUserGameEventData[] GameDtos { get; set; }
    }

    [System.Serializable]
    public class BCCompEventLeaderboardResponse : BCRunScriptResponse<BCCompEventLeaderboardData>
    {
        
    }

    [System.Serializable]
    public class BCCompEventLeaderboardData : BCBasicResponseData
    {
        [JsonProperty("fireLeagueTotalCount")] public int FireLeagueTotalCount { get; set; }
        [JsonProperty("userDataList")] public List<BCLeaderboardUserData> UserDataList { get; set; }
        [JsonProperty("currentPeriod")] public TScheduledTime CurrentPeriod { get; set; }
        [JsonProperty("updatedLeaderboardVersion")] public int UpdatedLeaderboardVersion { get; set; }
        [JsonProperty("updatedLeaderboardId")] public string UpdatedLeaderboardId { get; set; }
    }

    [System.Serializable]
    public class BCLeaderboardUserData
    {
        [JsonProperty("playerId")] public string UserId { get; set; }
        [JsonProperty("score")] public int Score { get; set; }
        [JsonProperty("name")] public string UserName { get; set; }
        [JsonProperty("pictureUrl")] public string AvatarUrl { get; set; }
        [JsonProperty("summaryFriendData")] public BCSummaryFriendData SummaryFriendData { get; set; }
        [JsonProperty("rank")] public int Rank { get; set; }
    }

    [System.Serializable]
    public class BCCompEventSubmitResponse : BCRunScriptResponse<BCCompEventLeaderboardData>
    {
    }

    [System.Serializable]
    public class BCCompEventSubmitData : BCBasicResponseData
    {
        [JsonProperty("fireLeagueTotalCount")] public int FireLeagueTotalCount { get; set; }
        [JsonProperty("userDataList")] public List<BCLeaderboardUserData> UserDataList { get; set; }       
        [JsonProperty("updatedLeaderboardVersion")] public int UpdatedLeaderboardVersion { get; set; }
        [JsonProperty("updatedLeaderboardId")] public string UpdatedLeaderboardId { get; set; }        
    }

    [Serializable]
    public class BCLeaderboardsResponse : BCRunScriptResponse<BCLeaderboardsData>
    {
    }

    [Serializable]
    public class BCLeaderboardsData : BCBasicResponseData
    {
        [JsonProperty("playerLeaderboards")] public Dictionary<string, BCPlayerLeaderboardData> PlayerLeaderboards;
        [JsonProperty("groupLeaderboards")] public Dictionary<string, BCGroupLeaderboardData> GroupLeaderboards;
    }

    [Serializable]
    public class BCPlayerLeaderboardData : RealmObject
    {
        [JsonProperty("leaderboard")] public IList<BCPlayerLeaderboardEntry> Leaderboard { get; }
        [JsonProperty("payoutRules")] public IList<BCPayoutRule> PayoutRules { get; }
        [JsonProperty("periodNotFound")] public bool PeriodNotFound { get; set; }
        
        [JsonIgnore] [PrimaryKey] public string Category { get; set; }
        
        public override bool Equals(object other)
        {
            return ReferenceEquals(this, other) || (other is BCPlayerLeaderboardData otherLeaderboard &&
                   PeriodNotFound == otherLeaderboard.PeriodNotFound &&
                   Leaderboard.SafeEqualsBasedOnItems(otherLeaderboard.Leaderboard) &&
                   PayoutRules.SafeEqualsBasedOnItems(otherLeaderboard.PayoutRules));
        }
        
        public override int GetHashCode()
        {
            return HashCode.Combine(Leaderboard.HashBasedOnItems(), PayoutRules.HashBasedOnItems(), PeriodNotFound);
        }
    }
    
    [Serializable]
    public class BCGroupLeaderboardData : RealmObject
    {
        [JsonProperty("leaderboard")] public IList<BCGroupLeaderboardEntry> Leaderboard { get; }
        [JsonProperty("payoutRules")] public IList<BCPayoutRule> PayoutRules { get; }
        [JsonProperty("periodNotFound")] public bool PeriodNotFound { get; set; }
        
        [JsonIgnore] [PrimaryKey] public string Category { get; set; }
        
        public override bool Equals(object other)
        {
            return ReferenceEquals(this, other) || (other is BCGroupLeaderboardData otherLeaderboard &&
                   PeriodNotFound == otherLeaderboard.PeriodNotFound && 
                   Leaderboard.SafeEqualsBasedOnItems(otherLeaderboard.Leaderboard) && 
                   PayoutRules.SafeEqualsBasedOnItems(otherLeaderboard.PayoutRules));
        }
        
        public override int GetHashCode()
        {
            return HashCode.Combine(Leaderboard.HashBasedOnItems(), PayoutRules.HashBasedOnItems(), PeriodNotFound);
        }
    }

    [Serializable]
    public class BCPayoutRule : EmbeddedObject
    {
        [JsonProperty("reward")] public BCRewards Reward { get; set; }
        [JsonProperty("rank")] public BCRank Rank { get; set; }
        
        public override bool Equals(object obj)
        {
            return ReferenceEquals(this, obj) || (obj is BCPayoutRule otherRule &&
                   Reward.SafeEquals(otherRule.Reward) &&
                   Rank.SafeEquals(otherRule.Rank));
        }
        
        public override int GetHashCode()
        {
            return HashCode.Combine(Reward, Rank);
        }
    }

    [Serializable]
    public class BCPlayerLeaderboardEntry : EmbeddedObject
    {
        [JsonProperty("playerId")] public string PlayerId { get; set; }
        [JsonProperty("rank")] public int Rank { get; set; }
        [JsonProperty("name")] public string Name { get; set; }
        [JsonProperty("score")] public int Score { get; set; }
        [JsonProperty("pictureUrl")] public string PictureUrl { get; set; }
        [JsonProperty("summaryFriendData")] public BCSummaryFriendData SummaryFriendData { get; set; }

        public override bool Equals(object obj)
        {
            return ReferenceEquals(this, obj) || (obj is BCPlayerLeaderboardEntry playerLeaderboardEntry &&
                   PlayerId == playerLeaderboardEntry.PlayerId && Rank == playerLeaderboardEntry.Rank &&
                   Name == playerLeaderboardEntry.Name && Score == playerLeaderboardEntry.Score &&
                   PictureUrl == playerLeaderboardEntry.PictureUrl &&
                   SummaryFriendData.SafeEquals(playerLeaderboardEntry.SummaryFriendData));
        }
        
        public override int GetHashCode()
        {
            return HashCode.Combine(PlayerId, Rank, Name, Score, PictureUrl, SummaryFriendData);
        }
    }

    [Serializable]
    public class BCGroupLeaderboardEntry : EmbeddedObject
    {
        [JsonProperty("groupId")] public string GroupId { get; set; }
        [JsonProperty("rank")] public int Rank { get; set; }
        [JsonProperty("groupName")] public string GroupName { get; set; }
        [JsonProperty("score")] public int Score { get; set; }
        [JsonProperty("groupSummaryData")] public BCGroupSummaryData GroupSummaryData { get; set; }

        public override bool Equals(object obj)
        {
            return ReferenceEquals(this, obj) || (obj is BCGroupLeaderboardEntry groupLeaderboardEntry &&
                   GroupId == groupLeaderboardEntry.GroupId && Rank == groupLeaderboardEntry.Rank &&
                   GroupName == groupLeaderboardEntry.GroupName && Score == groupLeaderboardEntry.Score &&
                   GroupSummaryData.SafeEquals(groupLeaderboardEntry.GroupSummaryData));
        }
        
        public override int GetHashCode()
        {
            return HashCode.Combine(GroupId, Rank, GroupName, Score, GroupSummaryData);
        }
    }
}