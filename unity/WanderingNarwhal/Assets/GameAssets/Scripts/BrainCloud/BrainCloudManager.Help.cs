using System;
using BBB.Social.Chat;
using BrainCloud;
using Newtonsoft.Json;

namespace BBB.BrainCloud
{
    public partial class BrainCloudManager
    {
        public void SendHelp(ChatMessage message, string helpType, int amount, Action success,
            Action<BCSendHelpError> failure, bool disableValidation = false)
        {
            var helpEntity = new BCSendHelpData
            {
                UserId = message.Sender.Id,
                HelpType = helpType,
                Amount = amount,
                MessageId = message.Id,
                ChannelId = message.ChannelId,
#if BBB_DEBUG
                DisableValidation = disableValidation,
#endif
            };
            _brainCloudWrapper.RunScript("SendHelp", JsonConvert.SerializeObject(helpEntity), SuccessCallback,
                FailureCallback);
            return;

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                success.SafeInvoke();
            }

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                failure.SafeInvoke(Enum.IsDefined(typeof(BCSendHelpError), code)
                    ? (BCSendHelpError)code
                    : BCSendHelpError.BadRequest);
            }
        }
    }
}