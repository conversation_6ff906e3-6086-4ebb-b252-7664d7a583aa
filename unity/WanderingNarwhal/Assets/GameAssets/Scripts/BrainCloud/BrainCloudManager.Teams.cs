using System;
using BBB.Core;
using BrainCloud;
using BrainCloud.Common;
using GameAssets.Scripts.SocialScreens.Teams.TeamInfo;
using Newtonsoft.Json;

namespace BBB.BrainCloud
{
    public partial class BrainCloudManager
    {
        private const int NotAuthenticated = -1;

        public void CreateGroup(string groupName, string description, TeamType teamType, int requiredLevel, string icon, string country, Action<BCTeamData> success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            const string groupType = BrainCloudConstants.BrainCloudTeamType;
            var isOpenGroup = teamType is TeamType.Public;
            const GroupACL.Access memberAccess = GroupACL.Access.ReadWrite;
            const GroupACL.Access otherAccess = GroupACL.Access.ReadOnly;
            var acl = new GroupACL(otherAccess, memberAccess);
            var summaryData = JsonConvert.SerializeObject(new BCGroupSummaryData
            {
                Description = description,
                RequiredLevel = requiredLevel,
                Icon = icon,
                Country = country
            });

            _brainCloudWrapper.GroupService.CreateGroupWithSummaryData(groupName, groupType, isOpenGroup, acl, EmptyJson, EmptyJson, EmptyJson, summaryData, SuccessCallback, ErrorHandlerCallback((FailureCallback)FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                ProcessError(statusMessage, code, error, cbObject);
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Create group success. jsonResponse={jsonResponse}");
                var response = JsonConvert.DeserializeObject<BCResponse>(jsonResponse);
                var teamData = response.Data.ToObject<BCTeamData>();
                success.SafeInvoke(teamData);
            }
        }

        public void FetchTeamInfo(string teamUid, Action<BCTeamData> success, Action<int> failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke(NotAuthenticated);
                return;
            }

            if (teamUid == null)
            {
                failure.SafeInvoke(ReasonCodes.GROUP_NOT_FOUND);
                return;
            }

            _brainCloudWrapper.GroupService.ReadGroup(teamUid, SuccessCallback, ErrorHandlerCallback((FailureCallback)FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                ProcessError(statusMessage, code, error, cbObject);
                failure.SafeInvoke(code);
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Fetch team info success. jsonResponse={jsonResponse}");
                var response = JsonConvert.DeserializeObject<BCResponse>(jsonResponse);
                var teamData = response.Data.ToObject<BCTeamData>();
                success.SafeInvoke(teamData);
            }
        }

        public void EditGroup(string teamUid, string groupName, string description, TeamType teamType, int requiredLevel, string icon, string country, Action<BCTeamData> success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            var groupData = new BCTeamData
            {
                groupId = teamUid,
                name = groupName,
                groupType = teamType.ToString(),
                summaryData = new BCGroupSummaryData
                {
                    Description = description,
                    RequiredLevel = requiredLevel,
                    Icon = icon,
                    Country = country
                }
            };
            _brainCloudWrapper.RunScript("UpdateGroupData", JsonConvert.SerializeObject(groupData), (Action<BCTeamDataResponse>)SuccessCallback, failure);
            return;

            void SuccessCallback(BCTeamDataResponse teamDataResponse)
            {
                success.SafeInvoke(teamDataResponse.Data.Response.Data);
            }
        }

        public void LeaveGroup(string teamUid, Action success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            DisconnectFromChat(teamUid);
            DisableRTT();
            _brainCloudWrapper.GroupService.LeaveGroup(teamUid, SuccessCallback, ErrorHandlerCallback((FailureCallback)FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                ProcessError(statusMessage, code, error, cbObject);
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Leave team success. jsonResponse={jsonResponse}");
                success.SafeInvoke();
            }
        }

        public void SearchGroupsByName(string searchQuery, Action<BCTeamData[]> success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            var jsonContext = "{\"pagination\":{\"rowsPerPage\":50,\"pageNumber\":1},\"searchCriteria\":{\"name\":{\"$regex\":\""+searchQuery+"\",\"$options\":\"i\"},\"groupType\":\""+BrainCloudConstants.BrainCloudTeamType+"\"},\"sortCriteria\":{\"createdAt\":1,\"updatedAt\":-1}}";
            _brainCloudWrapper.GroupService.ListGroupsPage(jsonContext, SuccessCallback, ErrorHandlerCallback(FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                ProcessError(statusMessage, code, error, cbObject);
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Search groups success. jsonResponse={jsonResponse}");
                var response = JsonConvert.DeserializeObject<BCResponse>(jsonResponse);
                var teamData = response.Data["results"]?["items"]?.ToObject<BCTeamData[]>();
                success.SafeInvoke(teamData);
            }
        }

        public void BrowseGroups(float avgLevelsPerDay, Action<BCTeamData[]> success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            _brainCloudWrapper.RunScript("/teams/GetSuggestions", $"{{\"levelWinsAvg\": {avgLevelsPerDay}}}", SuccessCallback, ErrorHandlerCallback(FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                ProcessError(statusMessage, code, error, cbObject);
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Browse groups success. jsonResponse={jsonResponse}");
                var response = JsonConvert.DeserializeObject<BCResponse>(jsonResponse);
                var teamData = response.Data["response"]["data"]["results"]?.ToObject<BCTeamData[]>();
                success.SafeInvoke(teamData);
            }
        }

        public void JoinGroup(string teamUid, Action<BCTeamData> success, Action<int> failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke(NotAuthenticated);
                return;
            }

            _brainCloudWrapper.GroupService.JoinGroup(teamUid, SuccessCallback, ErrorHandlerCallback((FailureCallback)FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                ProcessError(statusMessage, code, error, cbObject);
                failure.SafeInvoke(code);
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Join group success. jsonResponse={jsonResponse}");
                var response = JsonConvert.DeserializeObject<BCResponse>(jsonResponse);
                var teamData = response.Data.ToObject<BCTeamData>();
                success.SafeInvoke(teamData);
            }
        }

        public void ToggleAdminPrivileges(string teamUid, TeamMemberInfo teamMemberInfo, Action<BCTeamData> success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            _brainCloudWrapper.GroupService.UpdateGroupMember(teamUid, teamMemberInfo.Uid, teamMemberInfo.IsLeader ? BrainCloudGroup.Role.MEMBER : BrainCloudGroup.Role.ADMIN, null, SuccessCallback, ErrorHandlerCallback((FailureCallback)FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                ProcessError(statusMessage, code, error, cbObject);
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Toggle admin success. jsonResponse={jsonResponse}");
                var response = JsonConvert.DeserializeObject<BCResponse>(jsonResponse);
                var teamData = response.Data.ToObject<BCTeamData>();
                success.SafeInvoke(teamData);
            }
        }

        public void KickPlayerFromGroup(string teamUid, TeamMemberInfo teamMemberInfo, bool banUser, Action<BCTeamData> success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            var payload = $"{{\"teamUid\": \"{teamUid}\", \"teamMember\": \"{teamMemberInfo.Uid}\", \"banUser\": {banUser.ToString().ToLower()}}}";
            _brainCloudWrapper.RunScript("/teams/KickFromTeam", payload, SuccessCallback, ErrorHandlerCallback(FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                ProcessError(statusMessage, code, error, cbObject);
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Kick player success. jsonResponse={jsonResponse}");
                var response = JsonConvert.DeserializeObject<BCResponse>(jsonResponse);
                var teamData = response.Data["response"]["data"]?.ToObject<BCTeamData>();
                success.SafeInvoke(teamData);
            }
        }

        public void ResignOwnerGroup(string teamUid, Action<string> success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }
            
            var payload = $"{{\"groupId\": \"{teamUid}\"}}";
            _brainCloudWrapper.RunScript("/teams/ResignLeadership", payload, SuccessCallback, ErrorHandlerCallback(FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                ProcessError(statusMessage, code, error, cbObject);
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Resign owner success. jsonResponse={jsonResponse}");
                var response = JsonConvert.DeserializeObject<BCResponse>(jsonResponse);
                var newOwnerId = response.Data["response"]["data"]["newOwnerId"]?.ToObject<string>();
                if (newOwnerId != null)
                {
                    success.SafeInvoke(newOwnerId);
                }
            }
        }
    }
}