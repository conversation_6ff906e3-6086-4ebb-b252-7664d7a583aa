using BBB;
using BBB.Audio;
using BBB.DI;
using BebopBee;
using BebopBee.Core.Audio;
using UnityEngine.Audio;

namespace Loading.Commands
{
    public class AudioSetupCommand: CommandBase
    {
        public override void Execute(IContext context)
        {
            var incrementalContext = context as IIncrementalContext;
            var config = incrementalContext.Resolve<IConfig>();
            var genericResourcesProvider = incrementalContext.Resolve<GenericResourceProvider>();
            var audioMixers = genericResourcesProvider.GetAllPreloaded<AudioMixer>();
            var audioPlayer = StandardAudioPlayer.Create(audioMixers, new StandardLogger());
            AudioProxy.SetInstances(audioPlayer, audioPlayer, audioPlayer);
            AudioPlayerConfigurator.Configure(audioPlayer, config);
            var genericSounds = genericResourcesProvider.GetPreloaded<AudioContextSettings>(GenericResKeys.GenericSoundsContext);
            var genericVoiceover = genericResourcesProvider.GetPreloaded<AudioContextSettings>(GenericResKeys.GenericVoiceoverContext);
            var genericMusic = genericResourcesProvider.GetPreloaded<AudioContextSettings>(GenericResKeys.GenericMusicContext);
            AudioProxy.AddContext(genericSounds);
            AudioProxy.AddContext(genericVoiceover);
            AudioProxy.AddContext(genericMusic);
            var userSettings = incrementalContext.Resolve<UserSettings>();
            userSettings.RefreshSettingApplication();
            CurrentStatus = CommandStatus.Success;
        }
    }
}