using BBB;
using BBB.DI;
using BebopBee;
using Core.Debug;

namespace Loading.Commands
{
    public class LoadDebugManagersCommand : CommandBase
    {
        public override void Execute(IContext context)
        {
#if BBB_LOG
            var logStopwatch = new LogStopwatch("green");
            logStopwatch.Start();
#endif
            
#if BBB_DEBUG
            var container = context.Resolve<IUnityContainer>();
            var screensManager = context.Resolve<IScreensManager>();
            var crawler = container.gameObject.AddComponent<CrawlerController>();
            crawler.PreInitOnAppStart(screensManager);
#endif
            
            CurrentStatus = CommandStatus.Success;

#if BBB_LOG
            logStopwatch.StopLog("LoadDebugManagersCommand done");
#endif
        }
    }
}