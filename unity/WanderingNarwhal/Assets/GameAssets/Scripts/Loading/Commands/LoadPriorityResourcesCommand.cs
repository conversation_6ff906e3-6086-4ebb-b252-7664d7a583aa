using BBB;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.Modals;
using BebopBee;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.Profiling;

namespace Loading.Commands
{
    public class LoadPriorityResourcesCommand : CommandBase
    {
        private ResourceCache _cache;
        private PriorityResourceProvider _priorityResourceProvider;
        private static bool _initialized;

        protected override async void CommandExecutionStart(IContext context)
        {
            base.CommandExecutionStart(context);

            _cache = (ResourceCache) context.Resolve<IResourceCacheHandle>();
            _cache.BeginCachingPersistentResources();
            _priorityResourceProvider = new PriorityResourceProvider();
            Profiler.BeginSample("priorityResourceProvider.CacheResources");
            _priorityResourceProvider.CacheResources(_cache);
            Profiler.EndSample();

            var incrementalContext = (IIncrementalContext)context;
            incrementalContext.AddServiceToRegister<PriorityResourceProvider>(_priorityResourceProvider);
            incrementalContext.RegisterContext();
            
            Profiler.BeginSample("_cache.ReloadAll");
            if (_initialized)
            {
                _initialized = false;
                await UniTask.NextFrame();
            }
            await _cache.ReloadAllAsync();
            Profiler.EndSample();
            
            _initialized = true;
        }

        public override void Execute(IContext context)
        {
            base.Execute(context);

            if(!_cache.IsLoading() && _initialized)
            {
                // Add ImageLoadingActivity_P to modals manager
                var loadingIndicatorPrefab = _priorityResourceProvider.GetPreloaded<GameObject>(GenericResKeys.LoadingImageIndicator);
                context.Resolve<IModalsManager>().SetLoadingIndicatorPrefab(loadingIndicatorPrefab);
                CurrentStatus = CommandStatus.Success;
            }
        }
    }
}