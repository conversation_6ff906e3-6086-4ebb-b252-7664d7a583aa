using BBB;
using BBB.DI;
using BebopBee;
using Bebopbee.Core.Systems.RpcCommandManager;
using Core.RPC;
using GameAssets.Scripts.Utils;
using UnityEngine.Profiling;

namespace Loading.Commands
{
    public class SetupRpcCommand : CommandBase
    {
        public override void Execute(IContext context)
        {
            var incrementalContext = (IIncrementalContext)context;
                        
            RPCService.SetServerUrl(GameConstants.RPCServerURL);
            RpcComm.SetServerUrl(GameConstants.RPCServerURL);
            var rpcCommandManager = new RpcCommandManager();
            RpcCommands.Initialize(rpcCommandManager);
            
            Profiler.BeginSample("new PoolManager<MemoryStreamPoolAble>");
            var pool = new PoolManager<MemoryStreamPoolAble>(1);
            Profiler.EndSample();
            pool.Name = "StartupGlobalPool";
            Profiler.BeginSample("Prewarm");
            pool.Prewarm();
            Profiler.EndSample();
            incrementalContext.AddServiceToRegister<PoolManager<MemoryStreamPoolAble>>(pool);
            incrementalContext.AddServiceToRegister<RpcCommandManager>(rpcCommandManager);
            
            incrementalContext.RegisterContext();
            
            CurrentStatus = CommandStatus.Success;
        }
    }
}