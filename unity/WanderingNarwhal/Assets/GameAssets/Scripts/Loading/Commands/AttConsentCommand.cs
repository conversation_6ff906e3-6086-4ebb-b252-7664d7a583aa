#if UNITY_IOS && !UNITY_EDITOR
#define UNITY_IOS_DEVICE
#endif

#if UNITY_IOS_DEVICE
using System;
using System.Collections;
using BBB;
using BBB.Core;
using BBB.Loading.Modal;
using Unity.Advertisement.IosSupport;
using UnityEngine;
using UnityEngine.iOS;
#endif

using BBB.DI;
using BebopBee;
namespace Loading.Commands
{
    public sealed class AttConsentCommand : CommandBase
    {
#if UNITY_IOS_DEVICE
        private IContext _context;
        private ICoroutineExecutor _coroutineExecutor;
        private PriorityResourceProvider _priorityResourceProvider;
#endif
        protected override void CommandExecutionStart(IContext context)
        {
            base.CommandExecutionStart(context);

            IsTimeConstraint = false;
            
#if UNITY_IOS_DEVICE
            _priorityResourceProvider = context.Resolve<PriorityResourceProvider>();
            _context = context;
            _coroutineExecutor = context.Resolve<ICoroutineExecutor>();
            if (AttSelectionRequired())
            {
                CheckAttStatus(_context);
            }
            else
            {
                SetRaveFbLoginMode(true);
                CurrentStatus = CommandStatus.Success;
                _priorityResourceProvider?.ReleaseCachedResourcesForAttModal();
            }
#else
            CurrentStatus = CommandStatus.Success;
#endif
        }
#if UNITY_IOS_DEVICE
        private bool AttSelectionRequired()
        {
            var version = Version.Parse(Device.systemVersion);
            return version.Major > 14 || (version.Major == 14 && version.Minor >= 5);
        }

        private IEnumerator RequestAttCoroutine()
        {
            ATTrackingStatusBinding.RequestAuthorizationTracking();
    
            while (ATTrackingStatusBinding.GetAuthorizationTrackingStatus() == ATTrackingStatusBinding.AuthorizationTrackingStatus.NOT_DETERMINED)
            {
                yield return null;
            }
    
            CheckAttStatus(_context);
        }

        private void RequestAtt()
        {
            _coroutineExecutor.StartCoroutine(RequestAttCoroutine());
            _priorityResourceProvider?.ReleaseCachedResourcesForAttModal();
        }

        private void CheckAttStatus(IContext context)
        {
            var status = ATTrackingStatusBinding.GetAuthorizationTrackingStatus();
            
            switch (status)
            {
                case ATTrackingStatusBinding.AuthorizationTrackingStatus.NOT_DETERMINED:
                {
                    var modalsBuilder = context.Resolve<IModalsBuilder>();
                    var controller = modalsBuilder.CreateModalView<TrackPermissionModalController>(ModalsType.TrackPermissionModal);
                    controller.Setup(RequestAtt);
                    controller.ShowModal();
                    break;
                }
                case ATTrackingStatusBinding.AuthorizationTrackingStatus.AUTHORIZED:
                case ATTrackingStatusBinding.AuthorizationTrackingStatus.RESTRICTED:
                case ATTrackingStatusBinding.AuthorizationTrackingStatus.DENIED:
                    CurrentStatus = CommandStatus.Success;
                    SetRaveFbLoginMode();
                    break; 
            }
        }


        private void SetRaveFbLoginMode(bool oldIos = false)
        {
            var attStatus = ATTrackingStatusBinding.GetAuthorizationTrackingStatus();
            var authorized = oldIos || attStatus == ATTrackingStatusBinding.AuthorizationTrackingStatus.AUTHORIZED;
            IronSource.Agent.setConsent(authorized);
            AudienceNetwork.AdSettings.SetAdvertiserTrackingEnabled(authorized);

            if (!oldIos)
            {
                BDebug.Log(LogCat.Consent, "iOS 14.5+ device detected, checking ATT status to determine FB login mode");
                if (authorized /* && !bfgRave.facebookClassicLoginModeEnabled()*/)
                {
                    BDebug.Log(LogCat.Consent, "Changing the FB login mode to classic");
                    // bfgRave.enableFBClassicLoginMode(true);
                }
                else if (attStatus is ATTrackingStatusBinding.AuthorizationTrackingStatus.DENIED or ATTrackingStatusBinding.AuthorizationTrackingStatus.RESTRICTED /*&& bfgRave.facebookClassicLoginModeEnabled()*/)
                {
                    BDebug.Log(LogCat.Consent, "Changing the FB login mode to limited");
                    //bfgRave.enableFBClassicLoginMode(false);
                }
            }
            else
            {
                BDebug.Log(LogCat.Consent, "iOS 14.4 or earlier device detected, ignoring ATT status for FB login mode");
            }
        }
#endif
    }
}