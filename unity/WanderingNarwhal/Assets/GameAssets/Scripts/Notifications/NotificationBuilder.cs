using System.Collections.Generic;

namespace BBB
{
    public static class NotificationKeys
    {
        public const string IdKey = "id";
        public const string TitleKey = "title";
        public const string SubtitleKey = "subtitle";
        public const string MessageKey = "message";
        public const string TimeKey = "time";
        public const string ActionKey = "action";
        public const string ActionParamsKey = "action_params";
        public const string CustomKey = "custom";
        public const string ImageKey = "image";
        public const string PriorityKey = "priority";
        public const string SoundKey = "sound";
        public const string CategoryKey = "category";
        public const string NotifTypeKey = "notif_type";
        public const string BadgeKey = "badge";
    }

    public class Notification
    {
        public string Id { get; internal set; }
        public string NotifType { get; internal set; }
        public string Title { get; internal set; }
        public string Message { get; internal set; }
        public string Subtitle { get; internal set; }
        public int Time { get; internal set; }
        public string LoadingAction { get; internal set; }
        public Dictionary<string, object> Custom { get; internal set; }
        public int Priority { get; internal set; }
        public string Image { get; internal set; }
        public string SoundName { get; internal set; }
        public string Category { get; internal set; }
        public string LoadingActionParams { get; internal set; }
        public string LargeIcon { get; internal set; }

        public int BadgeNumber { get; internal set; }

        public Notification()
        {
        }

        public string SerializeToJson()
        {
            var data = new Dictionary<string, object>()
            {
                {NotificationKeys.IdKey, Id},
                {NotificationKeys.NotifTypeKey, NotifType},
                {NotificationKeys.TitleKey, Title},
                {NotificationKeys.MessageKey, Message},
                {NotificationKeys.SubtitleKey, Subtitle},
                {NotificationKeys.TimeKey, Time},
                {NotificationKeys.ActionKey, LoadingAction},
                {NotificationKeys.ActionParamsKey, LoadingActionParams},
                {NotificationKeys.CustomKey, Custom},
                {NotificationKeys.PriorityKey, Priority},
                {NotificationKeys.ImageKey, Image},
                {NotificationKeys.SoundKey, SoundName},
                {NotificationKeys.CategoryKey, Category},
                {NotificationKeys.BadgeKey, BadgeNumber}
            };
            return MiniJSON.Json.Serialize(data);
        }
    }

    public class NotificationBuilder
    {
        private readonly Notification _notification;

        public NotificationBuilder()
        {
            _notification = new Notification();
        }

        public NotificationBuilder WitId(string id)
        {
            _notification.Id = id;
            return this;
        }
        public NotificationBuilder WitType(string notifType)
        {
            _notification.NotifType = notifType;
            return this;
        }

        public NotificationBuilder Title(string title)
        {
            _notification.Title = title;
            return this;
        }

        public NotificationBuilder Message(string message)
        {
            _notification.Message = message;
            return this;
        }

        public NotificationBuilder Subtitle(string subtitle)
        {
            _notification.Subtitle = subtitle;
            return this;
        }

        public NotificationBuilder ScheduleIn(int time)
        {
            _notification.Time = time;
            return this;
        }

        public NotificationBuilder WithLargeIcon(string largeIconName)
        {
            _notification.LargeIcon = largeIconName;
            return this;
        }

        public NotificationBuilder Priority(int priority)
        {
            _notification.Priority = priority;
            return this;
        }

        public NotificationBuilder WithCategory(string category)
        {
            _notification.Category = category;
            return this;
        }

        public NotificationBuilder WithImage(string image)
        {
            _notification.Image = image;
            return this;
        }

        public NotificationBuilder WithBadgeNumber(int badgeNumber)
        {
            _notification.BadgeNumber = badgeNumber;
            return this;
        }

        public NotificationBuilder WithSound(string soundName)
        {
            _notification.SoundName = soundName;
            return this;
        }

        public NotificationBuilder WithLoadingAction(string action, string actionParams)
        {
            _notification.LoadingAction = action;
            _notification.LoadingActionParams = actionParams;
            return this;
        }

        public NotificationBuilder WithCustomParameters(Dictionary<string, object> custom)
        {
            _notification.Custom = custom;
            return this;
        }

        public Notification Build()
        {
            return _notification;
        }
    }
}