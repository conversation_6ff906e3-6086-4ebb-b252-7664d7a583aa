using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using System.Linq;
using System.Security.Cryptography;
using BBB;
using BBB.AssetBundles;
using BBB.Core.AssetBundles;
using BBB.CSVSerialization;
using Core.Configs;
using FBConfig;
using Google.FlatBuffers;
using PBConfig;
using V1 = AssetBundleGraph;
using Model = UnityEngine.AssetGraph.DataModel.Version2;


namespace UnityEngine.AssetGraph
{
    [CustomNode("Build/Generate Bundles Config", 100)]
    public class GenerateBundlesConfig : Node, Model.NodeDataImporter
    {
        [SerializeField] private SerializableMultiTargetString m_exportPath;

        public override string ActiveStyle
        {
            get { return "node 0 on"; }
        }

        public override string InactiveStyle
        {
            get { return "node 0"; }
        }

        public override string Category
        {
            get { return "Export"; }
        }

        public override Model.NodeOutputSemantics NodeInputType
        {
            get
            {
                return
                    (Model.NodeOutputSemantics)
                    ((uint)Model.NodeOutputSemantics.Assets |
                     (uint)Model.NodeOutputSemantics.AssetBundles);
            }
        }

        public override Model.NodeOutputSemantics NodeOutputType
        {
            get { return Model.NodeOutputSemantics.None; }
        }

        public override void Initialize(Model.NodeData data)
        {
            //Take care of this with Initialize(NodeData)
            m_exportPath = new SerializableMultiTargetString();

            data.AddDefaultInputPoint();
        }

        public void Import(V1.NodeData v1, Model.NodeData v2)
        {
            m_exportPath = new SerializableMultiTargetString(v1.ExporterExportPath);
        }

        public override Node Clone(Model.NodeData newData)
        {
            var newNode = new GenerateBundlesConfig();
            newNode.m_exportPath = new SerializableMultiTargetString(m_exportPath);

            newData.AddDefaultInputPoint();

            return newNode;
        }

        public override void OnInspectorGUI(NodeGUI node, AssetReferenceStreamManager streamManager, NodeGUIEditor editor, Action onValueChanged)
        {
            if (m_exportPath == null)
            {
                return;
            }

            var currentEditingGroup = editor.CurrentEditingGroup;

            EditorGUILayout.HelpBox("Generate Bundles Config To Directory: generate bundles config output directory.", MessageType.Info);
            editor.UpdateNodeName(node);

            GUILayout.Space(10f);

            //Show target configuration tab
            editor.DrawPlatformSelector(node);
            using (new EditorGUILayout.VerticalScope(GUI.skin.box))
            {
                var disabledScope = editor.DrawOverrideTargetToggle(node, m_exportPath.ContainsValueOf(currentEditingGroup), (bool enabled) =>
                {
                    using (new RecordUndoScope("Remove Target Export Settings", node, true))
                    {
                        if (enabled)
                        {
                            m_exportPath[currentEditingGroup] = m_exportPath.DefaultValue;
                        }
                        else
                        {
                            m_exportPath.Remove(currentEditingGroup);
                        }

                        onValueChanged();
                    }
                });

                using (disabledScope)
                {
                    EditorGUILayout.LabelField("Bundles Path:");

                    string newExportPath = null;

                    newExportPath = editor.DrawFolderSelector("", "Select Bundles Folder",
                        m_exportPath[currentEditingGroup],
                        Application.dataPath + "/../",
                        (string folderSelected) =>
                        {
                            var projectPath = Directory.GetParent(Application.dataPath).ToString();

                            if (projectPath == folderSelected)
                            {
                                folderSelected = string.Empty;
                            }
                            else
                            {
                                var index = folderSelected.IndexOf(projectPath);
                                if (index >= 0)
                                {
                                    folderSelected = folderSelected.Substring(projectPath.Length + index);
                                    if (folderSelected.IndexOf('/') == 0)
                                    {
                                        folderSelected = folderSelected.Substring(1);
                                    }
                                }
                            }

                            return folderSelected;
                        }
                    );
                    if (newExportPath != m_exportPath[currentEditingGroup])
                    {
                        using (new RecordUndoScope("Change Bundles Path", node, true))
                        {
                            m_exportPath[currentEditingGroup] = newExportPath;
                            onValueChanged();
                        }
                    }
                }
            }
        }

        public override void Prepare(BuildTarget target,
            Model.NodeData node,
            IEnumerable<PerformGraph.AssetGroups> incoming,
            IEnumerable<Model.ConnectionData> connectionsToOutput,
            PerformGraph.Output Output)
        {
            ValidateExportPath(
                m_exportPath[target],
                GetExportPath(target, m_exportPath[target]),
                () => throw new NodeException("Export Path is empty.", "Set valid export path from inspector.", node),
                () => throw new NodeException("Directory set to Export Path does not exist. Path:" + m_exportPath[target],
                    "Create exporting directory or set valid export path from inspector.", node));
        }

        public override void Build(BuildTarget target,
            Model.NodeData node,
            IEnumerable<PerformGraph.AssetGroups> incoming,
            IEnumerable<Model.ConnectionData> connectionsToOutput,
            PerformGraph.Output Output,
            Action<Model.NodeData, string, float> progressFunc)
        {
            Export(target, node, incoming, connectionsToOutput, progressFunc);
        }

        private void Export(BuildTarget target,
            Model.NodeData node,
            IEnumerable<PerformGraph.AssetGroups> incoming,
            IEnumerable<Model.ConnectionData> connectionsToOutput,
            Action<Model.NodeData, string, float> progressFunc)
        {
            var exportPath = GetExportPath(target, m_exportPath[target]);

            // Let's generate BundleIndexConfig based on what asset bundles are included in a build and version it using app version
            var platform = RuntimePlatform.WindowsPlayer;
            if (target == BuildTarget.Android)
                platform = RuntimePlatform.Android;
            else if (target == BuildTarget.iOS)
                platform = RuntimePlatform.IPhonePlayer;

            var streamingAssetPath = Application.streamingAssetsPath.Replace('/', Path.DirectorySeparatorChar);
            var configsStreamingAssetsPath = Path.Combine(streamingAssetPath, "Configs");

            if (!Directory.Exists(configsStreamingAssetsPath))
                Directory.CreateDirectory(configsStreamingAssetsPath);

            Debug.LogFormat("Generating LocalBundleIndexConfig");
            var configMetadataFile = Path.Combine(configsStreamingAssetsPath, $"{ConfigManifest.ConfigMetadataName}.bin");

            var serializer = new FlatbufferSerializer();
            var configMetadataDict = serializer.DeserializeFile<FBConfig.ConfigsManifest>(configMetadataFile)?.UnPack();

            configMetadataDict = configMetadataDict ?? new FBConfig.ConfigsManifestT();
            configMetadataDict.Metadata = configMetadataDict.Metadata ?? new List<FBConfig.ConfigMetadataT>();

            var bundleConfigStruct = AssetBundlesMenuItems.GenerateIndexConfigDict(BundleConstants.GetPlatformName(platform), BundleInfo.BundleTypeLocal);

            var configName = "FBLocalBundleIndexConfig";
            var bundleFileHash = SaveBundleIndexFile(bundleConfigStruct.BundleConfig, configName, configsStreamingAssetsPath);
            SaveCsvFile(bundleConfigStruct.BundleConfig, configName);
            UpdateOrCreateMetadata(configMetadataDict.Metadata, configName, bundleFileHash, ConfigTypes.Fixed);

            Debug.LogFormat("Generating LocalBundleInfoConfig");
            var bundleInfoConfigStruct = AssetBundlesMenuItems.GenerateInfoConfigDict(BundleConstants.GetPlatformName(platform), BundleInfo.BundleTypeLocal);

            configName = "FBLocalBundleInfoConfig";
            bundleFileHash = SaveBundleInfoFile(bundleInfoConfigStruct, configName, configsStreamingAssetsPath);
            SaveCsvFile(bundleInfoConfigStruct, configName);
            UpdateOrCreateMetadata(configMetadataDict.Metadata, configName, bundleFileHash, ConfigTypes.Fixed);

            Debug.LogFormat("Generating LocalSpriteAtlasIndexConfig");
            configName = "FBLocalSpriteAtlasIndexConfig";
            var spriteAtlasFileHash = SaveSpriteAtlasInfoFile(bundleConfigStruct.AtlasConfig, configName, configsStreamingAssetsPath);
            UpdateOrCreateMetadata(configMetadataDict.Metadata, configName, spriteAtlasFileHash, ConfigTypes.Fixed);

            configMetadataDict.Version = CurrentBundleVersion.GetVersion();
            configMetadataDict.VersionCode = int.Parse(CurrentBundleVersion.GetBuildCode());

            int CompareSort(FBConfig.ConfigMetadataT x, FBConfig.ConfigMetadataT y)
            {
                return string.Compare(x.Uid, y.Uid, StringComparison.Ordinal);
            }

            configMetadataDict.Metadata.Sort(CompareSort);
            var bytes = configMetadataDict.SerializeToBinary();
            using (var file = File.Create(configMetadataFile))
            {
                file.Write(bytes, 0, bytes.Length);
            }


            var allExistingFiles = GetAllFilesFrom(configsStreamingAssetsPath);
            var localFilenames = new[] { "FBLocalBundleIndexConfig", "FBLocalBundleInfoConfig", "FBLocalSpriteAtlasIndexConfig" };
            foreach (var file in allExistingFiles)
            {
                var filename = Path.GetFileName(file);
                if (!filename.Contains("-"))
                    continue;
                var fileConfigName = filename[..filename.LastIndexOf('-')];
                if (localFilenames.Any(_ => _ == fileConfigName))
                {
                    bool Compare(FBConfig.ConfigMetadataT obj)
                    {
                        return obj.Uid == fileConfigName;
                    }

                    var idx = configMetadataDict.Metadata.FindIndex(Compare);
                    if (idx > -1 && !file.Contains(configMetadataDict.Metadata[idx].Hash))
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch (Exception e)
                        {
                            Debug.LogException(e);
                        }
                    }
                }
            }

            AssetDatabase.Refresh();
        }

        private void SaveCsvFile(BundleIndexConfigDictT bundleConfig, string configName)
        {
            SaveCsvFile(bundleConfig.Values, configName);
        }

        private void SaveCsvFile(BundleInfoConfigDictT bundleInfo, string configName)
        {
            SaveCsvFile(bundleInfo.Values, configName);
        }

        private void SaveCsvFile(IEnumerable<object> values, string configName)
        {
            try
            {
                var serializer = new CSVSerializer(new CSVSettings(';', '\n', true),
                    new BoolCSVType(),
                    new NumberCSVType(),
                    new StringCSVType(),
                    new ObjectCSVType());

                var csv = serializer.Serialize(values);
                var folder = Path.Combine(AssetBundlesPreProcess.AssetBundlesPath, "Report");
                if (!Directory.Exists(folder))
                    Directory.CreateDirectory(folder);
                var csvPath = Path.Combine(folder, $"{configName}-{Application.version}-{CurrentBundleVersion.GetCommitHash()}.csv");
                File.WriteAllText(csvPath, csv);
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        private string SaveSpriteAtlasInfoFile(FBConfig.SpriteAtlasIndexConfigDictT atlasConfig, string configName, string configsStreamingAssetsPath)
        {
            var spriteAtlasInfoOffset = new Google.FlatBuffers.Offset<FBConfig.SpriteAtlasIndexConfig>[atlasConfig.Values.Count];
            var builder = new Google.FlatBuffers.FlatBufferBuilder(1024);
            var i = 0;
            foreach (var val in atlasConfig.Values)
            {
                var uid = builder.CreateString(val.Uid);
                var sprites = new StringOffset[val.Sprites.Count];
                var j = 0;
                foreach (var dep in val.Sprites)
                {
                    sprites[j] = builder.CreateString(dep);
                    j++;
                }

                var spritesOffset = FBConfig.SpriteAtlasIndexConfig.CreateSpritesVector(builder, sprites);
                var info = FBConfig.SpriteAtlasIndexConfig.CreateSpriteAtlasIndexConfig(builder, uid, spritesOffset);
                spriteAtlasInfoOffset[i] = info;
                i += 1;
            }

            var vector = FBConfig.SpriteAtlasIndexConfig.CreateSortedVectorOfSpriteAtlasIndexConfig(builder, spriteAtlasInfoOffset);
            var root = FBConfig.SpriteAtlasIndexConfigDict.CreateSpriteAtlasIndexConfigDict(builder, vector);
            builder.Finish(root.Value);
            var bytes = builder.SizedByteArray();
            return SaveFile(bytes, configName, configsStreamingAssetsPath);
        }

        private string SaveBundleInfoFile(FBConfig.BundleInfoConfigDictT bundleInfoConfigStruct, string configName, string configsStreamingAssetsPath)
        {
            var bundleInfosOffset = new Google.FlatBuffers.Offset<FBConfig.BundleInfoConfig>[bundleInfoConfigStruct.Values.Count];
            var builder = new Google.FlatBuffers.FlatBufferBuilder(1024);
            var i = 0;
            foreach (var val in bundleInfoConfigStruct.Values)
            {
                var uid = builder.CreateString(val.Uid);
                var hash = builder.CreateString(val.Hash);
                var bundleType = builder.CreateString(val.BundleType);
                var dependencies = new StringOffset[val.Dependencies.Count];
                var j = 0;
                foreach (var dep in val.Dependencies)
                {
                    dependencies[j] = builder.CreateString(dep);
                    j++;
                }

                var dependenciesOffset = FBConfig.BundleInfoConfig.CreateDependenciesVector(builder, dependencies);
                var info = FBConfig.BundleInfoConfig.CreateBundleInfoConfig(builder, uid, val.Version, dependenciesOffset, hash, bundleType);
                bundleInfosOffset[i] = info;
                i += 1;
            }

            var vector = FBConfig.BundleInfoConfig.CreateSortedVectorOfBundleInfoConfig(builder, bundleInfosOffset);
            var root = FBConfig.BundleInfoConfigDict.CreateBundleInfoConfigDict(builder, vector);
            builder.Finish(root.Value);
            var bytes = builder.SizedByteArray();
            return SaveFile(bytes, configName, configsStreamingAssetsPath);
        }

        private string SaveBundleIndexFile(FBConfig.BundleIndexConfigDictT bundleConfig, string configName, string configsStreamingAssetsPath)
        {
            var bundleInfosOffset = new Google.FlatBuffers.Offset<FBConfig.BundleIndexConfig>[bundleConfig.Values.Count];
            var builder = new Google.FlatBuffers.FlatBufferBuilder(1024);
            var i = 0;
            foreach (var val in bundleConfig.Values)
            {
                var uid = builder.CreateString(val.Uid);
                var bundleName = builder.CreateString(val.Bundlename);
                var bundleType = builder.CreateString(val.BundleType);
                var info = FBConfig.BundleIndexConfig.CreateBundleIndexConfig(builder, uid, bundleName, bundleType);
                bundleInfosOffset[i] = info;
                i += 1;
            }

            var vector = FBConfig.BundleIndexConfig.CreateSortedVectorOfBundleIndexConfig(builder, bundleInfosOffset);
            var root = FBConfig.BundleIndexConfigDict.CreateBundleIndexConfigDict(builder, vector);
            builder.Finish(root.Value);
            var bytes = builder.SizedByteArray();
            return SaveFile(bytes, configName, configsStreamingAssetsPath);
        }

        private void UpdateOrCreateMetadata(List<FBConfig.ConfigMetadataT> metadataList, string configName, string bundleFileHash, ConfigTypes configType)
        {
            bool Compare(FBConfig.ConfigMetadataT obj)
            {
                return obj.Uid == configName;
            }

            var idx = metadataList.FindIndex(Compare);
            FBConfig.ConfigMetadataT metadata;
            if (idx > -1)
            {
                metadata = metadataList[idx];
            }
            else
            {
                metadata = new FBConfig.ConfigMetadataT();
                metadataList.Add(metadata);
            }

            metadata.Hash = bundleFileHash;
            metadata.ConfigType = (int)configType;
            metadata.Uid = configName;
        }

        private static string SaveFile<T>(T config, string bundleName, string streamingAssetsPath, ProtobufSerializer serializer)
        {
            var bytes = serializer.SerializeToBytes<T, ConfigSerializer>(config);
            return SaveFile(bytes, bundleName, streamingAssetsPath);
        }

        private static string SaveFile(byte[] bytes, string bundleName, string streamingAssetsPath)
        {
            var fileHash = "";
            using (var stream = new MemoryStream(bytes))
            {
                SHA1 sha = new SHA1CryptoServiceProvider();
                fileHash = BitConverter.ToString(sha.ComputeHash(stream));
                fileHash = fileHash.Replace("-", "").ToLower();
                var filename = $"{bundleName}-{fileHash}.bin";
                var filepath = Path.Combine(streamingAssetsPath, filename);
                File.WriteAllBytes(filepath, bytes);
            }

            return fileHash;
        }

        private HashSet<string> GetAllFilesFrom(string exportPath)
        {
            var ret = new HashSet<string>();
            var files = Directory.GetFiles(exportPath, "*", SearchOption.AllDirectories);
            foreach (var f in files)
            {
                if (f.Contains(".manifest") || f == "." || f == ".." || f.EndsWith("Manifest"))
                    continue;
                ret.Add(f);
            }

            return ret;
        }

        private string GetExportPath(BuildTarget target, string path)
        {
            path = path.Replace("{Platform}", BuildTargetUtility.TargetToAssetBundlePlatformName(target));
            if (string.IsNullOrEmpty(path))
            {
                return Directory.GetParent(Application.dataPath).ToString();
            }
            else if (path[0] == '/')
            {
                return path;
            }
            else
            {
                return FileUtility.GetPathWithProjectPath(path);
            }
        }

        public static bool ValidateExportPath(string currentExportFilePath, string combinedPath, Action NullOrEmpty, Action DoesNotExist)
        {
            if (string.IsNullOrEmpty(currentExportFilePath))
            {
                NullOrEmpty();
                return false;
            }

            if (!Directory.Exists(combinedPath))
            {
                DoesNotExist();
                return false;
            }

            return true;
        }
    }
}