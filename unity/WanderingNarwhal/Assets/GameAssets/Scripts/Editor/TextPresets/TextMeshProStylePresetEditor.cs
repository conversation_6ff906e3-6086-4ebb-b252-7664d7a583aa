using GameAssets.Scripts.TextPresets;
using UnityEditor;
using UnityEngine;

namespace GameAssets.Scripts.Editor.TextPresets
{
    [CustomEditor(typeof(TextMeshProStylePreset))]
    public class TextMeshProStylePresetEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();
            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("Batch Tools", EditorStyles.boldLabel);

            if (GUILayout.Button("Apply This Preset To All Matching Prefabs"))
            {
                var preset = (TextMeshProStylePreset)target;
                var count = preset.ApplyToAllMatchingPrefabs();
                Debug.Log($"Applied style preset to {count} prefabs");
            }
        }
    }
}