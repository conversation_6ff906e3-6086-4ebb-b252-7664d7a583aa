using UnityEditor;
using UnityEngine;

namespace BBBEditor
{
    public class ListOfHotkeysWindow : EditorWindow
    {
        [MenuItem("BebopBee/List Of All Debug Hotkeys")]
        public static void Create()
        {
            GetWindow<ListOfHotkeysWindow>("Hotkeys");
        }

        private void OnEnable()
        {
            minSize = new Vector2(800, 100);
        }

        private void OnGUI()
        {
            EditorGUIUtility.labelWidth = 350;
            EditorGUILayout.LabelField("Hold [B]", "Delay any new non-immediate modal display");
            EditorGUILayout.LabelField("[Tab]", "Increase timescale 2x. If it is larger that 8, then it will be reset to 0.5");
            EditorGUILayout.LabelField("[R] In Ranks modal", "Trigger start rank up animation. Own score item will be moved up by 1");
            EditorGUILayout.LabelField("[R]+[Left Ctrl] In Ranks modal", "Same as regular [R] but distance will be increased to 2");
            EditorGUILayout.LabelField("[W]", "Force fetch inbox from server. Usually inbox is auto-fetched every 30s");
            EditorGUILayout.LabelField("Hold [Left Shift] + any Url Open",
                "Block Url Open in system browser. Can be used, for example, to complete Instagram or Facebook in-game task without launching browser.");
            EditorGUILayout.LabelField("[Z] In DailyEventPopup", "Forse finish current daily event level. Modal needs to be reopened to display the change.");
            EditorGUILayout.LabelField("[Enter] In Confirmation panel of SyncServerModal", "Skip entering 'OK' confirmation and load the game state");
            EditorGUILayout.LabelField("[number key]+[F4]","Trigger Add 1 star to corresponding Passport Game Event stamp. num key 1 - oscars event, 2 - fennec in love, 3 - koalas.");
            //EditorGUILayout.LabelField("","");
        }
    }
}