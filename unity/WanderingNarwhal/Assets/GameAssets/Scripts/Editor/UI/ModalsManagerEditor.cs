using BBB.UI.Core;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

public class ModalsManagerEditor : EditorWindow {

    private Dictionary<string, List<GameObject>> _mappedModals = new Dictionary<string, List<GameObject>>();
    private Vector2 _scrollPos;

    [SerializeField] private TreeViewState _modalTreeViewState;

    TreeViewTest _treeViewTest;

    [MenuItem("BebopBee/Modals Manager")]
	public static void ShowWindow()
    {
        EditorWindow window = EditorWindow.GetWindow(typeof(ModalsManagerEditor));
        window.titleContent.text = "Modals Manager";
    }



    public void OnFocus()
    {
        if (_mappedModals.Count == 0)
        {
            FindModals();
            RefreshView();
        }
    }

    public void FindModals()
    {
        _mappedModals.Clear();
        string[] allAssets = GetAllPrefabs();
        List<string> allAssetsTest = new List<string>(allAssets);
        allAssetsTest.ForEach(x => ResolveAsset(x));
    }

    private void RefreshView()
    {
        if (_modalTreeViewState == null)
            _modalTreeViewState = new TreeViewState();

        _treeViewTest = new TreeViewTest(_modalTreeViewState, _mappedModals);
    }

    public void ResolveAsset(string path)
    {      
        UnityEngine.Object prefabObject = AssetDatabase.LoadMainAssetAtPath(path);
        GameObject prefabGameObject;
        try
        {
            prefabGameObject = prefabObject as GameObject;
            Component comp = prefabGameObject.GetComponent(typeof(ViewPresenter));
            if (comp != null)
            {
                string[] pathSplit = path.Split('/');

                if (!_mappedModals.ContainsKey(pathSplit[3]))
                    _mappedModals.Add(pathSplit[3], new List<GameObject>());

                _mappedModals[pathSplit[3]].Add(prefabGameObject);               
            }
        }
        catch
        {
            Debug.Log("Invalid prefab : "+path);
        }
    }

    public void OnGUI()
    {
        if (GUILayout.Button("Refresh Manager", GUILayout.Width(250), GUILayout.Height(25)))
        {
            FindModals();
            RefreshView();
        }

        using (var scrollView = new EditorGUILayout.ScrollViewScope(_scrollPos))
        {
            _scrollPos = scrollView.scrollPosition;

            _treeViewTest.OnGUI(new Rect(0, 0, position.width, position.height));
        }
    }

    public string[] GetAllPrefabs()
    {
        string[] temp = AssetDatabase.GetAllAssetPaths();
        List<string> result = new List<string>();
        foreach (string s in temp)
        {
            if (s.Contains(".prefab") && s.Contains("Modals"))
                result.Add(s);
        }
        return result.ToArray();

        
    }
}

public class TreeViewTest : TreeView
{
    private Dictionary<string, List<GameObject>> _modalMap;

    public TreeViewTest(TreeViewState treeViewState, Dictionary<string, List<GameObject>> modalMap) : base(treeViewState)
    {
        _modalMap = modalMap;
        Reload();
    }

    protected override TreeViewItem BuildRoot()
    {
        var root = new TreeViewItem { id = 0, depth = -1, displayName = "Root" };
        int idIndexer = 1;

        foreach(var pair in _modalMap)
        {
            var modalMapRoot = new TreeViewItem { id = idIndexer++, displayName = pair.Key };
            root.AddChild(modalMapRoot);

            pair.Value.ForEach(x => modalMapRoot.AddChild(new TreeViewItem()
            {
                id = idIndexer++,
                displayName = x.name
            }));
        }

        SetupDepthsFromParentsAndChildren(root);

        return root;
    }
}
