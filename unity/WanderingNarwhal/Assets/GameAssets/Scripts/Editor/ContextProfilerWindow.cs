using System;
using System.Collections.Generic;
using System.Linq;
using BebopBee.Core;
using BebopBee.UnityEngineExtensions;
using UnityEditor;
using UnityEngine;

namespace BBB.GameAssets.Editor
{
    public class ContextProfilerWindow : EditorWindow
    {
        private string _searchPattern;
        

        [MenuItem("BebopBee/Context Profiler")]
        private static void Init()
        {
            var window = (ContextProfilerWindow)GetWindow(typeof(ContextProfilerWindow));
            window.Show();
        }

        private void OnGUI()
        {
            SearchPatternInputField();
            
            if (GUILayout.Button("Find non-null contexts"))
                FindNonNullContexts();
        }

        private void FindNonNullContexts()
        {
            EditorUtility.DisplayProgressBar("Analyzing objects graph", string.Empty, 0f);


            var transforms = Selection.gameObjects.SelectMany(go => go.transform.GetChildrenRecursevely());
            var monoBehaviours = transforms.SelectMany(tf => tf.GetComponents<MonoBehaviour>()).ToList();

            var foundTypes = new HashSet<Type>();

            int index = 0;
            foreach (var monoBehaviour in monoBehaviours)
            {
                ContextProfilerUtility.LogAllActiveContextReferences(monoBehaviour, _searchPattern, foundTypes);
                EditorUtility.DisplayProgressBar("Analyzing objects graph", string.Empty, (float)index / monoBehaviours.Count);
                index++;
            }

            foreach (var key in foundTypes)
            {
                Debug.LogWarningFormat("Found in {0}", key.Name);
            }

            
            EditorUtility.ClearProgressBar();
        }
        
        private void SearchPatternInputField()
        {
            EditorGUILayout.BeginHorizontal();

            EditorGUILayout.TextField("Context Name = ");
            _searchPattern = EditorGUILayout.TextField(_searchPattern);

            EditorGUILayout.EndHorizontal();
        }
    }
}