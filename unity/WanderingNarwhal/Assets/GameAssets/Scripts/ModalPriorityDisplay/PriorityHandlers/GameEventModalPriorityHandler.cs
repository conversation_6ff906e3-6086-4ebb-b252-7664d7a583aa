using System;
using System.Collections;
using BBB;
using BBB.DI;

namespace GameAssets.Scripts.ModalPriorityDisplay.PriorityHandlers
{
    public abstract class GameEventModalPriorityHandler : ModalPriorityHandlerBase
    {
        protected readonly IGameEventManager GameEventManager;

        protected GameEventModalPriorityHandler(IContext context) : base(context)
        {
            GameEventManager = context.Resolve<IGameEventManager>();
        }

        public override bool ShouldShow(ScreenType currentScreen)
        {
            return HasEventWithRequiredStage();
        }

        public override IEnumerator Show(Action<bool> successCallback)
        {
            var success = GameEventManager.TryAutoShowGameEvent();
            successCallback?.Invoke(success);
            yield break;
        }

        protected abstract bool HasEventWithRequiredStage();
    }
}