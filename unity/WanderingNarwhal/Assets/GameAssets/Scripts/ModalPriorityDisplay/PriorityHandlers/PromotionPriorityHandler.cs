using System;
using System.Collections;
using BBB;
using BBB.DI;
using GameAssets.Scripts.Promotions;

namespace GameAssets.Scripts.ModalPriorityDisplay.PriorityHandlers
{
    public sealed class PromotionPriorityHandler : ModalPriorityHandlerBase
    {
        public override bool NonGenericModalIsExpected => true;

        private readonly PromotionManager _promotionManager;
        private readonly PromotionAutoShowPriority _promotionAutoShowPriority;
        private ScreenType _currentScreen;

        public PromotionPriorityHandler(IContext context, PromotionAutoShowPriority promotionAutoShowPriority) : base(context)
        {
            _promotionManager = context.Resolve<PromotionManager>();
            _promotionAutoShowPriority = promotionAutoShowPriority;
        }

        public override bool ShouldShow(ScreenType currentScreen)
        {
            _currentScreen = currentScreen;
            return _promotionManager.ShouldAutoShow(currentScreen, _promotionAutoShowPriority);
        }

        public override IEnumerator Show(Action<bool> successCallback)
        {
            _promotionManager.AutoShow(_currentScreen, _promotionAutoShowPriority);
            successCallback?.Invoke(true);
            yield break;
        }
    }
}