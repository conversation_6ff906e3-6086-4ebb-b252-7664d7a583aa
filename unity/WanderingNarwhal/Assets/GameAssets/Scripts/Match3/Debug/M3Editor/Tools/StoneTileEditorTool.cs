#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using GameAssets.Scripts.Match3.Logic.Tiles;
using Grid = BBB.Grid;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class StoneTileEditorTool : TileTool
    {
        public StoneTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.Stone, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            CreateTile(coords, grid, TileSpeciality.Stone, TileAsset.Stone, prm);
            base.Apply(grid, coords, cardinalDirections, prm);
        }

        protected override void CreateTile(Coords coords, Grid grid, TileSpeciality tileSpeciality,
            TileAsset tileAsset, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null)
            {
                return;
            }

            var count = StoneTile.MinStoneHp;

            if (cell.HasTile())
            {
                count = cell.Tile.GetParam(TileParamEnum.AdjacentHp);
                count++;

                if (count is < StoneTile.MinStoneHp or > StoneTile.MaxStoneHp)
                {
                    count = StoneTile.MinStoneHp;
                }

                cell.HardRemoveTile(0);
            }

            base.CreateTile(coords, grid, tileSpeciality, tileAsset, count);
        }
    }
}
#endif