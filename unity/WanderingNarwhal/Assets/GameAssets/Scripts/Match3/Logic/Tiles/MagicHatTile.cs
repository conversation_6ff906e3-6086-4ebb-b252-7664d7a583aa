using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class MagicHatTile : Tile
    {
        private static readonly List<Coords> AllMagicHats = new();
        
        public MagicHatTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams = null)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.MagicHat;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.MagicHat;
            AddMandatoryParamsTile();
        }

        public override bool CheckApplicability(BoosterItem boosterItem, Cell cell, Grid grid)
        {
            if ((State & (TileState.ChainMod | TileState.IceCubeMod | TileState.SandMod)) != 0) return true;
            return GetParam(TileParamEnum.MagicHatOutOfRabbitsFlag) <= 0;
        }

        protected override bool CanDieRepeatedly()
        {
            return true;
        }
        
        public override bool DisAllowCellBackgroundSpawn()
        {
            return true;
        }
        
        public override bool PreventBackgroundInteraction()
        {
            return true;
        }
        
        public static void MarkMagicHatsOnGridOutOfRabbitsIfNeeded(Grid grid, IRootSimulationHandler events,
            TileHitReactionHandler reactionHandler, GoalsSystem goalSystem)
        {
            var targetLeft = goalSystem.GetLeftGoalCount(GoalType.MagicHat);
            var isOutOfRabbits = targetLeft <= 0;

            if (isOutOfRabbits)
            {
                foreach (var cell in grid.Cells)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.MagicHatMod))
                    {
                        AllMagicHats.Add(cell.Coords);
                    }
                }
            }

            if (AllMagicHats.Count == 0) return;

            reactionHandler.NewBusyCells ??= new List<Cell>(AllMagicHats.Count);
            events.AddAction(new ActionSyncCoords(new List<Coords>(AllMagicHats)));

            foreach (var magicHatCoords in AllMagicHats)
            {
                var cell = grid.GetCell(magicHatCoords);
                cell.Tile.SetParam(TileParamEnum.MagicHatOutOfRabbitsFlag, 1);
                events.AddAction(new ActionChangeTileParam(cell.Tile.Id, cell.Coords,
                    new List<(TileParamEnum, int)> { new(TileParamEnum.MagicHatOutOfRabbitsFlag, 1) }));
                reactionHandler.NewBusyCells.Add(cell);
            }

            AllMagicHats.Clear();
        }

        public override IEnumerable<GeneralizedLayer> GetGeneralizedLayers()
        {
            if (GetParam(TileParamEnum.MagicHatOutOfRabbitsFlag) == 1)
            {
                yield return GeneralizedLayer.ExhaustedMagicHat;
            }
            
            yield return GeneralizedLayer.MagicHat;
        }
    }
}