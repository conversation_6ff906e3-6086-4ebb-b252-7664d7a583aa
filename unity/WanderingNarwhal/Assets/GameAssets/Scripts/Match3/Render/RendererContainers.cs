using System;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Core;
using BBB.DI;
using BBB.UI;
using BBB.UI.Level;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;
using Object = UnityEngine.Object;

namespace BBB.Match3.Renderer
{
    public class RendererContainers : IContextInitializable, IContextReleasable
    {
        private const int RectTransformHierarchyCapacity = 1500;

        private TilesResources _tilesResources;
        private IMatch3SharedResourceProvider _match3ResourceProvider;
        private IConfig _config;
        private Match3ResourceHelper _match3ResourceHelper;
        private SpawnerSettingsManager _spawnerSettingsManager;
        private OverlaysRenderer _overlaysRenderer;
        private Transform _mainContainer;
        private RectTransform _tilesContainer;
        private RectTransform _formingBoostsContainer;

        private Dictionary<FxType, int> _initialFxTypesCountMap;
        private Dictionary<TileLayerState, int> _initialTileStateCountMap;
        private Dictionary<TileLayerState, GoPool> _layersPool;
        private Dictionary<FxType, GoPool> _fxPool;
        private readonly Dictionary<ContainerType, Transform> _containersDict = new();

        public RectTransform FloorsOverlay { get; private set; }
        public RectTransform TilesOverlay { get; private set; }
        public GoPool TilesViewPool { get; private set; }
        public RectTransform FloorsContainer { get; private set; }
        
        private static readonly Dictionary<TileLayerState, int> TileStateCountMap = new()
        {
            [TileLayerState.Normal] = 0,
            [TileLayerState.HorizontalLb] = 5,
            [TileLayerState.VerticalLb] = 5,
            [TileLayerState.Bomb] = 5,
            [TileLayerState.Propeller] = 5,
            [TileLayerState.ColorBomb] = 3,
            [TileLayerState.Litter] = 1,
            [TileLayerState.Blinking] = 1
        };
        
        private static readonly Dictionary<FxType, int> InitialFxTypeCountMap = new()
        {
            [FxType.TripleLineBreaker] = 1,
            [FxType.Whirlpool] = 1,
            [FxType.WhirlpoolSecondWave] = 1,
            [FxType.DoubleLineBreaker] = 1,
            [FxType.Match] = 5,
            [FxType.WindTrail] = 1,
            [FxType.Shovel] = 1,
            [FxType.Balloon] = 1,
            [FxType.LightningBolt] = 2,
            [FxType.BombBombCombine] = 1,
            [FxType.DiscoBallDiscoBallCombine] = 1,
            [FxType.SuperDiscoBallSuperDiscoBallCombine] = 1,
            [FxType.BoostRevealExplosion] = 3,
            [FxType.CircleWave] = 1,
            [FxType.Comet] = 5,
            [FxType.CometExplosion] = 5,
            [FxType.CometStart] = 5,
            [FxType.DoFlyStar] = 5,
            [FxType.Dim] = 1,
            [FxType.StarExplosion] = 5,
            [FxType.LightningHit] = 10,
            [FxType.LightningRainPiece] = 10,
            [FxType.LightningBoltPiece] = 10,
            [FxType.BackgroundRemove] = 0,
            [FxType.BackgroundDoubleRemove] = 0,
            [FxType.PetalRemove] = 0,
            [FxType.ShakeOverlayEffect] = 20,
            [FxType.TileRemove] = 20,
            [FxType.TileStar] = 20,
            [FxType.ScoreText] = 20,
            [FxType.LitterDestroy] = 10,
            [FxType.SmallCrossExplosion] = 5,
            [FxType.PropellerFlight] = 5,
            [FxType.PropellerFlightShadow] = 5,
            [FxType.PropellerDestroy] = 5,
            [FxType.Swap] = 1,
            [FxType.DiscoRushCollect] = 0,
            [FxType.VerticalBooster] = 1,
            [FxType.HorizontalBooster] = 1,
            [FxType.BoosterTrail] = 1
        };

        public void InitializeByContext(IContext context)
        {
            if (_containersDict.Count == 0)
            {
                _overlaysRenderer = context.Resolve<OverlaysRenderer>();
                foreach (var enumValue in new[] { ContainerType.OverlayFx0, ContainerType.OverlayFx1, ContainerType.OverlayFx2 })
                {
                    _containersDict.Add(enumValue, _overlaysRenderer.GetOverlayLayer(enumValue));
                }
            }

            _config = context.Resolve<IConfig>();
            _spawnerSettingsManager = context.Resolve<SpawnerSettingsManager>();
            _tilesResources = context.Resolve<TilesResources>();
            _match3ResourceProvider = context.Resolve<IMatch3SharedResourceProvider>();
            _match3ResourceHelper = context.Resolve<Match3ResourceHelper>();
            _mainContainer = context.Resolve<IGridController>().Transform;
            FloorsContainer = InitAndGetContainer(ContainerType.Floors);
            FloorsOverlay = InitAndGetContainer(ContainerType.FloorsOverlay);
            TilesOverlay = InitAndGetContainer(ContainerType.TilesOverlay);
            _tilesContainer = InitAndGetContainer(ContainerType.Tiles);
            _formingBoostsContainer = InitAndGetContainer(ContainerType.FormingBoosts);
        }

        private static void LeftBottomAlignment(RectTransform rectTransform, Vector2 size, Vector2 offset)
        {
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.zero;
            rectTransform.pivot = Vector2.zero;
            rectTransform.offsetMin = offset;
            rectTransform.sizeDelta = size;
        }

        private RectTransform InitAndGetContainer(ContainerType containerType)
        {
            if (_containersDict.TryGetValue(containerType, out var value))
                return value as RectTransform;

            var rectTransform = _mainContainer.Find(containerType.ToString()) as RectTransform;
            _containersDict[containerType] = rectTransform;

            if (rectTransform == null) return null;
            rectTransform.hierarchyCapacity = RectTransformHierarchyCapacity;
            return rectTransform;
        }

        public void ForceReleaseAllSpawned(bool cleanupPools = false)
        {
            if (_layersPool != null)
            {
                foreach (var kvp in _layersPool)
                {
                    var goPool = kvp.Value;
                    goPool.ForceReleaseAll();
                    if (cleanupPools)
                    {
                        goPool.Cleanup();
                    }
                }
            }
            
            _layersPool?.Clear();
            _layersPool = null;

            if (_fxPool != null)
            {
                foreach (var kvp in _fxPool)
                {
                    var goPool = kvp.Value;
                    goPool.ForceReleaseAll();
                    if (cleanupPools)
                    {
                        goPool.Cleanup();
                    }
                }
            }
            
            _fxPool?.Clear();
            _fxPool = null;
            
            if (TilesViewPool != null) 
            { 
                TilesViewPool.ForceReleaseAll(); 
                TilesViewPool.Cleanup(); 
                TilesViewPool = null; 
            }
        }

        public void ReparentTo(TileView view, ContainerType containerType)
        {
            if (_containersDict.TryGetValue(containerType, out var container))
            {
                view.transform.SetParent(container);
            }
            else
            {
                BDebug.LogError(LogCat.General, $"Container {containerType} not found");
            }
        }

        public void PrewarmEventFx(FxType fxType, int count)
        {
            _fxPool[fxType].Prewarm(count);
        }

        public void SetupContainers(ILevel level)
        {
            var grid = level.Grid;
            var gridSizeInTiles = grid.Size;
            var size = gridSizeInTiles.Multiply(_tilesResources.CellSize);

            LeftBottomAlignment(FloorsContainer, size, Vector2.zero);
            LeftBottomAlignment(FloorsOverlay, size, Vector2.zero);
            LeftBottomAlignment(TilesOverlay, size, Vector2.zero);
            LeftBottomAlignment(_tilesContainer, size, Vector2.zero);
            LeftBottomAlignment(_formingBoostsContainer, size, Vector2.zero);

            TilesViewPool ??= new GoPool(_tilesResources.TileContainerPrefab,
                _tilesContainer, grid.Cells.Count);

            _initialTileStateCountMap ??= CreateInitialLayerStateCountMap(grid.Cells.Count);
            _initialFxTypesCountMap ??= CreateInitialFxTypeCountMap();
            _overlaysRenderer.LoadOverlay(Match3ResKeys.BonusTimeOverlayName);

            var tileStateCountMap = new Dictionary<TileLayerState, int>(_initialTileStateCountMap);
            var fxTypeCountMap = new Dictionary<FxType, int>(_initialFxTypesCountMap);

            foreach (var cell in grid.Cells)
            {
                if (cell.IsAnyOf(CellState.BackOne))
                    fxTypeCountMap[FxType.BackgroundRemove]++;

                if (cell.IsAnyOf(CellState.BackDouble))
                    fxTypeCountMap[FxType.BackgroundDoubleRemove]++;

                if (cell.IsAnyOf(CellState.Petal))
                {
                    fxTypeCountMap[FxType.PetalRemove]++;
                    fxTypeCountMap.AddSafe(FxType.PetalAnticipation, 1);
                }

                if (cell.IsAnyOf(CellState.Ivy))
                    fxTypeCountMap.AddSafe(FxType.IvyDestroy, 1);

                if (ReferenceEquals(cell.Tile, null))
                    continue;

                var assetLayerState = cell.Tile.Speciality.ToLayerState();
                var stateLayerState = cell.Tile.State.ToLayerState();

                var count = tileStateCountMap.GetSafe(assetLayerState);
                if (count < grid.Cells.Count)
                    tileStateCountMap.AddSafe(assetLayerState, 1);

                count = tileStateCountMap.GetSafe(stateLayerState);
                if (count < grid.Cells.Count)
                    tileStateCountMap.AddSafe(stateLayerState, 1);

                ApplyTileToFxTypeCountMap(cell.Tile, fxTypeCountMap);

                fxTypeCountMap.AddSafe(FxType.TileRemove, 1);
                fxTypeCountMap.AddSafe(FxType.TileStar, 1);
                fxTypeCountMap.AddSafe(FxType.ScoreText, 1);
            }

            const int amountOfFxInGrid = 5;
            foreach (var fxType in GetSingleInstanceFxTypes())
            {
                fxTypeCountMap.AddSafe(fxType, amountOfFxInGrid);
            }

            var tileResourceInfos = _match3ResourceHelper.GetTileResourceInfos(level, _spawnerSettingsManager.SpawnerSettings);

            _layersPool ??= new Dictionary<TileLayerState, GoPool>(tileResourceInfos.Count);

            foreach (var tileResourceInfo in tileResourceInfos)
            {
                var state = tileResourceInfo.State;

                if (_layersPool.ContainsKey(state))
                    continue;

                var order = tileResourceInfo.DefaultOrder;
                var prefabName = tileResourceInfo.PrefabName;
                var prefab = _match3ResourceProvider.GetPrefab(prefabName);

                if (prefab == null)
                {
                    BDebug.LogError(LogCat.Match3, $"Prefab for tile state '{prefabName}' is null");
                    continue;
                }

                _layersPool[state] = new GoPool(prefab, _tilesContainer, tileStateCountMap.GetSafe(state), OnCreate);
                continue;

                void OnCreate(GameObject createdInstance)
                {
                    ApplySorting(order, createdInstance);
                }
            }

            _fxPool ??= new Dictionary<FxType, GoPool>();

            foreach (var fxType in _match3ResourceHelper.GetUsedFxTypes(level, _config, _spawnerSettingsManager.SpawnerSettings))
            {
                if (_fxPool.TryGetValue(fxType, out var fxPool))
                {
                    if (fxTypeCountMap.TryGetValue(fxType, out var fxCount))
                        fxPool.Prewarm(fxCount);
                    continue;
                }

                var prefabName = fxType.ToPrefabName();
                var prefab = _match3ResourceProvider.GetPrefab(prefabName);
                if (prefab == null)
                {
                    BDebug.LogError(LogCat.Match3, $"Fx prefab is null: '{prefabName}'");
                    continue;
                }

                var containerType = fxType.GetContainerType();
                var rectTransform = InitAndGetContainer(containerType);
                var canvas = rectTransform.GetComponent<Canvas>();
                var containerSortingOrder = canvas.sortingOrder;
                if (!_containersDict.ContainsKey(containerType))
                {
                    LeftBottomAlignment(rectTransform, size, Vector2.zero);
                }

                var fxFound = fxTypeCountMap.TryGetValue(fxType, out var count);

                if (!fxFound)
                {
                    UnityEngine.Debug.LogWarning($"Fx {fxType} was not found in the count map");
                }

                _fxPool[fxType] = new GoPool(prefab, rectTransform, Math.Max(1, count), OnCreate);
                continue;

                void OnCreate(GameObject createdInstance)
                {
                    ApplySorting(containerSortingOrder, createdInstance);
                }
            }
        }

        private static IEnumerable<FxType> GetSingleInstanceFxTypes()
        {
            yield return FxType.LineBreaker;
            yield return FxType.DoubleBomb;
            yield return FxType.CollectEventTile;
            yield return FxType.DoFlyResource;
        }

        private static void ApplyTileToFxTypeCountMap(Tile tile, Dictionary<FxType, int> map)
        {
            foreach (var fxType in tile.ToLayerState().ToFxTypes())
            {
                map.AddSafe(fxType, 1);
            }
        }

        private static Dictionary<TileLayerState, int> CreateInitialLayerStateCountMap(int totalCount)
        {
            TileStateCountMap[TileLayerState.Normal] = totalCount;
            return TileStateCountMap;
        }

        private static Dictionary<FxType, int> CreateInitialFxTypeCountMap()
        {
            return InitialFxTypeCountMap;
        }

        private static void ApplySorting(int sortingValue, GameObject instance)
        {
            var canvas = instance.GetComponent<Canvas>();
            if (canvas != null)
            {
                canvas.overrideSorting = true;
                canvas.sortingOrder += sortingValue;
            }

            foreach (var child in instance.transform.GetChildrenRecursevely())
            {
                var spriteRenderer = child.GetComponent<UnityEngine.Renderer>();
                if (spriteRenderer != null)
                    spriteRenderer.sortingOrder += sortingValue;
            }
        }

        public void ReleaseByContext(IContext context)
        {
            ForceReleaseAllSpawned(true);
            foreach (var child in _mainContainer.GetComponentsInChildren<Transform>())
            {
                Object.Destroy(child.gameObject);
            }
        }

        public Transform GetContainer(ContainerType containerType)
        {
            if (_containersDict.TryGetValue(containerType, out var result))
                return result;

            throw new Exception($"Container {containerType} not found");
        }

        public GoPool GetLayersPool(TileLayerState state)
        {
            try
            {
                return _layersPool[state];
            }
            catch
            {
                UnityEngine.Debug.LogError($"{state} not found in _layersPool");
                throw;
            }
        }

        public GoPool GetFxPool(FxType type)
        {
            return _fxPool[type];
        }

        public TObject SpawnFx<TObject>(FxType type) where TObject : Component
        {
            try
            {
                var obj = _fxPool[type].Spawn<TObject>();
                return obj;
            }
            catch (KeyNotFoundException e)
            {
                UnityEngine.Debug.LogError($"FxType key {type} not found: " + e.Message);
            }

            return null;
        }

        public GameObject SpawnFx(FxType type)
        {
            try
            {
                var obj = _fxPool[type].Spawn();
                //YA: Make obj transform scale 0 so that there is no glitch before the scale curve gets applied 
                obj.transform.localScale = Vector3.zero;
                return obj;
            }
            catch (KeyNotFoundException e)
            {
                UnityEngine.Debug.LogError($"FxType key {type} not found: " + e.Message);
            }

            return null;
        }
    }
}