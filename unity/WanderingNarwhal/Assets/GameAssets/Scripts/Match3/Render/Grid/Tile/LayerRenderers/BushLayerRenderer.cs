namespace BBB.Match3.Renderer
{
    public class BushLayerRenderer : DelayedAppearLayerRenderer
    {
        private const string BushShowTile = "BushTile_Show";

        public override void Init(Tile tile)
        {
            base.Init(tile);
            if (SpineAnimator != null)
            {
                SpineAnimator.Skeleton.SetToSetupPose();
                SpineAnimator.AnimationState.ClearTracks();
            }
        }

        public override void Show()
        {
            if (SpineAnimator != null)
            {
                SpineAnimator.AnimationState.SetAnimation(0, BushShowTile, false);
            }
        }
    }
}