using System.IO;
using System.Linq;
using System.Reflection;
using BBB;
using BBB.Core;
using BBB.Match3.Renderer;
using BBB.UI;
using BebopBee.Core;
using BebopBee.Core.Audio;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Match3.Settings;
using UnityEditor;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    [CustomEditor(typeof(GoalIconFlySettingsScriptableObject))]
    public class GoalIconFlySettingsInspector : UnityEditor.Editor
    {
        private GoalIconFlySettingsScriptableObject _target;
        private TileResourceSelector _tileResourceSelector;
        private TilesResources _tilesResources;
        private GoalsAnimationsSettingsList _goalsSettings;
        private RendererContainers _rendererContainers;

        private void OnEnable()
        {
            _target = target as GoalIconFlySettingsScriptableObject;
        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            GUI.enabled = Application.isPlaying;
            if (GUILayout.Button("Play preview"))
            {
                PlayPreviewAsync().Forget();
            }

            GUI.enabled = false;
        }

        public async UniTask PlayPreviewAsync()
        {
            const string cellName = "CellPrefab(Clone)";
            const string goalName = "GoalItem(Clone)";

            var cells = FindObjectsOfType<RectTransform>().Where(go => go.name == cellName).ToArray();
            var goals = FindObjectsOfType<RectTransform>().Where(go => go.name == goalName).ToArray();

            if (cells.Length == 0)
            {
                BDebug.LogError(LogCat.Match3,"No cells found on scene, please load any m3 level in M3LevelEditor");
                return;
            }

            if (goals.Length == 0)
            {
                BDebug.LogError(LogCat.Match3,"No goal target object found on scene");
                return;
            }

            var cell = cells[Random.Range(0, cells.Length)];
            var endPoint = goals[Random.Range(0, goals.Length)];

            var goalsSettings = GetGoalsSettings();
            if (goalsSettings is null)
            {
                BDebug.LogError(LogCat.Match3,"Goal settings asset not found");
                return;
            }

            var tileRes = GetTileRes();
            var goalType = goalsSettings.GetGoalForSettings(_target);
            FxType fx;
            Sprite goalSprite;
            if (goalType == GoalType.None)
            {
                const GoalType defaultGoalType = GoalType.Green;

                // this goal type has associated Default goal animation, so try to compare it.
                goalsSettings.GetSettinsForGoal(defaultGoalType, out var tempFx, out var tempGoalSettings);

                // Check if this is DefaultGoal animation (and not specific custom goal animation).
                if (tempGoalSettings == _target)
                {
                    fx = tempFx;
                    var tileData = await tileRes.GetAsync(defaultGoalType.ToTileKind());
                    goalSprite = tileData.Sprite;                }
                else
                {
                    endPoint = cells[Random.Range(0, cells.Length)];
                    fx = _target.PreviewFx;
                    goalSprite = _target.PreviewSprite;
                }
            }
            else
            {
                goalsSettings.GetSettinsForGoal(goalType, out fx, out _);
                goalSprite = goalsSettings.GetGoalIcon(goalType);
            }

            var grid = FindObjectOfType<GridController>();
            var rendererContainers = GetRendererContainers();

            if (rendererContainers is null)
            {
                BDebug.LogError(LogCat.Match3,"Renderer Containers not found");
                return;
            }
            
            var resourcesSelector = GetTileResSelector();

            var flySettings = _target.ToSettingsData(
                from: cell.transform.position,
                to: endPoint.transform.position,
                fx: fx,
                gridController: grid,
                tileResources: resourcesSelector,
                fxRenderer: null,
                rendererContainers: rendererContainers,
                goalIcon: goalSprite,
                skin: null,
                tilesResources: GetTileRes(),
                onTargetReached: null,
                onEnd: null);

            switch (flySettings.flyMode)
            {
                case GoalFlyMode.GoalFlyAnimator:
                    GoalIconFlyAnimator.Instance.Launch(flySettings);
                    break;
                case GoalFlyMode.DoFlyAnimator:
                    DoFlyAnimator.Instance.Launch(flySettings, playSoundMethod: AudioProxy.PlaySound);
                    break;
                case GoalFlyMode.WaypointAnimator:
                    WaypointFlyAnimator.Instance.Launch(flySettings, -1, Vector3.zero);
                    break;
            }
        }

        private RendererContainers GetRendererContainers()
        {
            if (_rendererContainers is null)
            {
                var levelController = GameObject.FindObjectOfType<LevelControllerBase>();
                if (levelController != null)
                {
                    var type = typeof(LevelControllerBase);
                    var field = type.GetField("_rendererContainers", BindingFlags.Static | BindingFlags.NonPublic);
                    if (field != null)
                    {
                        _rendererContainers = field.GetValue(levelController) as RendererContainers;
                    }
                }
            }

            return _rendererContainers;
        }

        private GoalsAnimationsSettingsList GetGoalsSettings()
        {
            if (_goalsSettings is null)
            {
                _goalsSettings = FindSingleAssetByFilter<GoalsAnimationsSettingsList>("t: GoalsAnimationsSettingsList");
            }

            return _goalsSettings;
        }

        private TileResourceSelector GetTileResSelector()
        {
            if (_tileResourceSelector is null)
            {
                var go = FindSingleAssetByFilter<GameObject>("t:prefab TilesResourcesExtra");
                if (go != null)
                {
                    _tileResourceSelector = go.GetComponent<TileResourceSelector>();
                }
            }

            return _tileResourceSelector;
        }

        private TilesResources GetTileRes()
        {
            if (_tilesResources is null)
            {
                var go = FindSingleAssetByFilter<GameObject>("t:prefab TilesResources", "TilesResources");
                if (go != null)
                {
                    _tilesResources = go.GetComponent<TilesResources>();
                }
            }

            return _tilesResources;
        }

        private static T FindSingleAssetByFilter<T>(string filter, string exactFileName = null) where T: Object
        {
            var guids = AssetDatabase.FindAssets(filter);
            foreach (string guid in guids)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                // Use Path.GetFileNameWithoutExtension to get the name of the file from its path.
                string assetName = Path.GetFileNameWithoutExtension(assetPath);

                // Perform an exact comparison.
                if (exactFileName == null || assetName == exactFileName)
                {
                    // If the names match exactly, load and return the asset.
                    return AssetDatabase.LoadAssetAtPath<T>(assetPath);
                }
            }

            return null;
        }
    }
}