using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using BBB.Core;
using System;

namespace BBB
{
    public class DebugDailyTasksController : DebugPanelController
    {
        [SerializeField] private TMP_InputField _progressInputField;
        [SerializeField] private Dropdown _tasksListDropdown;
        const int ResetValue = 0;
        private string _selectedTaskUid;

        public void PopulateTasksList()
        {
            var tasks = DebugScreenController.DailyTasksManager.GetCurrentTasks();
            var options = new List<Dropdown.OptionData>();
            options.Add(new Dropdown.OptionData("Select Task"));
            foreach (var task in tasks)
            {
                options.Add(new Dropdown.OptionData(task.TaskUid));
            }
            _tasksListDropdown.options = options;
        }

        public void OnSetProgressButton()
        {
            var progress = ParseScoreInputField();

            if (progress <= ResetValue)
            {
                BDebug.LogError(LogCat.General, "Score has to be more than 0");
                return;
            }

            // Increment the progress of the selected task
            var currentTasks = DebugScreenController.DailyTasksManager.GetCurrentTasks();
            
            foreach (var task in currentTasks)
            {
                if (task.TaskUid == _selectedTaskUid)
                {
                    task.Progress += progress;
                    BDebug.Log(LogCat.General, $"Incremented progress for task {task.TaskUid} by {progress}. New progress: {task.Progress}");
                    break;
                }
            }
        }

        public void OnResetAllProgressButton()
        {
            var currentTasks = DebugScreenController.DailyTasksManager.GetCurrentTasks();

            foreach (var task in currentTasks)
            {
                    task.Progress = ResetValue;
                    task.IsClaimed = false;
                    BDebug.Log(LogCat.General, $"Reset progress for task {task.TaskUid}. New progress: {task.Progress}");
            }

            DebugScreenController.TasksState.StreakRewardClaimed = false;
        }

        public void OnChangeDayButton()
        {
            DebugScreenController.DailyTasksManager.SkipToNextDay();
            PopulateTasksList();
        }

        public void OnTaskSelected(int index)
        {
            _selectedTaskUid = _tasksListDropdown.options[index].text;
        }

        public int ParseScoreInputField()
        {
            if (int.TryParse(_progressInputField.text, out var result))
            {
                return result;
            }
            return -1;
        }

        protected override void OnInit()
        {
            base.OnInit();
            PopulateTasksList();
            _tasksListDropdown.onValueChanged.AddListener(OnTaskSelected);
        }
    }
}