using BBB.Core;
using BBB.DI;
using BBB.Views;

namespace BBB.Controllers
{
    public class DebugScreenControllerWrapper : BaseScreensController<IDebugScreenViewPresenter>
    {
        private GenericHudManager _genericHudManager;

        public override void Init(IContext previousContext)
        {
            base.Init(previousContext);

            if (_genericHudManager != null) return;

            _genericHudManager = previousContext.Resolve<GenericHudManager>();
        }

        protected override void OnCloseButtonClicked()
        {
            base.OnCloseButtonClicked();
            ScreensBuilder.ShowPreviousScreen();
        }
    }
}