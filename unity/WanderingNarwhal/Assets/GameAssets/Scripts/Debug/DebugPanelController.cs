namespace BBB
{
    public class DebugPanelController : BbbMonoBehaviour
    {
        protected DebugScreenController DebugScreenController;

        public void Init(DebugScreenController debugScreenController)
        {
            DebugScreenController = debugScreenController;
            OnInit();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            OnClose();
        }

        protected virtual void OnInit()
        {
        }

        protected virtual void OnClose()
        {
        }
    }
}
