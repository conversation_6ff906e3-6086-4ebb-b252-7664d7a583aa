using System.Collections.Generic;
using System.Text;
using BBB.Audio;
using BBB.UI.LoopScrollRect;
using BebopBee.Core.Audio;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace BBB
{
    public class DebugScrollAnimTestController : DebugPanelController
    {
        public LoopScroll ScrollRect;
        public LoopScrollAnimaionController ScrollRectAnim;
        public GameObject ListItemTemplate;
        public Button AddBtn;
        public Button RefreshBtn;
        public TextMeshProUGUI ModelOutput;

        [FormerlySerializedAs("MoveUpByInput")]
        public TMP_InputField SelectedItem;

        public TMP_InputField FocusOnItemInput;
        public Button FocusInstantBtn;
        public Button FocusSmoothBtn;
        private List<string> _profiles = new List<string>();
        private int _profilesCreated;

        private int _lastFocusedItem = 0;

        private void Start()
        {
            ClearAndRefreshList();
            RefreshBtn.onClick.AddListener(ClearAndRefreshList);
            AddBtn.onClick.AddListener(() =>
            {
                _profiles.Add("item_" + _profilesCreated);
                ScrollRect.contentModel.Add(new ListItemData()
                {
                    prefab = ListItemTemplate.GetComponent<ILoopScrollListItem>(),
                    model = null,
                });
                _profilesCreated++;
            });

            FocusInstantBtn.onClick.AddListener(() =>
            {
                int index = 0;
                int.TryParse(FocusOnItemInput.text, out index);
                var normPos = ScrollRect.CalcNormalizedItemPosition(index);
                ScrollRect.SetContentNormalizedPosition(normPos);
                _lastFocusedItem = index;
            });

            FocusSmoothBtn.onClick.AddListener(() => {
                int index = 0;
                int.TryParse(FocusOnItemInput.text, out index);
                var normPos = ScrollRect.CalcNormalizedItemPosition(_lastFocusedItem);
                ScrollRect.SetContentNormalizedPosition(normPos);
                var endNormPos = ScrollRect.CalcNormalizedItemPosition(index);
                var endPos = ScrollRect.CalcContentLocalPositionAtNormalizedPosition(endNormPos);
                ScrollRectAnim.AnimateScrollMotionToLocalPosition(endPos, 2f, Ease.Linear, null);
            });
        }

        [ContextMenu("ClearList")]
        private void ClearAndRefreshList()
        {
            ScrollRect.ClearAllVisibleListItems();
            ScrollRect.contentModel.Clear();

            ScrollRect.onItemSpawnedEvent -= InitItem;
            ScrollRect.onItemSpawnedEvent += InitItem;
            ScrollRect.gameObject.SetActive(true);

            _profiles.Clear();
            _profilesCreated = 0;
        }

        private void Update()
        {
            if (ModelOutput != null)
            {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < _profiles.Count; i++)
                {
                    sb.AppendLine(_profiles[i]);
                }
                ModelOutput.text = sb.ToString();
            }
        }

        private void InitItem(ILoopScrollListItem item, object model, int index)
        {
            var listItem = item as DebugScrollAnimTestListItem;
            if (listItem != null)
            {
                if (index < 0 || index >= _profiles.Count)
                {
                    Debug.LogError("Profile not found at index: " + index);
                    listItem.Init("null", null, null, null, null);
                    return;
                }

                var currProfile = _profiles[index];
                Debug.Log($"Initializing item '{currProfile}' at index '{index}'");
                //var isCollectAvailable = false;
                listItem.Init(currProfile, OnRemoveClick, MoveToEndOnClick, OnMoveUpClick, OnSelect);
            }
        }

        private void OnSelect(string uid)
        {
            SelectedItem.text = uid;
        }

        private void OnRemoveClick(string uid)
        {
            Debug.Log($"Remove item clicked for uid: '{uid}'\nitemsList:\n{string.Join(";\n", _profiles.ToArray())}");
            var index = _profiles.IndexOf((profile) => profile == uid);

            if (index < 0) return;

            Debug.Log($"Removing item at index '{index}'");
            _profiles.RemoveAt(index);
            ScrollRectAnim.AnimatedRemoveItem(index);
        }

        private void MoveToEndOnClick(string uid)
        {
            Debug.Log($"Move to end click for item '{uid}'");
            var index = _profiles.IndexOf((profile) => profile == uid);
            if (index < 0 || index == _profiles.Count - 1) return;

            // Move profile to end and move all profiles in between by 1 position.
            var swap = _profiles[index];
            for (int i = index; i < _profiles.Count - 1; i++)
            {
                _profiles[i] = _profiles[i + 1];
            }
            _profiles[_profiles.Count - 1] = swap;
            ScrollRectAnim.AnimatedMoveItemToPosition(index, _profiles.Count - 1);
        }

        private void OnMoveUpClick(string uid)
        {
            Debug.Log($"Move up click for item '{uid}'");
            int targetIndex = _profiles.IndexOf((profile) => profile == SelectedItem.text);
            if (targetIndex < 0 || targetIndex >= _profiles.Count)
            {
                targetIndex = 0;
                SelectedItem.text = _profiles[0];
            }

            var index = _profiles.IndexOf((profile) => profile == uid);
            if (index < 0 || index >= _profiles.Count) return;
            if (index == targetIndex) return;

            var swap = _profiles[index];
            var delta = targetIndex > index ? 1 : -1;
            for (int i = index; i != targetIndex; i += delta)
            {
                _profiles[i] = _profiles[i + delta];
            }

            _profiles[targetIndex] = swap;

            ScrollRectAnim.AnimatedMoveItemHighlightedWithAutofocus(index, targetIndex, onBeforeItemScaleBack: (_) => { AudioProxy.PlaySound(GenericSoundIds.GenericBurst); },
                onOtherItemDidMoveDown: (_) => { AudioProxy.PlaySound(GenericSoundIds.ScrollTick); });
        }
    }
}