using BebopBee;
using UnityEngine;

namespace BBB
{
    public static class DebugMatch3Settings
    {
        public static bool IsDebugRecording()
        {
            var result = false;
            if (AppDefinesConverter.BbbDebug)
                result = PlayerPrefs.GetInt("match3/recording", 0) == 1;
            return result;
        }

        public static void SetDebugRecording(bool on)
        {
            PlayerPrefs.SetInt("match3/recording", on ? 1 : 0);
            PlayerPrefs.Save();
        }
    }
}