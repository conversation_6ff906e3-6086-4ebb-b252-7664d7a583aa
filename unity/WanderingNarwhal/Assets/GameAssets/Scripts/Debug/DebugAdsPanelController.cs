using BBB.Ads;
using BBB.Core.Ads;
using UnityEngine.UI;

namespace BBB
{
    public class DebugAdsPanelController : DebugPanelController, IAdsListener
    {
        public Text Status;
        public InputField adId;
        private IAdsPlacement _placement;

        protected override void OnInit()
        {
            base.OnInit();
            SetStatus("No Ad");
        }

        private void SetStatus(string status)
        {
            Status.text = status;
        }

        public void ShowTestSuite()
        {
            IronSource.Agent.launchTestSuite();
        }

        public void FetchAd()
        {
            DebugScreenController.AdsManager.RemoveListener(this);
            DebugScreenController.AdsManager.AddListener(this);
            var adUnit = AdsUtil.AdMobRewardedVideoUnits[0];
            _placement = DebugScreenController.AdsManager.CreatePlacement(adId.text, new RewardedVideoAdParams(id: adId.text.GetHashCode(), adUnitId: adUnit));
            _placement?.Preload();
            SetStatus("Fetch Ad");
        }

        public void ShowAd()
        {
            SetStatus("Showing Ad");
            _placement?.ShowAsync("Test");
        }

        private void OnFetchSucceded()
        {
            SetStatus("Ad Ready");
        }

        private void OnFetchFailed()
        {
            SetStatus("Ad Fetch Failed");
        }

        private void OnVideoEnded()
        {
            SetStatus("Ad Ended");
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            DebugScreenController.AdsManager.RemoveListener(this);
        }

        public void OnLoaded(IAdsPlacement placement, IAdsRequestResult result)
        {
            SetStatus(string.Format("OnLoaded Success: {0} desc: {1}", result.Success, result.Description));
        }

        public void OnStarted(IAdsPlacement placement)
        {
            SetStatus("OnStarted");
        }

        public void OnShown(IAdsPlacement placement, IAdsRequestResult result)
        {
            SetStatus(string.Format("OnShown Success: {0} desc: {1}", result.Success, result.Description));
        }

        public void OnDismissed(IAdsPlacement placement)
        {
            SetStatus("OnDismissed");
        }

        public void OnRewarded(IAdsPlacement placement, string currency, double amount)
        {
            SetStatus(string.Format("OnRewarded Currency: {0} amount: {1}", currency, amount));
        }

        public void OnPreLoading(IAdsPlacement placement)
        {
            
        }

        public void OnPreLoadingFinished(IAdsPlacement placement)
        {
            
        }
    }
}