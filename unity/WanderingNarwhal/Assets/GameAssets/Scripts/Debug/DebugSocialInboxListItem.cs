using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class DebugSocialInboxListItem : BbbMonoBehaviour, IScrollable
    {
        public TextMeshProUGUI Description;
        public Button Delete;

        public void Init(string description, Action onDeleteClick)
        {
            Description.text = description;
            Delete.ReplaceOnClick(() => onDeleteClick?.Invoke());
        }

        public void InitItem(int itemId)
        {
        }

        public void UninitItem()
        {
            
        }
    }
}