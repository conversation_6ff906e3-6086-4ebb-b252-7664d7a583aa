using FBConfig;
using UnityEngine;
using UnityEngine.UI;
using BBB;

public class DebugQuestObjectiveObject : BbbMonoBehaviour
{
    [SerializeField] private Text _uidText;
    [SerializeField] private Text _completeConditionText;
    [SerializeField] private Text _completeConditionResult;
    
    private QuestManager _questManager;
    private QuestObjectiveConfig _questConfig;

    public void Setup(QuestManager questManager, QuestObjectiveConfig questObjectiveConfig)
    {
        _questManager = questManager;
        _questConfig = questObjectiveConfig;

        _uidText.text = questObjectiveConfig.Uid;
        _completeConditionText.text = questObjectiveConfig.CompleteCondition;
    }
    
    
}
