using System.Collections.Generic;
using BBB.DI;
using Cysharp.Threading.Tasks;

namespace BBB.Actions
{
    public class OpenExternal : ISimpleAction
    {
#if BBB_DEBUG
        /// <summary>
        /// Used to avoid URL opens and external logins
        /// when crawler bot is playing the game.
        /// </summary>
        public static bool _debugIsExtrnalUrlOpenDisabled = false;
#endif

        private IPlayerManager _playerManager;
        private IUrlOpener _urlOpener;
        private string _url;
        private string _visitingMarker;

        public UniTask ExecuteAsync(IContext context, Dictionary<string, string> actionParams)
        {
            _playerManager = context.Resolve<IPlayerManager>();
            _urlOpener = context.Resolve<IUrlOpener>();

            _url = actionParams.GetSafe("url");
            _visitingMarker = actionParams.GetSafe("visiting_marker");

#if BBB_DEBUG
            if (!_debugIsExtrnalUrlOpenDisabled)
            {
                _urlOpener.OpenUrl(_url);
            }
#else
            _urlOpener.OpenUrl(_url);
#endif
            
            if (!_visitingMarker.IsNullOrEmpty())
                _playerManager.Player.MarkAsVisited(_visitingMarker);

            return UniTask.CompletedTask;
        }
    }
}