using System;
using BBB;
using BBB.DI;
using UnityEngine;
using Object = UnityEngine.Object;

namespace GameAssets.Scripts.Theme
{
    public interface IThemeManager
    {
        event Action OnThemeUnlocked;

        T GetThemedObject<T>(ThemePlacementId placementId, string additionalFilter, string eventUid = "", bool forceEventTheme = false) where T : Object;
        GameObject GetThemedTransition(string prefabName);
        void Initialize(IContext context);
        void Setup(ILockManager lockManager, IGameEventManager gameEventManager);
        void Restart();
    }
}