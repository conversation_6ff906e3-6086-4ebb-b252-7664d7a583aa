using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.Core.AssetBundles;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.UI;
using BebopBee;
using Core.Configs;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.GameEvents.EndGameEvents.Model;
using UnityEngine;
using Object = UnityEngine.Object;

namespace GameAssets.Scripts.Theme
{
    public class ThemeManager : IThemeManager, PBConfig.ISchedulableDataProvider, IBundlePredownloadProvider
    {
        private const string ThemeDefinitionName = "_theme_definition";
        
        private static readonly Type[] RequiredConfigs =
        {
            typeof(ThemeConfig),
            typeof(ThemeMetaConfig)
        };

        public event Action OnThemeUnlocked;

        private readonly List<ThemeDefinition> _themeDefinitions = new();
        private IBundleManager _bundleManager;
        private IAssetsManager _assetsManager;
        private TimeManager _timeManager;
        private IDictionary<string, ThemeConfig> _themeConfig;
        private ThemeMetaConfig _themeMetaConfig;
        private IBundleInfoProvider _bundleInfoProvider;
        private ILockManager _lockManager;
        private IAccountManager _accountManager;
        private IEventDispatcher _eventDispatcher;
        private IScreensBuilder _screensBuilder;
        private IGameEventManager _gameEventManager;
        private IGameEventResourceManager _gameEventResourceManager;
        private BundlesBackgroundDownloaderManager _bundlesBackgroundDownloaderManager;

        private int _totalRelativeDuration;

        public ThemeManager(BundlesBackgroundDownloaderManager bundlesBackgroundDownloaderManager)
        {
            _bundlesBackgroundDownloaderManager = bundlesBackgroundDownloaderManager;
        }

        public void Setup(ILockManager lockManager, IGameEventManager gameEventManager)
        {
            _lockManager = lockManager;
            _gameEventManager = gameEventManager;
            SaveLockStatusLocally();
        }

        private void SaveLockStatusLocally()
        {
            if (_lockManager == null) return;

            bool themeUnlocked = false;
            foreach (ThemeDefinition themeDefinition in _themeDefinitions)
            {
                bool previouslyLocked = IsLocked(themeDefinition.LockItemUid);
                bool isCurrentlyLocked = _lockManager.IsLocked(themeDefinition.LockItemUid, LockItemType.Other);
                themeUnlocked |= (previouslyLocked && !isCurrentlyLocked);

                PlayerPrefs.SetInt(LockStatusKey(themeDefinition.LockItemUid),
                    isCurrentlyLocked ? 1 : 0);
            }

            if (themeUnlocked)
            {
                OnThemeUnlocked?.Invoke();
            }
        }

        private string LockStatusKey(string lockItemUid)
        {
            return $"ThemeLockStatus_{lockItemUid}_{_accountManager.Profile.Uid}";
        }

        private bool IsLocked(string lockItemUid)
        {
            return PlayerPrefs.GetInt(LockStatusKey(lockItemUid), 1) == 1;
        }

        private ThemeDefinition GetEventTheme(string requiredEventUid, bool ignoreScreenRequirements)
        {
            if(!string.IsNullOrEmpty(requiredEventUid))
                return GetEventThemeByUid(requiredEventUid);

            GameEventBase gameEvent = null;
            if (ignoreScreenRequirements)
            {
                gameEvent = _gameEventManager.GetAvailableSideMapEvent();
            }
            else if ((_screensBuilder.CurrentScreenType & ScreenType.SideMap) > 0)
            {
                gameEvent = _gameEventManager.GetCurrentSideMapEvent();
            }

            if (gameEvent != null)
            {
                return GetEventThemeByUid(gameEvent.Uid);
            }

            return null;
        }

        private ThemeDefinition GetEventThemeByUid(string eventUid)
        {
            var settings = (EndGameEventSettings)_gameEventResourceManager.GetSettings(eventUid);
            return settings != null ? settings.Theme : null;
        }

        public T GetThemedObject<T>(ThemePlacementId placementId, string additionalFilter, string eventUid = "", bool forceEventTheme = false) where T : Object
        {
            //first try get the theme from the event
            var eventTheme = GetEventTheme(eventUid, forceEventTheme);
            if (eventTheme != null)
                return eventTheme.GetThemedObject<T>(placementId, additionalFilter);

            foreach (var themeDefinition in _themeDefinitions)
            {
                if (IsLocked(themeDefinition.LockItemUid)) continue;

                var obj = themeDefinition.GetThemedObject<T>(placementId, additionalFilter);
                if (obj != null)
                {
                    return obj;
                }
            }

            return null;
        }

        public GameObject GetThemedTransition(string prefabName)
        {
            foreach (var themeDefinition in _themeDefinitions)
            {
                if (IsLocked(themeDefinition.LockItemUid)) continue;

                var go = themeDefinition.GetTransitionPrefab(prefabName);
                if (go != null)
                {
                    return go;
                }
            }

            return null;
        }

        public void Initialize(IContext context)
        {
            _bundleManager = context.Resolve<IBundleManager>();
            _assetsManager = context.Resolve<IAssetsManager>();
            _timeManager = context.Resolve<TimeManager>();
            _bundleInfoProvider = context.Resolve<IBundleInfoProvider>();
            _accountManager = context.Resolve<IAccountManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _eventDispatcher.AddListener<LevelFinishedEvent>(OnLevelFinishedHandler);
            _screensBuilder = context.Resolve<IScreensBuilder>();
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();

            var config = context.Resolve<IConfig>();
            SetupThemeConfig(config);
            Subscribe();
        }
        
        private void Subscribe()
        {
            Unsubscribe();
            Config.OnConfigUpdated += SetupThemeConfig;
            _bundlesBackgroundDownloaderManager.RegisterProvider(this);
        }

        private void Unsubscribe()
        {
            Config.OnConfigUpdated -= SetupThemeConfig;
            _bundlesBackgroundDownloaderManager.UnregisterProvider(this);
        }

        private void SetupThemeConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            SetupThemeConfigAsync(config, updatedConfigs).Forget();
        }

        private async UniTask SetupThemeConfigAsync(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;
            _totalRelativeDuration = 0;
            _themeDefinitions.Clear();

            _themeMetaConfig = config.TryGetDefaultFromDictionary<ThemeMetaConfig>();
            _themeConfig = config.Get<ThemeConfig>();

            if (_themeConfig == null)
            {
                return;
            }
            foreach (var c in _themeConfig.Values)
            {
                if (!Enum.TryParse(c.SchedulingType, out SchedulingType schedulingType)) continue;

                if (schedulingType == SchedulingType.Relative)
                {
                    _totalRelativeDuration += c.DurationInDays;
                }
            }
            foreach (var themeConfig in _themeConfig.Values)
            {
                var themeDefinition = themeConfig.Uid + ThemeDefinitionName;
                if (ShouldBeEnabled(themeDefinition, themeConfig))
                {
                    var loaded = await _assetsManager.LoadAsync<ThemeDefinition>(themeDefinition);
                    
                    var definition = loaded.Get();
                    definition.LockItemUid = themeConfig.Uid;
                    _themeDefinitions.Add(definition);
                    OnThemeUnlocked?.Invoke();
                }
            }
        }

        private void OnLevelFinishedHandler(LevelFinishedEvent obj)
        {
            if (obj.isWin)
            {
                SaveLockStatusLocally();
            }
        }

        public Vector4 GetHSVAdjust(string eventUid = "", bool forceEventTheme = false)
        {
            var eventTheme = GetEventTheme(eventUid, forceEventTheme);
            if (eventTheme != null)
                return eventTheme.getHSVAdjust();

            foreach (var themeDefinition in _themeDefinitions)
            {
                if (IsLocked(themeDefinition.LockItemUid)) continue;

                var hsvAdjust = themeDefinition.getHSVAdjust();
                if (hsvAdjust.magnitude > 0)
                {
                    return hsvAdjust;
                }
            }

            return Vector4.zero;
        }

        private bool ShouldBeEnabled(string themeDefinition, ThemeConfig themeConfig)
        {
            var currentTicks = GetCurrentUtcDateTime().Ticks;
            

            var lastStartTime = SchedulableConfigUtils.GetLastStartTime(SchedulableConfigUtils.GetSchedulableConfigFrom(themeConfig), this);
            var lastEndTime = lastStartTime.AddDays(themeConfig.DurationInDays);
            var downloadWindowStartTime = lastStartTime.AddDays(-themeConfig.DownloadWindowInDays);

            var downloadEnabled = downloadWindowStartTime.Ticks < currentTicks &&
                                  currentTicks < lastEndTime.Ticks;
            var bundleExists = downloadEnabled && _bundleManager.ContainsAsset(themeDefinition);

            var dateEnabled = lastStartTime.Ticks < currentTicks &&
                              currentTicks < lastEndTime.Ticks;

            return bundleExists && dateEnabled;
        }

        public DateTime GetCurrentUtcDateTime()
        {
            return _timeManager.GetCurrentDateTime();
        }

        public int TotalRelativeDuration => _totalRelativeDuration;

        public int GetTotalPrecedingDuration(int relativeSortIndex)
        {
            var result = 0;
            foreach (var c in _themeConfig.Values)
            {
                if (!Enum.TryParse(c.SchedulingType, out SchedulingType schedulingType)) continue;

                if (schedulingType == SchedulingType.Relative &&
                    c.RelativeSortIndex < relativeSortIndex)
                    result += c.DurationInDays;
            }

            return result;
        }

        public DateTime GetRelativeTimelineStart()
        {
            var relativeTimelineStart = _themeMetaConfig.RelativeTimelineStart.Value;
            return new DateTime(relativeTimelineStart.Year, relativeTimelineStart.Month,
                relativeTimelineStart.Day, _themeMetaConfig.UtcHourToStart, _themeMetaConfig.UtcMinuteToStart, 0);
        }

        public (int, int) GetUtcHourAndMinuteToStart()
        {
            return (_themeMetaConfig.UtcHourToStart, _themeMetaConfig.UtcMinuteToStart);
        }
        
        public List<BackgroundDownloadData> GetBundlesToPredownload()
        {
            List<BackgroundDownloadData> bundles = null;
            var currentTicks = GetCurrentUtcDateTime().Ticks;
            foreach (var themeConfig in _themeConfig.Values)
            {
                var themeDefinition = themeConfig.Uid + ThemeDefinitionName;
                
                var lastStartTime = SchedulableConfigUtils.GetLastStartTime(SchedulableConfigUtils.GetSchedulableConfigFrom(themeConfig), this);
                var lastEndTime = lastStartTime.AddDays(themeConfig.DurationInDays);
                var downloadWindowStartTime = lastStartTime.AddDays(-themeConfig.DownloadWindowInDays);

                if (downloadWindowStartTime.Ticks > currentTicks || currentTicks > lastEndTime.Ticks) continue;

                var bundleName = _bundleInfoProvider.GetBundleFromAsset(themeDefinition)?.Bundlename;
                if (bundleName.IsNullOrEmpty()) continue;
                
                bundles ??= new List<BackgroundDownloadData>();
                bundles.Add(new BackgroundDownloadData{Priority = BackgroundDownloadPriority.Low, Name = bundleName});
            }
            return bundles;
        }

        public void Restart()
        {
            Unsubscribe();
        }
    }
}