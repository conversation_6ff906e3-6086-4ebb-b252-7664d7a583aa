using BBB;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.Screens;
using Bebopbee.Core.Utility;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameAssets.Scripts.Wallet.Visualizing;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Collection.UI
{
    public class WildCardRewardScreenViewPresenter : ModalsViewPresenter, IWildCardRewardScreenViewPresenter, IDestroyable
    {

        [Header("Animation")]
        [SerializeField] private float _scrimOpacity;
        [SerializeField] private float _scrimFadeInDurationMilli;
        [SerializeField] private WildCardItemFx _introImpactFx;
        [SerializeField] private int _introImpactFxDurationMilli;
        [SerializeField] private int _wcReceiveFlyDurationMilli;
        [SerializeField] private AnimationCurve _wcReceiveScale;
        [SerializeField] private WildCardItemFx _trailFx;
        [SerializeField] private float _trailFxTimeNormalized;
        [SerializeField] private int _trailDelayToStopMilli;
        [SerializeField] private int _titleAppearDurationMilli;
        [SerializeField] private AnimationCurve _titleScale;
        [SerializeField] private int _subTitleAppearDurationMilli;
        [SerializeField] private AnimationCurve _subTitleScale;
        [SerializeField] private int _tapToContinueAppearDurationMilli;
        [SerializeField] private AnimationCurve _tapToContinueScale;
        [SerializeField] private int _wcBounceDurationMilli;
        [SerializeField] private AnimationCurve _wcBounceScaleX;
        [SerializeField] private AnimationCurve _wcBounceScaleY;
        [SerializeField] private WildCardItemFx _shineFx;
        [SerializeField] private float _shineFxTimeNormalized;
        [SerializeField] private int _fadeOutDurationMilli;
        [SerializeField] private int _wcFlyAnimationDurationMill;
        [SerializeField] private AnimationCurve _wcScaleOnIcon;
        [SerializeField] private WildCardItemFx _hitImpactFx;
        [SerializeField] private float _hitImpactFxTimeNormalized;
        [Space]
        [SerializeField] private int _wcRevealDurationMilli;
        [SerializeField] private AnimationCurve _wcRevealScale;
        [SerializeField] private WildCardItemFx _revealImpactFx;
        [SerializeField] private float _revealImpactFxTimeNormalized;
        [Space]
        [SerializeField] private int _wcReturnDurationMilli;
        [SerializeField] private AnimationCurve _wcReturnScale;
        [SerializeField] private WildCardItemFx _returnImpactFx;
        [SerializeField] private float _returnImpactFxTimeNormalized;
        [SerializeField] private int _wcFinalBounceDurationMilli;
        [SerializeField] private AnimationCurve _wcFinalBounceScaleX;
        [SerializeField] private AnimationCurve _wcFinalBounceScaleY;
        
        [Header("Fields")]
        [SerializeField] private LocalizedTextPro _headerText;
        [SerializeField] private RectTransform _tapToContinueLabel;
        [SerializeField] private RectTransform _title;
        [SerializeField] private RectTransform _subTitle;
        [SerializeField] private Image _scrimView;
        [SerializeField] private Transform _wcStartPosition;
        [SerializeField] private Transform _wcTargetPosition;
        [SerializeField] private Transform _wcIconPosition;
        [SerializeField] private SpineDrivenPathHolder _cardFlyPathCurveHolder;
        [SerializeField] private SpineDrivenPathHolder _cardFlyToIconPathCurveHolder;
        [SerializeField] private SpineDrivenPathHolder _cardReturnPathCurveHolder;

        private Transform _cachedWildCardParent;
        private WildCardItem _wildCard;
        private IEventDispatcher _eventDispatcher;

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _eventDispatcher = context.Resolve<IEventDispatcher>();
        }

        public override void Show()
        {
            base.Show();
            ResetView();
        }

        private void ResetView()
        {
            CloseButton.interactable = false;
            _tapToContinueLabel.localScale = Vector3.zero;
            _title.localScale = Vector3.zero;
            _subTitle.localScale = Vector3.zero;
            var scrimViewColor = _scrimView.color;
            scrimViewColor.a = 0;
            _scrimView.color = scrimViewColor;
        }

        public async void StartWcReceiveAnimation(WildCardItem wildCard)
        {
            _wildCard = wildCard;
            var wildCardTransform = wildCard.transform;
            _cachedWildCardParent = wildCardTransform.parent;
            wildCardTransform.SetParent(transform);
            _wcStartPosition.position = _wildCard.transform.position;
            
            _wildCard.StartFX(_introImpactFx);
            await UniTask.Delay(_introImpactFxDurationMilli);
            
            var flyAnimationDuration = _wcReceiveFlyDurationMilli * MathUtility.MillisToSeconds;
            var wcBounceDuration = _wcBounceDurationMilli * MathUtility.MillisToSeconds;
            _cardFlyPathCurveHolder.SetPositions(wildCardTransform.position, _wcTargetPosition.position);
            var followingTransform = _cardFlyPathCurveHolder.GetFollowingTransformFromStartPosition();
            followingTransform.Start(flyAnimationDuration);
            await UniTask.NextFrame();
            
            var transformTween = DOTween.To(() => 0f,
                _ =>
                {
                    wildCardTransform.position = followingTransform.Transform.position;
                }, 1f, flyAnimationDuration);
            DOTween.Sequence()
                .Append(_scrimView.DOFade(_scrimOpacity, _scrimFadeInDurationMilli * MathUtility.MillisToSeconds))
                .Join(transformTween)
                .Join(DOTween.To(() => 0f,
                        progress =>
                        {
                            wildCardTransform.localScale = Vector3.one * _wcReceiveScale.Evaluate(progress);
                        }, 1f, flyAnimationDuration))
                .InsertCallback(_trailFxTimeNormalized, () => _wildCard.StartFX(_trailFx))
                .AppendCallback(StartTitleAnimation)
                .Append(DOTween.To(() => 0f,
                    progress =>
                    {
                        var wildCardLocalScale = wildCardTransform.localScale;
                        wildCardLocalScale.x = _wcBounceScaleX.Evaluate(progress);
                        wildCardLocalScale.y = _wcBounceScaleY.Evaluate(progress);
                        wildCardTransform.localScale = wildCardLocalScale;
                    }, 1f, wcBounceDuration));
            _cardFlyPathCurveHolder.Completed -= CompleteHandler;
            _cardFlyPathCurveHolder.Completed += CompleteHandler;
            return;

            async void CompleteHandler()
            {
                _cardFlyPathCurveHolder.Completed -= CompleteHandler;
                followingTransform.Finish();
                transformTween.Kill();
                wildCardTransform.position = _wcTargetPosition.position;

                await UniTask.Delay((int)(_shineFxTimeNormalized * MathUtility.SecondsToMillis));
                wildCard.StartFX(_shineFx);
            }
        }

        private void StartTitleAnimation()
        {
            DOTween.Sequence()
                .Append(DOTween.To(() => 0f,
                    progress =>
                    {
                        _title.localScale = Vector3.one * _titleScale.Evaluate(progress);
                    }, 1f, _titleAppearDurationMilli * MathUtility.MillisToSeconds))
                .Append(DOTween.To(() => 0f,
                    progress =>
                    {
                        _subTitle.localScale = Vector3.one * _subTitleScale.Evaluate(progress);
                    }, 1f, _subTitleAppearDurationMilli * MathUtility.MillisToSeconds))
                .AppendCallback(ActivateCloseButton);
        }

        private void ActivateCloseButton()
        {
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<WildCardRewardShownEvent>());
            DOTween.To(() => 0f,
                progress =>
                {
                    _tapToContinueLabel.localScale = Vector3.one * _tapToContinueScale.Evaluate(progress);
                }, 1f, _tapToContinueAppearDurationMilli * MathUtility.MillisToSeconds)
                .OnComplete(() => CloseButton.interactable = true);
        }

        protected override async void OnCloseButtonClick()
        {
            if (!CloseButton.interactable) return;
            CloseButton.interactable = false;
            
            FadeOutScreen(_fadeOutDurationMilli);
            StartCardFlyAnimation(_wcFlyAnimationDurationMill);
            await UniTask.Delay(_fadeOutDurationMilli + _wcFlyAnimationDurationMill);
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<WildCardHitIconEvent>());
            StartNewWildCardReveal(_wcRevealDurationMilli);
            await UniTask.Delay(_wcRevealDurationMilli);
            StartReturnToPosition(_wcReturnDurationMilli);
            await UniTask.Delay(_wcReturnDurationMilli);
            StartBounceAnimation(_wcFinalBounceDurationMilli);
            await UniTask.Delay(_wcFinalBounceDurationMilli);
            base.OnCloseButtonClick();
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<WildCardRewardAnimationFinishedEvent>());
        }

        private void FadeOutScreen(float durationMilli)
        {
            var duration = durationMilli * MathUtility.MillisToSeconds;
            _scrimView.DOFade(0, duration);
            _tapToContinueLabel.DOScale(0, duration);
            _title.DOScale(0, duration);
            _subTitle.DOScale(0, duration);
        }

        private async void StartCardFlyAnimation(float durationMilli)
        {
            _wildCard.StopFX(_shineFx);
            var duration = durationMilli * MathUtility.MillisToSeconds;
            var wildCardTransform = _wildCard.transform;
            _cardFlyToIconPathCurveHolder.SetPositions(wildCardTransform.position, _wcIconPosition.position);
            var followingTransform = _cardFlyToIconPathCurveHolder.GetFollowingTransformFromStartPosition();
            followingTransform.Start(duration);
            await UniTask.NextFrame();
            var transformTween = DOTween
                .To(() => 0f,
                    _ =>
                    {
                        wildCardTransform.position = followingTransform.Transform.position;
                    }, 1f, duration);
            _cardFlyToIconPathCurveHolder.Completed -= CompleteHandler;
            _cardFlyToIconPathCurveHolder.Completed += CompleteHandler;
            
            DOTween
                .To(() => 0f,progress =>
                    {
                        wildCardTransform.localScale = Vector3.one * _wcScaleOnIcon.Evaluate(progress);
                    }, 1f, duration);
            return;

            async void CompleteHandler()
            {
                _cardFlyToIconPathCurveHolder.Completed -= CompleteHandler;
                followingTransform.Finish();
                transformTween.Kill();
                wildCardTransform.position = _wcIconPosition.position;

                await UniTask.Delay((int)(_hitImpactFxTimeNormalized * MathUtility.SecondsToMillis));
                _wildCard.StartFX(_hitImpactFx);

                await UniTask.Delay(_trailDelayToStopMilli);
                _wildCard.StopFX(WildCardItemFx.TrailFX);
            }
        }

        private async void StartNewWildCardReveal(float durationMilli)
        {           
            var wildCardTransform = _wildCard.transform;
            var duration = durationMilli * MathUtility.MillisToSeconds;
            wildCardTransform.position = _wcTargetPosition.position;
            wildCardTransform.localScale = Vector3.one * _wcRevealScale.Evaluate(0);
            DOTween
                .To(() => 0f,
                    progress =>
                    {
                        wildCardTransform.localScale = _wcRevealScale.Evaluate(progress) * Vector3.one;
                    }, 1f, duration);
            await UniTask.Delay((int)(_revealImpactFxTimeNormalized * MathUtility.SecondsToMillis));
            _wildCard.StartFX(_revealImpactFx);
        }

        private async void StartReturnToPosition(float durationMilli)
        {
            _wildCard.StartFX(WildCardItemFx.TrailFX);
            var wildCardTransform = _wildCard.transform;
            var duration = durationMilli * MathUtility.MillisToSeconds;
            _cardReturnPathCurveHolder.SetPositions(wildCardTransform.position, _wcStartPosition.position);
            var followingTransform = _cardReturnPathCurveHolder.GetFollowingTransformFromStartPosition();
            followingTransform.Start(duration);
            await UniTask.NextFrame();
            var transformTween = DOTween
                .To(() => 0f,
                    _ => { wildCardTransform.position = followingTransform.Transform.position; }, 1f, duration);
            DOTween
                .To(() => 0f,
                    progress => { wildCardTransform.localScale = _wcReturnScale.Evaluate(progress) * Vector3.one; }, 1f,
                    duration);
            _cardReturnPathCurveHolder.Completed -= CompleteHandler;
            _cardReturnPathCurveHolder.Completed += CompleteHandler;
            return;

            async void CompleteHandler()
            {
                _cardReturnPathCurveHolder.Completed -= CompleteHandler;
                followingTransform.Finish();
                transformTween.Kill();
                wildCardTransform.position = _wcStartPosition.position;
                
                await UniTask.Delay((int)(_returnImpactFxTimeNormalized * MathUtility.SecondsToMillis));
                _wildCard.StartFX(_returnImpactFx);
                
                await UniTask.Delay(_trailDelayToStopMilli);
                _wildCard.StopFX(WildCardItemFx.TrailFX);
            }
        }

        private void StartBounceAnimation(float durationMilli)
        {
            var wildCardTransform = _wildCard.transform;
            var duration = durationMilli * MathUtility.MillisToSeconds;
            DOTween
                .To(() => 0f,
                    progress =>
                    {
                        var wildCardLocalScale = wildCardTransform.localScale;
                        wildCardLocalScale.x = _wcFinalBounceScaleX.Evaluate(progress);
                        wildCardLocalScale.y = _wcFinalBounceScaleY.Evaluate(progress);
                        wildCardTransform.localScale = wildCardLocalScale;
                    }, 1f, duration)
                .OnComplete(() => wildCardTransform.SetParent(_cachedWildCardParent));
        }
    }
}
