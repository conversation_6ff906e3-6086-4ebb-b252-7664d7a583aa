using BBB;
using BBB.Map;
using BBB.UI.Level;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Generic
{
    public class LevelTitle : BbbMonoBehaviour
    {
        [SerializeField] private Image _star;
        [SerializeField] private TextMeshProUGUI _caption;
        [SerializeField] private Sprite[] _starIcons = new Sprite[3];
        [SerializeField] private Color[] _stageColors = new Color[3];

        private ILocalizationManager _localization;
        private ILevel _level;

        public void Setup(ILocalizationManager locManager, ILevel level)
        {
            _localization = locManager;
            _level = level;
            UpdateText();
            UpdateStar();
        }

        private void UpdateText()
        {
            var levelName = _localization.getLocalizedTextWithArgs(LevelHelper.LevelString, _level.LevelName);
            _caption.text = levelName;
            var levelStage = Mathf.Clamp((int)_level.GetPaletteStage(), 0, 2);
            _caption.color = _stageColors[levelStage];
        }

        private void UpdateStar()
        {
            var levelStage = Mathf.Clamp((int)_level.GetPaletteStage(), 0, 2);
            _star.sprite = _starIcons[levelStage];
        }
    }
}