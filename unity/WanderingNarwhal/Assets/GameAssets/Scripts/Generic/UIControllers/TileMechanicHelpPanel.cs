using System.Collections.Generic;
using BBB.CellTypes;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BBB.Map;
using BBB.Match3.Renderer;
using BBB.UI.Level.Input;
using DG.Tweening;
using GameAssets.Scripts.Utils;
using JetBrains.Annotations;
using Spine.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.Profiling;
using UnityEngine.UI;

namespace BBB.UI.Level
{
    [System.Flags]
    public enum HelpTriggerType
    {
        DoubleTap = 0b00001,
        LongHold = 0b00010,
        FailedMatchSwipe = 0b00100,
    }

    /// <summary>
    /// Help panel is designed to show tile mechanic description information for certain tiles when that tile is tapped on by player.
    /// </summary>
    public class TileMechanicHelpPanel : BbbMonoBehaviour, IContextInitializable
    {
        [SerializeField]
        private TileMechanicHelpListSettings _settings;

        [SerializeField]
        private TextMeshProUGUI _nameText;

        [SerializeField]
        private TextMeshProUGUI _descriptionText;

        [SerializeField]
        private Transform _tileImageRoot;

        [SerializeField]
        private Canvas _canvas;

        [SerializeField]
        private float _displayDuration = 5;

        [SerializeField]
        private Animator _animator;

        [SerializeField]
        private string _introAnimName = "Intro";

        [SerializeField]
        private string _hideAnimName = "Outro";

        [SerializeField]
        private CanvasGroup _canvasGroup;

        [SerializeField]
        private Image _bgImage;

        [SerializeField]
        private Image _tileBgImage;

        [SerializeField]
        private List<StageColorPair> _bgStageColorPairs;

        [SerializeField]
        private float _tileAlphaDuration = 1.2f;

        private Transform _defaultTileParent;
        private TileView _currentTileView;
        private CellOverlayView _currentCellView;

        private float _displayTimer;
        private float _lastFeedbackTriggerTime;
        private Coords _lastTapCoords;
        private const float _minDelayForNextFeedback = 1.5f;
        private bool _introHasFinished;
        private bool _tappedWhileShowing;

        private IInputController _inputController;
        private TileController _tileController;
        private ICellController _cellController;
        private GameController _gameController;
        private IEventDispatcher _eventDispatcher;
        private FxRenderer _fxRenderer;
        private IGridController _gridController;
        private readonly List<Tweener> _tweeners = new();
        private ILevel _level;
        private Coords? _lastHighlightedCoords;

        public void InitializeByContext(IContext context)
        {
            if (_gameController == null)
            {
                _gameController = context.Resolve<GameController>();
            }

            _tileController = context.Resolve<TileController>();
            _cellController = context.Resolve<ICellController>();
            _inputController = context.Resolve<IInputController>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _fxRenderer = context.Resolve<FxRenderer>();
            _gridController = context.Resolve<IGridController>();
            Subscribe();
            _level = context.Resolve<LevelHolder>().level;

            if (_tweeners != null)
            {
                foreach (var tween in _tweeners)
                {
                    tween?.Kill();
                }

                _tweeners.Clear();
            }

            if (_animator == null)
            {
                _animator = GetComponent<Animator>();
            }

            // Panel object should be active to be able to track events.
            gameObject.SetActive(true);
            HidePanelImmediate();
            _canvasGroup.alpha = 1f;
            _animator.Rebind();
            _animator.Update(0f);
        }

        public void ResetDefaults()
        {
            _displayTimer = default;
            _lastFeedbackTriggerTime = default;
            _lastTapCoords = default;
            _introHasFinished = default;
            _tappedWhileShowing = default;
        }

        private void Unsubscribe()
        {
            if (_eventDispatcher != null)
            {
                _eventDispatcher.RemoveListener<LevelEndedEvent>(OnLevelEnded);
                _eventDispatcher.RemoveListener<BoostDescriptionPanel.BoostDescriptionPanelEvent>(OnBoosterPanelEvent);
            }

            if (_inputController != null)
            {
                _inputController.OnTileLongHoldEvent -= OnTileLongHold;
                _inputController.OnStartTouchEvent -= OnStartTouchEventHandler;
                _inputController.OnTileTappedEvent -= OnTileTappedEventHandler;
                _inputController.OnEndTouchEvent -= OnEndTouchEventHandler;
            }
        }

        private void Subscribe()
        {
            Unsubscribe();
            _eventDispatcher.AddListener<LevelEndedEvent>(OnLevelEnded);
            _eventDispatcher.AddListener<BoostDescriptionPanel.BoostDescriptionPanelEvent>(OnBoosterPanelEvent);
            _inputController.OnTileLongHoldEvent += OnTileLongHold;
            _inputController.OnStartTouchEvent += OnStartTouchEventHandler;
            _inputController.OnTileTappedEvent += OnTileTappedEventHandler;
            _inputController.OnEndTouchEvent += OnEndTouchEventHandler;
        }

        private void OnBoosterPanelEvent(BoostDescriptionPanel.BoostDescriptionPanelEvent obj)
        {
            if (obj.Arg0)
            {
                _displayTimer = 5f;
            }
        }

        private void OnLevelEnded(LevelEndedEvent obj)
        {
            _displayTimer = 5f;
        }

        private bool TryShowForCellOverlay(Coords coords, HelpTriggerType triggerType)
        {
            if (CheckIfQuitPanelIsShown()) return false;
            if (!_gameController.Grid.TryGetCell(coords, out var cell))
            {
                return false;
            }

            var currCoords = coords;
            if (cell.HasMultiSizeCellReferenceWithCellOverlay())
            {
                cell = cell.GetMainCellReference(out _, isCellOverlay: true);
                currCoords = cell.Coords;
            }

            var cellView = _cellController.GetCellView(currCoords, true);
            if (cellView == null) return false;
            if (_canvas.enabled) return false;
            var info = FindInfoForCell(cellView.State, triggerType);
            if (info == null) return false;

            if (_currentCellView == null)
            {
                _nameText.text = LocalizationManager.GetLocalizedText(info.NameLocKey, noError: true);
                _descriptionText.text = LocalizationManager.GetLocalizedText(info.DescriptionLocKey, noError: true);
                ReleaseCurrentItem();
                var originalView = cellView.GetView(info.CellState)?.CellOverlayView;
                Profiler.BeginSample($"Instantiate[{originalView.name}]");
                _currentCellView = originalView == null ? null : Instantiate(originalView, _tileImageRoot);
                Profiler.EndSample();
            }

            if (_currentCellView == null)
            {
                return false;
            }

            FixViewSortingOrder(_currentCellView.gameObject);

            var cellTransform = _currentCellView.transform;
            cellTransform.GetComponent<RectTransform>().anchoredPosition = Vector3.zero;
            cellTransform.localScale = new Vector3(info.PreviewScaleMlt, info.PreviewScaleMlt, info.PreviewScaleMlt);
            _currentCellView.Animate(CellAnimation.Preview);
            ShowPanel();
            return true;
        }

        private bool TryShowForTile(Tile tile, HelpTriggerType triggerType)
        {
            if (CheckIfQuitPanelIsShown()) return false;
            if (tile == null) return false;
            if (_canvas.enabled) return false;
            var tileView = _tileController.GetTileViewById(tile.Id, allowLogError: false);
            var info = FindInfoForTile(tileView, triggerType);
            if (info == null) return false;

            if (_currentTileView == null)
            {
                _nameText.text = LocalizationManager.GetLocalizedText(info.NameLocKey, noError: true);
                _descriptionText.text = LocalizationManager.GetLocalizedText(info.DescriptionLocKey, noError: true);
                ReleaseCurrentItem();
                _currentTileView = _tileController.CreateTileViewPreviewForTile(tile);
            }

            _currentTileView.TriggerPreviewAnimation();
            FixViewSortingOrder(_currentTileView.gameObject);

            var tileTransform = _currentTileView.transform;
            if (_defaultTileParent == null)
            {
                _defaultTileParent = tileTransform.parent;
            }

            tileTransform.SetParent(_tileImageRoot);
            tileTransform.GetComponent<RectTransform>().anchoredPosition = Vector3.zero;
            tileTransform.localScale = Vector3.one * info.PreviewScaleMlt;
            ShowPanel();
            return true;
        }

        private static bool CheckIfQuitPanelIsShown()
        {
            var proxy = FindObjectOfType<LevelControllerReferenceProxy>();
            return proxy.LevelExitMenuController.isExitPanelVisible;
        }

        private void FixViewSortingOrder(GameObject go)
        {
            var renderers = go.GetComponentsInChildren<SpriteRenderer>(true);
            foreach (var spriteRenderer in renderers)
            {
                if (spriteRenderer.sortingOrder < _canvas.sortingOrder)
                    spriteRenderer.sortingOrder += _canvas.sortingOrder;

                spriteRenderer.SetAlpha(0f);
                _tweeners.Add(spriteRenderer.DoAlpha(1f, _tileAlphaDuration));
            }

            var canvases = go.GetComponentsInChildren<Canvas>(true);
            foreach (var canvas in canvases)
            {
                if (canvas.sortingOrder < _canvas.sortingOrder)
                    canvas.sortingOrder += _canvas.sortingOrder;
            }

            var particleSystems = go.GetComponentsInChildren<ParticleSystem>(true);
            foreach (var particleSystem in particleSystems)
            {
                var renderer = particleSystem.GetComponent<Renderer>();
                if (renderer != null && renderer.sortingOrder < _canvas.sortingOrder)
                    renderer.sortingOrder += _canvas.sortingOrder;
            }

            var skeletonMecanims = go.GetComponentsInChildren<SkeletonMecanim>(true);
            foreach (var skeletonMecanim in skeletonMecanims)
            {
                var renderer = skeletonMecanim.GetComponent<MeshRenderer>();
                if (renderer != null && renderer.sortingOrder < _canvas.sortingOrder)
                    renderer.sortingOrder += _canvas.sortingOrder;
            }

            var skeletonGraphics = go.GetComponentsInChildren<SkeletonGraphic>();
            foreach (var skeletonGraphic in skeletonGraphics)
            {
                skeletonGraphic.SetAlpha(0f);
                _tweeners.Add(skeletonGraphic.DoAlpha(1f, _tileAlphaDuration));
            }
        }

        private void ResetViewSortingOrder(GameObject go)
        {
            var renderers = go.GetComponentsInChildren<SpriteRenderer>(true);
            foreach (var spriteRenderer in renderers)
            {
                if (spriteRenderer.sortingOrder > _canvas.sortingOrder)
                    spriteRenderer.sortingOrder -= _canvas.sortingOrder;
                spriteRenderer.SetAlpha(1f);
            }

            var canvases = go.GetComponentsInChildren<Canvas>(true);
            foreach (var canvas in canvases)
            {
                if (canvas.sortingOrder > _canvas.sortingOrder)
                    canvas.sortingOrder -= _canvas.sortingOrder;
            }

            var particleSystems = go.GetComponentsInChildren<ParticleSystem>(true);
            foreach (var particleSystem in particleSystems)
            {
                var renderer = particleSystem.GetComponent<Renderer>();
                if (renderer != null && renderer.sortingOrder > _canvas.sortingOrder)
                    renderer.sortingOrder -= _canvas.sortingOrder;
            }

            var skeletonMecanims = go.GetComponentsInChildren<SkeletonMecanim>(true);
            foreach (var skeletonMecanim in skeletonMecanims)
            {
                var renderer = skeletonMecanim.GetComponent<MeshRenderer>();
                if (renderer != null && renderer.sortingOrder > _canvas.sortingOrder)
                    renderer.sortingOrder -= _canvas.sortingOrder;
            }

            var skeletonGraphics = go.GetComponentsInChildren<SkeletonGraphic>();
            foreach (var skeletonGraphic in skeletonGraphics)
            {
                skeletonGraphic.SetAlpha(1f);
            }

            if (_tweeners == null) return;
            foreach (var tween in _tweeners)
            {
                tween?.Kill();
            }

            _tweeners.Clear();
        }

        private void ShowPanel()
        {
            if (CheckIfQuitPanelIsShown())
                return;

            var currentStagePalette = _bgStageColorPairs.Find(a => a.Stage == _level.GetPaletteStage());
            _bgImage.color = currentStagePalette.MainBgColor;
            _tileBgImage.color = currentStagePalette.SecondBgColor;
            _canvas.enabled = true;
            enabled = true;
            _animator.enabled = true;
            _displayTimer = 0;
            _animator.Play(_introAnimName);
        }

        private TileMechanicHelpInfo FindInfoForTile(TileView tileView, HelpTriggerType triggerType)
        {
            if (tileView == null) return null;
            foreach (var info in _settings.TileInfo)
            {
                if (info.IsFirstPriority
                    && (info.TriggerType & triggerType) != 0
                    && (info.LayerState & tileView.LayerState) != 0)
                {
                    return info;
                }
            }

            foreach (var info in _settings.TileInfo)
            {
                if (!info.IsFirstPriority
                    && (info.TriggerType & triggerType) != 0
                    && (info.LayerState & tileView.LayerState) != 0)
                {
                    return info;
                }
            }

            return null;
        }

        private CellMechanicHelpInfo FindInfoForCell(CellLayerState cellState, HelpTriggerType triggerType)
        {
            if (cellState == CellLayerState.None) return null;
            foreach (var info in _settings.CellInfo)
            {
                if (info.IsFirstPriority
                    && (info.CellState & cellState) != 0)
                {
                    return info;
                }
            }

            foreach (var info in _settings.CellInfo)
            {
                if (!info.IsFirstPriority
                    && (info.CellState & cellState) != 0)
                {
                    return info;
                }
            }

            return null;
        }

        private void OnTileLongHold(Coords coords)
        {
            OnTileTapped(coords, HelpTriggerType.LongHold);
        }

        // TODO: Do we need to keep this behavior?
        private void OnSwapEnded(Coords coordA, Coords coordB, bool isSwapSuccess)
        {
            if (isSwapSuccess) return;

            if (!TryShowOnCoords(coordA, HelpTriggerType.FailedMatchSwipe))
            {
                TryShowOnCoords(coordB, HelpTriggerType.FailedMatchSwipe);
            }
        }

        private bool TryShowOnCoords(Coords coords, HelpTriggerType triggerType)
        {
            if (_gameController.Grid.TryGetCell(coords, out var cell))
            {
                if (TryShowForCellOverlay(cell.Coords, triggerType))
                {
                    return true;
                }

                if (!cell.HasMultiSizeCellReferenceWithCellOverlay())
                {
                    cell = cell.GetMainCellReference(out _);
                    if (TryShowForTile(cell.Tile, triggerType))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private void OnStartTouchEventHandler(Coords coords, int tapCount)
        {
            var tapTime = Time.time;
            var elapsed = tapTime - _lastFeedbackTriggerTime;

            if (enabled && _introHasFinished)
            {
                _tappedWhileShowing = true;
                HidePanel();
                return;
            }

            if (_gameController.Grid.TryGetCell(coords, out var cell))
            {
                var overlayCell = cell.GetMainCellReference(out _, true);
                var isOverlay = overlayCell.IsAnyOf(CellState.Tnt | CellState.Ivy);

                cell = isOverlay ? overlayCell : cell.GetMainCellReference(out _);

                if (cell.Coords != _lastTapCoords || elapsed > _minDelayForNextFeedback)
                {
                    var tileView = _tileController.GetTileViewByCoord(cell.Coords, false);
                    if (tileView != null && !cell.Tile.IsBoost && !isOverlay && _gameController.IsTileAllowedToTap(cell.Coords))
                    {
                        if (_lastHighlightedCoords.HasValue && _lastHighlightedCoords != coords)
                        {
                            _fxRenderer.SetTileHighlight(_lastHighlightedCoords.Value, false);
                        }
                        
                        _fxRenderer.SetTileHighlight(coords, true);
                        _lastHighlightedCoords = coords;
                        
                        if (cell.Tile.Speciality != TileSpeciality.None && !_gridController.IsSwappableCell(cell.Coords))
                        {
                            tileView.TriggerTapFeedbackAnimation();
                            // Even though this is not technically a double tap, we are using the same trigger type so
                            // that we don't have to change all the Infos in TilemechanicHelpSettings.asset
                            TryShowOnCoords(coords, HelpTriggerType.DoubleTap);
                        }
                    }

                    var cellView = _cellController.GetCellView(cell.Coords, true);
                    cellView?.OnTileTap();

                    _lastTapCoords = cell.Coords;
                    _lastFeedbackTriggerTime = tapTime;
                }
            }

            if (tapCount == 2)
            {
                OnTileTappedEventHandler(coords);
            }
        }

        private void OnTileTappedEventHandler(Coords coords)
        {
            OnTileTapped(coords, HelpTriggerType.DoubleTap);
        }

        private void OnTileTapped(Coords coords, HelpTriggerType helpTriggerType)
        {
            if (_tappedWhileShowing) return;

            if (_gameController.Grid.TryGetCell(coords, out var someCell))
            {
                if (someCell.HasMultiSizeCellReferenceWithCellOverlay())
                {
                    TryShowOnCoords(coords, helpTriggerType);
                    return;
                }
                
                someCell = someCell.GetMainCellReference(out _);

                // If tile is a booster and this wasn't triggered by long hold, don't show the panel
                if (helpTriggerType is not HelpTriggerType.LongHold && someCell.HasTile() && someCell.Tile.IsBoost) return;

                // If tile is not swappable, the panel is already triggered in OnStartTouchEventHandler, so no need to duplicate it here.
                if (someCell.HasTile() && someCell.Tile.Speciality != TileSpeciality.None && !_gridController.IsSwappableCell(coords)) return;

                TryShowOnCoords(coords, helpTriggerType);
            }
        }

        private void OnEndTouchEventHandler(Coords coords)
        {
            if (_tappedWhileShowing)
            {
                _tappedWhileShowing = false;
                return;
            }

            if (_gameController.Grid.TryGetCell(coords, out var cell))
            {
                var overlayCell = cell.GetMainCellReference(out _, true);
                var isOverlay = overlayCell.IsAnyOf(CellState.Tnt | CellState.Ivy);

                cell = isOverlay ? overlayCell : cell.GetMainCellReference(out _);
                var tileView = _tileController.GetTileViewByCoord(cell.Coords, false);
                if (tileView == null || cell.Tile.IsBoost || isOverlay)
                    return;

                _fxRenderer.SetTileHighlight(coords, false);
                if (cell.Tile.Speciality != TileSpeciality.None && _gridController.IsSwappableCell(cell.Coords))
                {
                    tileView.TriggerTapFeedbackAnimation();
                }
            }
        }

        [UsedImplicitly]
        [AnimationTriggered("Match3HelpPanelIntro", "TileMechanicHelpPanel", "Base Layer", 22, "Called when the intro animation is finished.")]
        private void OnIntroFinished()
        {
            _introHasFinished = true;
        }

        [UsedImplicitly]
        [AnimationTriggered("Match3HelpPanelOutro", "TileMechanicHelpPanel", "Base Layer", 4, "Called when the outro animation is considered finished.")]
        private void OnOutroFinished()
        {
            HidePanelImmediate();
        }

        public void HidePanelImmediate()
        {
            _introHasFinished = false;
            enabled = false;
            _canvas.enabled = false;
            _animator.enabled = false;
            ReleaseCurrentItem();
        }

        private void Update()
        {
            _displayTimer += Time.deltaTime;
            if (_displayTimer >= _displayDuration)
            {
                HidePanel();
            }
        }

        private void HidePanel()
        {
            _displayTimer = 0;
            _animator.Play(_hideAnimName);
            enabled = false;
        }

        private void ReleaseCurrentItem()
        {
            if (_currentTileView != null)
            {
                var tileTransform = _currentTileView.transform;
                tileTransform.localScale = new Vector3(1f, 1f, 1f);
                tileTransform.localPosition = new Vector3();
                tileTransform.SetParent(_defaultTileParent);
                ResetViewSortingOrder(_currentTileView.gameObject);
                _currentTileView.ReleaseItem();
                _currentTileView = null;
            }

            if (_currentCellView != null)
            {
                Destroy(_currentCellView.gameObject);
                _currentCellView = null;
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            Unsubscribe();
        }
    }
}