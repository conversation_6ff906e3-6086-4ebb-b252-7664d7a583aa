using System.Collections.Generic;
using BBB.DI;
using BBB.Map;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace BBB.UI.Level
{
    public class BoostDescriptionIcon : BbbMonoBehaviour
    {
        private readonly Dictionary<string, (GameObject gameObject, BoostDescription boostDescription)> _iconGameObjects = new();
        private readonly HashSet<string> _loadedIcons = new();
        private IMatch3SharedResourceProvider _match3ResourceProvider;

        public void Init(IContext context)
        {
            _match3ResourceProvider = context.Resolve<IMatch3SharedResourceProvider>();
        }

        public async UniTask Show(string boostUid, string boostName, string boostDescription)
        {
            foreach (var kvp in _iconGameObjects)
            {
                if (kvp.Value.gameObject.activeSelf)
                {
                    kvp.Value.gameObject.SetActive(false);
                }
            }
            
            if (_iconGameObjects.TryGetValue(boostUid, out var itemGo))
            {
                itemGo.gameObject.SetActive(true);
                itemGo.boostDescription.SetInfo(boostName, boostDescription);
            }
            else
            {
                //TODO REMOVE CONCAT
                //["shovel","shuffle","balloon","wind"]
                var boosterName = "prefabs/descicon_" + boostUid;

                var go = await _match3ResourceProvider.CacheAndLoadAsync<GameObject>(this, boosterName);
                OnObjectLoaded(go, boostUid, boosterName);
                _iconGameObjects[boostUid].gameObject.SetActive(true);
                _iconGameObjects[boostUid].boostDescription.SetInfo(boostName, boostDescription);
            }
        }

        private void OnObjectLoaded(GameObject goLoaded, string boosterUid, string boosterName)
        {
            UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{goLoaded.name}]");
            var go = Instantiate(goLoaded, transform);
            UnityEngine.Profiling.Profiler.EndSample();
            _iconGameObjects.Add(boosterUid, (go, go.GetComponent<BoostDescription>()));
            go.SetActive(false);
            _loadedIcons.Add(boosterName);
        }

        public void Hide()
        {
            foreach (var kvp in _iconGameObjects)
            {
                if (kvp.Value.gameObject.activeSelf)
                {
                    kvp.Value.gameObject.SetActive(false);
                }
            }
        }

        public void Release()
        {
            Release_Internal();
        }

        private void Release_Internal()
        {
            foreach (var loadedIcon in _loadedIcons)
            {
                _match3ResourceProvider.ReleaseCached(loadedIcon);
            }

            _iconGameObjects.Clear();
            _loadedIcons.Clear();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            Release_Internal();
        }

        public async UniTask Preload(ILevel level)
        {
            var eligibleBoosts = level?.Config.TrueEligibleBoosts();
            if (eligibleBoosts != null)
            {
                foreach (var boostUid in eligibleBoosts)
                {
                    //["shovel","shuffle","balloon","wind"]
                    var boosterName = "prefabs/descicon_" + boostUid;
                    if (!_iconGameObjects.ContainsKey(boostUid))
                    {
                        var go = await _match3ResourceProvider.CacheAndLoadAsync<GameObject>(this, boosterName);
                        OnObjectLoaded(go, boostUid, boosterName);
                    }
                }
            }
        }
    }
}