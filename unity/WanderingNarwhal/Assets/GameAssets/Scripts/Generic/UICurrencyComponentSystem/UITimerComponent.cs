using System;
using System.Collections;
using BBB.DI;
using BBB.UI.Core;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.Utils;
using TMPro;
using UnityEngine;

namespace BBB.UI
{
    public class UITimerComponent : ContextedUiBehaviour
    {
        public event Action TimerExpired = delegate { };

        [SerializeField] private TextMeshProUGUI _timeText;

        private ILocalizationManager _localizationManager;
        private TimeManager _timeManager;
        private Coroutine _timerRoutine;

        private double _startTime;
        private double _endTime;
        private bool _wasSetup;
        private double _timeOfRefreshEnabling;

        private void SetupTimeManager(TimeManager timeManager)
        {
            _timeManager = timeManager;
        }

        private void SetupTime(long remainingTime)
        {
            _wasSetup = true;
            _startTime = _timeManager.CurrentTimeStamp();
            _endTime = _startTime + remainingTime;
            UpdateTimeText();

            if (this.gameObject.activeInHierarchy)
            {
                LaunchTimerRotine();
            }
        }

        public void SetupTime(TimeManager timeManager, long remainingTime)
        {
            SetupTimeManager(timeManager);
            SetupTime(remainingTime);
        }

        public void DisableTimerRefreshForDuration(float duration)
        {
            if (_timeManager != null)
            {
                _timeOfRefreshEnabling = _timeManager.CurrentTimeStamp() + (double) duration;
            }
        }

        public void SetRawTime(long time)
        {
            _timeText.text = time.ToLocalizedTimeFormat(_localizationManager);
        }

        private void LaunchTimerRotine()
        {
            if (_timerRoutine != null)
            {
                StopCoroutine(_timerRoutine);
            }

            _timerRoutine = StartCoroutine(TimerCoroutine());
        }

        private IEnumerator LateTimerStart(float delay)
        {
            yield return WaitCache.Seconds(delay);
            LaunchTimerRotine();
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            
            if (_wasSetup)
            {
                LaunchTimerRotine();
            }
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            
            if (_timerRoutine != null)
            {
                StopCoroutine(_timerRoutine);
            }
            _timerRoutine = null;
        }

        private IEnumerator TimerCoroutine()
        {
            if (_timeManager == null)
            {
                Debug.LogError("TimeManager was not provided to timer", gameObject);
                yield break;
            }

            while (_endTime > _timeManager.CurrentTimeStamp())
            {
                yield return WaitCache.Seconds(1f);
                UpdateTimeText();
            }

            _timerRoutine = null;
        }

        private void UpdateTimeText()
        {
            if (_timeManager == null)
            {
                Debug.LogError("TimeManager was not provided to timer");
                return;
            }

            if (_timeManager.CurrentTimeStamp() < _timeOfRefreshEnabling)
            {
                return;
            }

            if (_endTime < _timeManager.CurrentTimeStamp())
            {
                TimerExpired();
                _wasSetup = false;
            }
            else
            {
                LazyInit();

                var remainingTime = _endTime - _timeManager.CurrentTimeStamp();
                var timeSpan = TimeSpan.FromSeconds(remainingTime);
                _timeText.text = timeSpan.ToLocalizedTimeFormat(_localizationManager);
            }
        }

        protected override void InitWithContextInternal(IContext context)
        {
            _localizationManager = context.Resolve<ILocalizationManager>();
        }
    }
}