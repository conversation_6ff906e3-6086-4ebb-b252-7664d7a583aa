using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI
{
    public struct ProgressData
    {
        public int CurrentProgress;
        public int MaxProgress;
    }

    public class UIProgressBarComponent : BbbMonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI _progressNumberText;
        [SerializeField] private Image _progressImage;

        /// <summary>
        /// Bonus progress is visible only during progress boost animation.
        /// </summary>
        [Header("Settings for bonus boost animation")] [SerializeField]
        private Image _bonusProgressImage;

        /// <summary>
        /// Optional animation, which will play during bonus sequence. Main animation of progress bar motion is scripted.
        /// </summary>
        [SerializeField] private Animator _bonusAnimator;

        [SerializeField] private TextMeshProUGUI _bonusNumberText;
        [SerializeField] private float _bonusProgressDuration = 4f;
        [SerializeField] private bool _showInitialFillingAnimation;
        [SerializeField] private float _delayBeforeInitialFillingAnimation = 1f;
        [SerializeField] private float _initialFillingAnimationDuration = 1f;

        [SerializeField] private float _minimumVisualProgress = 0.03f;
        [SerializeField] private bool _extendedSlashSpace;

        private const string Slash = "/";
        private const string ExtendedSlash = " / ";
        private int _currentProgress;
        private int _maxProgress;
        private int _maxTextProgress;
        private int _currentTextProgress;
        private bool _isBonusAnimationPlaying;

        private TweenerCore<float, float, FloatOptions> _initialFillingAnimationTweener;
        
        protected override void OnEnable()
        {
            if (_showInitialFillingAnimation && _currentProgress > 0)
            {
                ShowFillingAnimation();
            }
        }

        private void ShowFillingAnimation()
        {
            _initialFillingAnimationTweener?.Kill();

            RefreshView(0, _maxProgress);
            _initialFillingAnimationTweener = DOTween.To(() => 0f, (x) => { RefreshView(Mathf.RoundToInt(_currentProgress * x), _maxProgress); }, 1f, _initialFillingAnimationDuration)
                .SetDelay(_delayBeforeInitialFillingAnimation);
        }

        public void SetupProgressBar(int currentProgress, int maxProgress)
        {
            if (_isBonusAnimationPlaying) return;
            _currentProgress = currentProgress;
            _currentTextProgress = currentProgress;
            _maxProgress = maxProgress;
            _maxTextProgress = maxProgress;
            RefreshView();

            if (_showInitialFillingAnimation && gameObject.activeInHierarchy)
            {
                ShowFillingAnimation();
            }
        }

        private void RefreshView()
        {
            if (_progressNumberText != null && _progressNumberText.enabled && _progressNumberText.gameObject.activeSelf)
            {
                _progressNumberText.text = _currentTextProgress + (_extendedSlashSpace ? ExtendedSlash : Slash) + _maxTextProgress;
            }

            if (_progressImage == null)
                return;
            
            var fillAmount = (float) _currentProgress / _maxProgress;
            if (_currentProgress != 0)
            {
                fillAmount = Mathf.Max(fillAmount, _minimumVisualProgress);
            }
            _progressImage.fillAmount = fillAmount;
        }

        private void RefreshView(int currentProgress, int maxProgress)
        {
            if (_progressNumberText != null && _progressNumberText.enabled && _progressNumberText.gameObject.activeSelf)
            {
                _progressNumberText.text = currentProgress + (_extendedSlashSpace ? ExtendedSlash : Slash) + maxProgress;
            }

            if (_progressImage == null)
                return;
            
            var fillAmount = (float) currentProgress / _maxProgress;
            if (currentProgress != 0)
            {
                fillAmount = Mathf.Max(fillAmount, _minimumVisualProgress);
            }
            _progressImage.fillAmount = fillAmount;
        }

        protected override void OnDisable()
        {
            _isBonusAnimationPlaying = false;
        }
    }
}