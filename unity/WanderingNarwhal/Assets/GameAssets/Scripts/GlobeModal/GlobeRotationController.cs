using System;
using BBB;
using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace GameAssets.Scripts.GlobeModal
{
    public class GlobeRotationController : BbbMonoBehaviour
    {
        private const int HalfDegree = 180;
        private const int FullDegree = 360;

        private enum ZoomMoveType
        {
            None,
            Zooming,
            RestoringZoom,
            ZoomingIn
        }

        public event Action<string, bool> RotationFinished = delegate { };

        /// <summary>
        /// Rotation of camera holder for hover around globe effect.
        /// </summary>
        /// <remarks>
        /// Pivot center is in origin coordinates, camera is child of transform and always pointing at center. -VK
        /// </remarks>
        [SerializeField] private Transform _cameraRotationPivot;

        /// <summary>
        /// Transform for motion of camera along x axis in local space for zoom effect.
        /// </summary>
        /// <remarks>
        /// Position pivot is child of rotation pivot transform, so current orientation of camera over the globe will not affect zoom directly. -VK
        /// </remarks>
        [SerializeField] private Transform _cameraPositionPivot;
        [SerializeField] private Camera _camera;
        [SerializeField] private Vector2 _resetCoords;
        [SerializeField] private float _yawSmoothTime = 0.3f;
        [SerializeField] private float _pitchSmoothTime = 0.2f;
        [SerializeField] private float _zoomSmoothTime = 0.01f;
        [SerializeField] private float _zoomRestoreTime = 0.25f;
        [SerializeField] private float _zoomInTime = 1f;
        [SerializeField] private float _minZoom = 130f;
        [SerializeField] private float _maxZoom = 80f;
        [SerializeField] private float _maxAbsPitch = 15f;
        [SerializeField] private float _earthWidth = 100f;
        [SerializeField] private float _earthPadding = 10f;
        [SerializeField] private float _minAspect = 0.625f;
        [SerializeField] private float _maxAspect = 0.75f;
        [SerializeField] private float _minAspectFactor = 1f;
        [SerializeField] private float _maxAspectFactor = 1.25f;
        [SerializeField] private float _inertiaThreshold = 0.5f;
        [SerializeField] private float _inertiaFrameMultiplier = 0.99f;
        [SerializeField] private float _maxYawSpeed = 360f;

        [SerializeField] private float _yawEpsilon;
        [SerializeField] private float _pitchEpsilon;
        [SerializeField] private float _zoomEpsilon;

        [SerializeField]
        private float _defaultTargetZoom = 213f;
        [SerializeField] private GlobeInputController _globeInputController;

        [Header("runtime values:")]
        [Tooltip("Always update yaw and pitch state.")]
        [SerializeField] private bool _neverSleep;
        [SerializeField] private float _yaw;
        [SerializeField] private float _pitch;
        [SerializeField] private float _zoom;
        [SerializeField] private float _targetYaw;
        [SerializeField] private float _targetPitch;
        [SerializeField] private float _targetZoom;
        [SerializeField] private float _offsetTargetYaw;
        [SerializeField] private float _offsetTargetPitch;

        [Tooltip("Multiplier parameter to allow animate target zoom without affecting default zoom value.")]
        [SerializeField] private float _targetZoomMultiplier = 1f;

        private bool _isDirty;
        private bool _isZoomDirty;
        private float _yawSpeed;
        private float _pitchSpeed;
        private float _zoomSpeed;

        private float _pauseMultiplier = 1f;

        private float _currentYawSmoothTime;
        private float _currentPitchSmoothTime;

        private ZoomMoveType _zoomMoveType;
        private string _targetLocationUid;

        private Quaternion _cameraInitialRotation;
        private Vector3 _cameraInitialPosition;

        private float _lastDeltaYaw;

        public float Yaw => _yaw;
        public float Pitch => _pitch;

        public void Init()
        {
            _minZoom = CalcMinZoom();
            _zoom = _minZoom;
            _targetZoom = _zoom;

            _cameraInitialRotation = _cameraRotationPivot.localRotation;
            _cameraInitialPosition = _cameraPositionPivot.localPosition;

            Subscribe();
        }

        public void Reset()
        {
            _minZoom = CalcMinZoom();
            _zoom = _minZoom;
            _targetZoom = _zoom;
            _zoomMoveType = ZoomMoveType.None;
            _targetLocationUid = null;
            _currentYawSmoothTime = _yawSmoothTime;
            _currentPitchSmoothTime = _pitchSmoothTime;
            _pauseMultiplier = 1f;
            _isDirty = false;
            _isZoomDirty = false;

            _neverSleep = false;
            _pitchSpeed = 0f;
            _zoomSpeed = 0f;
            _yawSpeed = 0f;
            _targetYaw = _resetCoords.x;
            _targetPitch = _resetCoords.y;
            _targetZoom = 0;
            _yaw = _resetCoords.x;
            _pitch = _resetCoords.y;
            _lastDeltaYaw = 0f;

            _cameraRotationPivot.localRotation = _cameraInitialRotation;
            _cameraPositionPivot.localPosition = _cameraInitialPosition;
        }

        public void RotateToCoords(float yaw, float pitch, string targetLocationUid)
        {
            RotateToCoordsWithSpeed(yaw, pitch, _yawSmoothTime, _pitchSmoothTime, targetLocationUid);
        }

        public void RotateToCoordsWithSpeed(float yaw, float pitch, float yawSmoothTime, float pitchSmoothTime, string targetLocationUid)
        {
            SetTargetAnglesAndSmoothTimes(yaw, pitch, yawSmoothTime, pitchSmoothTime);
            SetMoveTarget(targetLocationUid);

            _lastDeltaYaw = 0f;
        }

        public void ResetTargetCoordOffset()
        {
            _targetYaw += _offsetTargetYaw;
            _targetPitch += _offsetTargetPitch;

            _offsetTargetYaw = 0;
            _offsetTargetPitch = 0;
        }

        private void Subscribe()
        {
            Unsubscribe();

            _globeInputController.ZoomEnded += ZoomEndedHandler;
            _globeInputController.Zoomed += ZoomedHandler;
            _globeInputController.Rotated += RotatedHandler;
        }

        private void Unsubscribe()
        {
            _globeInputController.ZoomEnded -= ZoomEndedHandler;
            _globeInputController.Zoomed -= ZoomedHandler;
            _globeInputController.Rotated -= RotatedHandler;
        }

        private void ZoomEndedHandler()
        {
            RestoreZoom();
        }

        private void ZoomedHandler(float delta)
        {
            AddZoom(delta);
        }

        private void RotatedHandler(float deltaYaw, float deltaPitch)
        {
            AudioProxy.PlaySound(GenericSoundIds.GlobeSpin);

            deltaYaw = Mathf.Clamp(deltaYaw / Time.deltaTime, -_maxYawSpeed, _maxYawSpeed) * Time.deltaTime;
            SetTargetAngles(_targetYaw + deltaYaw, _targetPitch + deltaPitch);
            SetMoveTarget(null);

            _lastDeltaYaw = deltaYaw;
        }

        private void AddZoom(float delta)
        {
            SetTargetZoom(_targetZoom - delta);
            _zoomMoveType = ZoomMoveType.Zooming;
        }

        public void ResetTargetZoomToDefault()
        {
            _targetZoom = _defaultTargetZoom;
            _isZoomDirty = true;
        }

        /// <summary>
        /// Target coordinate temporary offset (shift).
        /// </summary>
        /// <param name="additionalYaw">Yaw axis coord.</param>
        /// <param name="additionalPitch">Pitch axis coord.</param>
        /// <remarks>
        /// Coords offset is used during intro animation.
        /// It adds to target yaw and pitch, resulting in constant shift of camera view angle.
        /// The main purpose of this offset - to make intro animation customizable for any starting city:
        /// for example, if we want to play intro on New York city,
        /// we simply set offset from default camera intro position to New York position (this also will work for any other city).
        /// This offset is active during whole intro animation duration, and at the end it must be reset by special method. -VK
        /// </remarks>
        public void SetTargetCoordOffset(float additionalYaw, float additionalPitch)
        {
            _offsetTargetYaw = additionalYaw;
            _offsetTargetPitch = additionalPitch;
        }

        private void RestoreZoom()
        {
            SetTargetZoom(_minZoom);
            _zoomMoveType = ZoomMoveType.RestoringZoom;
        }

        public void ZoomIn(float zoom, bool force)
        {
            SetTargetZoom(zoom, force);
            _zoomMoveType = ZoomMoveType.ZoomingIn;
        }

        private void SetTargetAngles(float yaw, float pitch)
        {
            SetTargetAnglesAndSmoothTimes(yaw, pitch, _yawSmoothTime, _pitchSmoothTime);
        }

        private void SetTargetAnglesAndSmoothTimes(float yaw, float pitch, float yawSmoothTime, float pitchSmoothTime)
        {
            SetTargetYawAndSmoothTime(yaw, yawSmoothTime);
            SetTargetPitchAndSmoothTime(pitch, pitchSmoothTime);
        }

        private void SetTargetYawAndSmoothTime(float yaw, float yawSmoothTime)
        {
            _currentYawSmoothTime = yawSmoothTime;
            if (Mathf.Abs(yaw - _yaw) > FullDegree)
            {
                yaw = Mod(yaw, FullDegree);
                _yaw = Mod(_yaw, FullDegree);
            }

            _targetYaw = yaw;
            _isDirty = true;

            float Mod(float a, int n)
            {
                return (a % n + n) % n;
            }
        }

        private void SetTargetPitchAndSmoothTime(float pitch, float pitchSmoothTime)
        {
            _currentPitchSmoothTime = pitchSmoothTime;
            _targetPitch = Mathf.Min(Mathf.Abs(pitch), _maxAbsPitch) * Mathf.Sign(pitch);
            _isDirty = true;
        }

        private void SetTargetZoom(float zoom, bool force = false)
        {
            _targetZoom = force ? zoom : Mathf.Clamp(zoom, _maxZoom, _minZoom);
            _isZoomDirty = true;
        }

        private void SetMoveTarget(string locationUid)
        {
            if (_targetLocationUid != null)
            {
                RotationFinished(_targetLocationUid, false);
            }

            _targetLocationUid = locationUid;
        }

        private float CalcMinZoom()
        {
            float distance;

            if (Screen.width > Screen.height)
            {
                distance = (_earthWidth * 0.5f + _earthPadding) / Mathf.Sin(_camera.fieldOfView * 0.5f * Mathf.Deg2Rad);
            }
            else
            {
                distance = (_earthWidth * 0.5f + _earthPadding) / _camera.aspect / Mathf.Sin(_camera.fieldOfView * 0.5f * Mathf.Deg2Rad);
            }

            var t = Mathf.Clamp01((_camera.aspect - _minAspect) / (_maxAspect - _minAspect));
            var fac = _minAspectFactor + (_maxAspectFactor - _minAspectFactor) * t;
            distance *= fac;

            return distance;
        }

        private void LateUpdate()
        {
            if (!isActiveAndEnabled)
                return;

            if (_neverSleep)
            {
                // Never Sleep toggle usually active during intro animation,
                // to allow playback TargetPitch and TargetYaw prerecorded clips. -VK
                _isDirty = true;
                _isZoomDirty = true;
            }

            if (!_isDirty)
            {
                _targetYaw = _yaw;
                _targetPitch = _pitch;
            }

            if (_isDirty || _isZoomDirty)
            {
                if (_isDirty)
                {
                    if (Mathf.Abs(_lastDeltaYaw) > _inertiaThreshold)
                    {
                        SetTargetYawAndSmoothTime(_targetYaw + _lastDeltaYaw, _yawSmoothTime);
                        _lastDeltaYaw *= _inertiaFrameMultiplier;
                    }

                    _yaw = SmoothDampAngleWithTargetLimit(_yaw, _targetYaw + _offsetTargetYaw, ref _yawSpeed, _currentYawSmoothTime, _maxYawSpeed);
                    _pitch = Mathf.SmoothDampAngle(_pitch, _targetPitch + _offsetTargetPitch, ref _pitchSpeed, _currentPitchSmoothTime);

                    if (Mathf.Abs(_yaw - _targetYaw) < _yawEpsilon &&
                        Mathf.Abs(_pitch - _targetPitch) < _pitchEpsilon)
                    {
                        _isDirty = false;

                        if (_targetLocationUid != null)
                        {
                            RotationFinished?.Invoke(_targetLocationUid, false);
                            _targetLocationUid = null;
                        }
                    }
                }

                if (_isZoomDirty)
                {
                    var smoothTime =
                        _zoomMoveType == ZoomMoveType.RestoringZoom ? _zoomRestoreTime :
                        _zoomMoveType == ZoomMoveType.ZoomingIn ? _zoomInTime :
                        _zoomSmoothTime;

                    _zoom = Mathf.SmoothDamp(_zoom, _targetZoom * _targetZoomMultiplier, ref _zoomSpeed, smoothTime);

                    if (Mathf.Abs(_zoom - _targetZoom * _targetZoomMultiplier) < _zoomEpsilon)
                    {
                        _isZoomDirty = false;
                    }
                }

                if (_cameraRotationPivot != null)
                {
                    _cameraRotationPivot.localRotation = Quaternion.Euler(_pitch, _yaw * _pauseMultiplier, 0f);
                }

                if (_cameraPositionPivot != null)
                {
                    _cameraPositionPivot.localPosition = Vector3.forward * _zoom;
                }
            }
        }

        private float SmoothDampAngleWithTargetLimit(float current, float target, ref float currentVelocity, float smoothTime, float maxSpeed = float.MaxValue)
        {
            // idea of this change is it keep velocity sign if speed is big enough that it overshoots and shortest distance happens from opposite side of the range
            // like current is 50, velocity is 250 and target is 300, without this check, it would switch velocity sign to opposite because it is closer
            // though, if we are rotating the globe like crazy, we should avoid that check completely
            if (Mathf.Abs(target - current) >= HalfDegree && Mathf.Abs(currentVelocity) > HalfDegree)
            {
                var newTarget = current + Mathf.Sign(target - current) * (HalfDegree - 1f);
                target = newTarget;
            }

            var delta = Mathf.DeltaAngle(current, target);
            return Mathf.SmoothDamp(current, current + delta, ref currentVelocity, smoothTime, maxSpeed, Time.deltaTime);
        }
    }
}