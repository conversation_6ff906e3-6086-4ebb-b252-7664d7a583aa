using BBB.Core;
using BBB.DI;
using Cysharp.Threading.Tasks;

namespace BBB
{
    public sealed class HudIconsResourceManager : IContextInitializable, ISpecializedResourceManager
    {
        private GenericHudManager _genericHudManager;

        public bool IsRequired => true;
        private int _tasksLeftToLoad;

        public void Reset() {}

        public void InitializeByContext(IContext context)
        {
            _genericHudManager = context.Resolve<GenericHudManager>();
        }

        public async UniTask TryReloadAsync(string screenBeingLoaded, ScreenType screenType)
        {
            var hudIcons = _genericHudManager.GetHudIconsToPreloadTasksTasks(screenBeingLoaded, screenType);

            _tasksLeftToLoad = hudIcons.Count;

            await UniTask.WhenAll(hudIcons);
            _tasksLeftToLoad = 0;
        }

        public bool IsLoading(string screenName, string prevScreenName) => _tasksLeftToLoad > 0;

        public bool HasFailed() => false;

        public void ResetFailure() {}

        public float Progress()
        {
            return _tasksLeftToLoad == 0 ? 1f : 0f;
        }

        public void DisposeForScreen(ScreenType screenType, ScreenType currentScreenType)
        {
            // Assets will be disposed by GenericHudManager
        }
    }
}