using UnityEngine;

namespace GameAssets.Scripts.ResourceManagement
{
    public interface IEpisodeSceneResourceManager
    {
        ParticleSystem GarbFx_change { get; }
        ParticleSystem ObjAppear_Fx { get; }
        Material SceneTaskIntro { get; } 
        Material SceneTaskOutro { get; }
        void ReturnIntroFx(ParticleSystem fx);
        void ReturnFallingStarsFx(ParticleSystem fx);
    }
}
