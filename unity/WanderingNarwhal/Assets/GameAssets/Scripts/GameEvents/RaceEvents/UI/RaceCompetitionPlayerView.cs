using System;
using System.Collections.Generic;
using BBB.DI;
using BebopBee.Social;
using DG.Tweening;
using GameAssets.Scripts.SocialScreens.Teams;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.RaceEvents.UI
{
    public class RaceCompetitionPlayerView : BbbMonoBehaviour
    {
        private const string RaceProgressRenderer = "RaceProgressRenderer";

        [SerializeField] private TextMeshProUGUI _nameText;
        [SerializeField] private AsyncAvatar _asyncAvatar;
        [SerializeField] private RectTransform _rectProgressRendererHolder;
        [SerializeField] private Canvas _canvasToShowHide;
        [SerializeField] private Canvas _ownPlayerAvatarBgCanvas;
        [SerializeField] private Canvas _otherPlayerAvatarBgCanvas;
        [SerializeField] private GameObject _friendPlayerAvatarBgCanvas;
        [SerializeField] private Color _normalNicknameColor;
        [SerializeField] private Color _friendNickNameColor;

        private readonly Dictionary<string, IRacePlayerProgressRenderer> _progressRenderers = new();

        private IGameEventResourceManager _resourceManager;
        private ISocialManager _socialManager;

        public bool Enabled
        {
            set => _canvasToShowHide.enabled = value;
        }

        protected IRacePlayerProgressRenderer ProgressRenderer { get; private set; }

        public virtual void InitWithContext(IContext context)
        {
            _resourceManager = context.Resolve<IGameEventResourceManager>();
            _socialManager = context.Resolve<ISocialManager>();
        }

        public virtual void Refresh(PlayerRowViewData playerRowViewData, int index, int scoreGoal, string raceEventUid, string raceStageId, bool displayScores)
        {
            _nameText.text = playerRowViewData.Name;
            _asyncAvatar.Setup(new AvatarInfo(playerRowViewData));

            foreach (var pRenderer in _progressRenderers.Values)
                pRenderer.Enabled = false;

            ProgressRenderer = GetOrCreateProgressRenderer(raceEventUid);
            ProgressRenderer.Refresh(playerRowViewData, index, scoreGoal, displayScores);
            ProgressRenderer.Enabled = true;

            _ownPlayerAvatarBgCanvas.enabled = playerRowViewData.IsOwn;
            _otherPlayerAvatarBgCanvas.enabled = !playerRowViewData.IsOwn;

            var isFriend = _socialManager.IsTeamMate(playerRowViewData.Uid) && !playerRowViewData.IsOwn;
            _friendPlayerAvatarBgCanvas.SetActive(isFriend);
            _nameText.color = isFriend ? _friendNickNameColor : _normalNicknameColor;
        }

        public void Hide()
        {
            ProgressRenderer?.Hide();
        }

        private IRacePlayerProgressRenderer GetOrCreateProgressRenderer(string raceEventUid)
        {
            if (_progressRenderers.TryGetValue(raceEventUid, out var progressRenderer))
                return progressRenderer;

            var raceEventVisualConfig = _resourceManager.GetGenericAsset<RaceEventVisualConfig>(raceEventUid, GameEventResKeys.RaceGameEventSettings);

            var raceProgressRendererPrefab = raceEventVisualConfig.FindPrefabByName(RaceProgressRenderer);
            var newGo = Instantiate(raceProgressRendererPrefab, _rectProgressRendererHolder);
            var newRenderer = newGo.GetComponent<IRacePlayerProgressRenderer>();
            _progressRenderers[raceEventUid] = newRenderer;
            return newRenderer;
        }

        public Tween StartTransitionToActualState(PlayerRowViewData playerItem, int index, int scoreGoal, Action action)
        {
            return ProgressRenderer.StartTransitionToActualState(playerItem, index, scoreGoal, action);
        }
    }
}