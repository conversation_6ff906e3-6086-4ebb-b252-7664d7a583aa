using System;
using System.Collections.Generic;
using GameAssets.Scripts.GameEvents;
using GameAssets.Scripts.Map.UI.Controllers;

namespace BBB.RaceEvents
{
    public interface IRoyaleEventManager : IInterruptionTracker
    {
        HashSet<RoyaleEvent> LastShownEvents { get; } 
        IEnumerable<RoyaleEvent> GetAllEvents();
        RoyaleEvent GetHighestPriorityEvent();
        RoyaleEvent GetRoyaleEvent(string eventUid);
        bool ShowRoyaleEvent(RoyaleEvent royaleEvent, bool shouldProcess = true);
        void ClaimRewardFor(RoyaleEvent royaleEvent);
        bool ShouldAutoShow(EventAutoshowCondition condition);
        void EnterLevelFlow();
        bool TryAutoShowEvent(EventAutoshowCondition condition, Action closeCallback = null);
        void ProcessEventStates();
        void SuppressStatePenalty(bool suppress);
    }
}