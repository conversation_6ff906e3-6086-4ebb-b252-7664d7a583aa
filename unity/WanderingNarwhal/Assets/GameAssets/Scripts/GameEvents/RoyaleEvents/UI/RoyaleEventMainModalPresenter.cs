using System;
using System.Collections.Generic;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.RaceEvents;
using BBB.Screens;
using BBB.UI;
using BBB.UI.Level;
using GameAssets.Scripts.GameEvents.RoyaleEvents.Model;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace GameAssets.Scripts.GameEvents.RoyaleEvents.UI
{
    public class RoyaleEventMainModalPresenter : ModalsViewPresenter, IRoyaleEventMainModalPresenter, IDestroyable
    {
        [FormerlySerializedAs("_stepsTextAnimDuration")]
        [Header("Constants")]
        [SerializeField] private float _stepsTextAnimUpDuration = 0.5f;
        [SerializeField] private float _stepsTextAnimDownDuration = 0.5f;
        [SerializeField] private float _playersNumberTextAnimDuration = 1f;
        [Header("References")]
        [SerializeField] private TextMeshProUGUI _mainTitleText;
        [SerializeField] private TextMeshProUGUI _descriptionText;
        [SerializeField] private GameObject _finalStatePanel;
        [SerializeField] private TextMeshProUGUI _finalStateText;
        [SerializeField] private TextMeshProUGUI _prizeNumberText;
        [SerializeField] private AnimatableNumberText _stepsFinishedText;
        [SerializeField] private AnimatableNumberText _playersLeftText;
        [SerializeField] private ClockCountdownText _clockCountdownText;
        [SerializeField] private RoyaleEventBalloonPanel _royaleEventRacePanel;
        [SerializeField] private Button _tapToContinueOverlayButton;
        [SerializeField] private Button _infoModalButton;
        [SerializeField] private RoyaleEventTutorialView _royaleEventTutorialView;
        [SerializeField] private List<GameObject> _tutorialStepObjects;
        [SerializeField] private GameObject _fireworksCanvas;
        
        private RoyaleEventMainModalViewModel _viewModel;
        private ILocalizationManager _localizationManager;
        public event Action TapToContinueButtonPressed;
        public event Action InfoModalButtonPressed;

        protected override void OnContextInitialized(IContext context)
        {
            _localizationManager = context.Resolve<ILocalizationManager>();
            _tapToContinueOverlayButton.ReplaceOnClick(TapToContinueButtonHandler);
            _infoModalButton.ReplaceOnClick(InfoModalButtonHandler);
            _royaleEventRacePanel.Init(context);
            _royaleEventTutorialView.Init(context, _tutorialStepObjects);
        }

        public void OnRootViewActivated()
        {
            _royaleEventTutorialView.Launch(_viewModel.ShouldShowTutorial);
        }

        public void Setup(RoyaleEventMainModalViewModel viewModel)
        {
            _viewModel = viewModel;
            
            _mainTitleText.SetText(viewModel.MainTitle);
            _descriptionText.SetText(viewModel.DescText);
            _finalStatePanel.SetActive(!viewModel.FinishStateText.IsNullOrEmpty());
            _finalStateText.SetText(viewModel.FinishStateText);
            _prizeNumberText.SetText(viewModel.PrizePoolSize.ToString());
            
            _clockCountdownText.Init(_localizationManager, viewModel.TimeLeftGetter);
            
            _stepsFinishedText.SetMaxScore(viewModel.MaxStep);
            _playersLeftText.SetMaxScore(viewModel.TotalPlayers);
            
            _stepsFinishedText.SetScoreInstant(viewModel.LastShownStep);
            _playersLeftText.SetScoreInstant(viewModel.LastShownPlayersLeftInRound);

            int stepToSet;
            int playersLeftToSet;
            
            _royaleEventRacePanel.Setup(viewModel);

            if (viewModel.ChangeType.ShouldAnimateStatusChange())
            {
                //prepare for animation
                stepToSet = viewModel.LastShownStep;
                playersLeftToSet = viewModel.LastShownPlayersLeftInRound; 
                _royaleEventRacePanel.PrepareStatusChange();
            }
            else
            {
                //instant numbers update
                stepToSet = viewModel.CurrentStep;
                playersLeftToSet = viewModel.CurrentPlayersLeftInRound;
                _royaleEventRacePanel.SetupToCurrentStatus();
            }
            
            _stepsFinishedText.SetScoreInstant(stepToSet);
            _playersLeftText.SetScoreInstant(playersLeftToSet);
            _royaleEventTutorialView.Setup(viewModel);
        }

        public override void OnShowAnimationFinished()
        {
            base.OnShowAnimationFinished();
            if (_viewModel.ChangeType.ShouldAnimateStatusChange())
            {
                AnimateStatusChange(_viewModel);
                _royaleEventRacePanel.AnimateStatusChange();
            }
        }

        private void AnimateStatusChange(RoyaleEventMainModalViewModel viewModel)
        {
            var animateUp = viewModel.ChangeType.AnimateUp();
            var stepToAnimateTo = animateUp ? viewModel.CurrentStep : 0;
            var durationToAnimateStep = animateUp ? _stepsTextAnimUpDuration : _stepsTextAnimDownDuration;
            _stepsFinishedText.SetScoreAnimated(stepToAnimateTo, durationToAnimateStep);
            _playersLeftText.SetScoreAnimated(viewModel.CurrentPlayersLeftInRound, _playersNumberTextAnimDuration);

            if (viewModel.CurrentStep >= RoyaleEvent.TotalSteps - 1)
            {
                _fireworksCanvas.SetActive(true);
            }
        }
        
        protected override void OnHide()
        {
            _fireworksCanvas.SetActive(false);
            _royaleEventTutorialView.ResetState();
            _royaleEventRacePanel.ResetState();
            base.OnHide();
        }

        private void TapToContinueButtonHandler()
        {
            TapToContinueButtonPressed?.Invoke();
        }

        private void InfoModalButtonHandler()
        {
            InfoModalButtonPressed?.Invoke();
        }
    }
}