using System.Collections.Generic;

namespace BBB
{
    public interface IGameEventMilestone
    {
        string EventUid { get; }
        string RewardUid { get; }
        int RewardNumber { get; }
        string ScoreSpriteName { get; }
        string RewardSpriteName { get; }
        int StartScore { get; }
        int Goal { get; }
        string GoalName { get; }
        Dictionary<string, int> GiftBoxContent { get; }

        int GetGoal();
        int GetRelativeScore(int totalScore);
        bool IsGiftBoxReward();
        MilestoneTarget GetMilestoneTarget();
    }
}