using System.Collections;
using System.Collections.Generic;
using BBB.GameAssets.Scripts.Player;
using BBB.UI.Level;
using PBConfig;
using PBGame;

namespace BBB
{
    public class GameEventMatch3LabelCounters : IEnumerable<(string, int)>
    {
        private readonly List<(string, int)> _gameEventScores = new List<(string, int)>();

        public class Builder
        {
            private GameEventMetaConfig _metaConfig;
            private LevelState _levelState;
            private FBConfig.ProgressionLevelConfig _levelConfig;
            private readonly List<GameEventBase> _gameEventsApplied = new List<GameEventBase>();

            public static Builder Start(GameEventMetaConfig metaConfig, FBConfig.ProgressionLevelConfig levelConfig, LevelState levelState)
            {
                var builder = new Builder();
                builder._metaConfig = metaConfig;
                builder._levelConfig = levelConfig;
                builder._levelState = levelState;
                return builder;
            }

            public void Apply(GameEventBase gameEvent)
            {
                _gameEventsApplied.Add(gameEvent);
            }

            public GameEventMatch3LabelCounters Build()
            {
                var result = new GameEventMatch3LabelCounters();

                bool finished = _levelState.ReachedFinalStage();

                foreach (var gameEvent in _gameEventsApplied)
                {
                    if (finished && gameEvent.GameplayType != GameEventGameplayType.SideMap &&
                        gameEvent.GameplayType != GameEventGameplayType.EndOfContent)
                        continue;
                    
                    int scoresNumber = gameEvent.GenerateScore(Stage.Good);
                    result._gameEventScores.Add((gameEvent.Uid, scoresNumber));
                }

                return result;
            }
        }

        public IEnumerator<(string, int)> GetEnumerator()
        {
            return _gameEventScores.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        public bool HasAny()
        {
            foreach(var tuple in _gameEventScores)
                if (tuple.Item2 > 0)
                    return true;

            return false;
        }
    }
}