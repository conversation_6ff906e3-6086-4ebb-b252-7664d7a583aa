using System;
using BBB.Core;

namespace BBB
{
    public enum League
    {
        Silver = 0,
        Golden = 1,
        Fire = 2
    }

    public static class LeagueExtensions
    {
        public const string LeagueKey = "League";
    
        public static string ToSkinName(this League league)
        {
            switch (league)
            {
                case League.Silver:
                    return "Silver";
                case League.Golden:
                    return "Gold";
                case League.Fire:
                    return "Fire";
                default:
                {
                    BDebug.Log<PERSON>rror(LogCat.General, $"Wrong league value {league}");
                    return "Silver";
                }
            }
        }

        public static string ToBoxSpriteName(this League league)
        {
            switch (league)
            {
                case League.Silver:
                    return "box_silver";
                case League.Golden:
                    return "box_gold";
                case League.Fire:
                    return "box_fire";
                default:
                {
                    BDebug.LogError(LogCat.General, $"Wrong league value {league}");
                    return "Silver";
                }
            }
        }

        public static string ToSpriteName(this League league)
        {
            switch (league)
            {
                case League.Silver:
                    return "Silver_League_Icon";
                case League.Golden:
                    return "Golden_League_Icon";
                case League.Fire:
                    return "Fire_League_Icon";
                default:
                {
                    BDebug.LogError(LogCat.General, $"Wrong league value {league}");
                    return "Silver_League_Icon";
                }
            }
        }
        public static string ToMultiplierSpriteName(this League league)
        {
            switch (league)
            {
                case League.Silver:
                    return "Multiplier_Silver_League_Icon";
                case League.Golden:
                    return "Multiplier_Golden_League_Icon";
                case League.Fire:
                    return "Multiplier_Fire_League_Icon";
                default:
                {
                    BDebug.LogError(LogCat.General, $"Wrong league value {league}");
                    return "Multiplier_Silver_League_Icon";
                }
            }
        }

        public static string ToBadgeSpriteName(this League league)
        {
            switch (league)
            {
                case League.Silver:
                    return "SilverBadge";
                case League.Golden:
                    return "GoldBadge";
                case League.Fire:
                    return "FireBadge";
                default:
                {
                    BDebug.LogError(LogCat.General, $"Wrong league value {league}");
                    return "SilverBadge";
                }
            }
        }
        
        public static string ToHeaderSpriteName(this League league)
        {
            switch (league)
            {
                case League.Silver:
                    return "SilverHeader";
                case League.Golden:
                    return "GoldHeader";
                case League.Fire:
                    return "FireHeader";
                default:
                {
                    BDebug.LogError(LogCat.General, $"Wrong league value {league}");
                    return "SilverHeader";
                }
            }
        }
        
        public static string ToHudIconName(this League league)
        {
            switch (league)
            {
                case League.Silver:
                    return "SilverHud";
                case League.Golden:
                    return "GoldHud";
                case League.Fire:
                    return "FireHud";
                default:
                {
                    BDebug.LogError(LogCat.General, $"Wrong league value {league}");
                    return "SilverHud";
                }
            }
        }

        public static League GetNextLeague(this League league)
        {
            switch(league)
            {
                case League.Silver:
                    return League.Golden;
                case League.Golden:
                    return League.Fire;
                case League.Fire:
                    return League.Fire;
                default:
                {
                    BDebug.LogError(LogCat.General, $"Wrong league value {league}");
                    return League.Golden;
                }
            }
        }

        public static League GetPreviousLeague(this League league)
        {
            switch(league)
            {
                case League.Silver:
                    return League.Silver;
                case League.Golden:
                    return League.Silver;
                case League.Fire:
                    return League.Golden;
                default:
                {
                    BDebug.LogError(LogCat.General, $"Wrong league value {league}");
                    return League.Silver;
                }
            }
        }

        public static string ToShortNameLoc(this League league)
        {
            switch(league)
            {
                case League.Silver:
                    return "SILVER_LEAGUE_SHORT_LOC";
                case League.Golden:
                    return "GOLDEN_LEAGUE_SHORT_LOC";
                case League.Fire:
                    return "FIRE_LEAGUE_SHORT_LOC";
                default:
                    throw new ArgumentOutOfRangeException(nameof(league), league, null);
            }
        }
    
        public static string ToNameLoc(this League league)
        {
            switch(league)
            {
                case League.Silver:
                    return "SILVER_LEAGUE_LOC";
                case League.Golden:
                    return "GOLDEN_LEAGUE_LOC";
                case League.Fire:
                    return "FIRE_LEAGUE_LOC";
                default:
                    throw new ArgumentOutOfRangeException(nameof(league), league, null);
            }
        }
    }
}