using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Core;
using BBB.DI;
using BBB.Screens;
using BebopBee.Core.Audio;
using Cysharp.Threading.Tasks;
using Spine.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class CompetitionEventPromotionDemotionViewPresenter : ModalsViewPresenter, ICompetitionEventPromotionDemotionViewPresenter
    {
        [SerializeField] private List<LeagueText> _leagueText;
        [SerializeField] private TextMeshProUGUI _titleText;
        [SerializeField] private TextMeshProUGUI _subTitleText;
        [SerializeField] private TextMeshProUGUI _messageText;
        [SerializeField] private TextMeshProUGUI _characterNameText;

        [SerializeField] private Image _headerImage;
        [SerializeField] private GameObject _promotionState;
        [SerializeField] private GameObject _demotionState;
        [SerializeField] private List<Image> _promotionShieldIcons;
        [SerializeField] private Image _promotionShieldIcon;
        [SerializeField] private Image _demotionShieldIcon;

        [SerializeField] private Transform _characterHolderRoot;
        [SerializeField] private bool _allowSwitchCharacterSkinFromCurrentLeague = true;

        [SerializeField] private string _titleLocKeyRestart = "GE_RESTART_MODAL_TITLE";
        [SerializeField] private string _titleLocKeyPromoted = "GE_PROMOTED_MODAL_TITLE";
        [SerializeField] private string _titleLocKeyDemoted = "GE_DEMOTED_MODAL_TITLE";

        [SerializeField] private string _subTitleLocKeyRestarted = "GE_DEMOTED_MODAL_SUBTITLE";
        [SerializeField] private string _subTitleLocKeyPromoted = "GE_PROMOTED_MODAL_SUBTITLE";
        [SerializeField] private string _subTitleLocKeyDemoted = "GE_DEMOTED_MODAL_SUBTITLE";

        [SerializeField] private string _dialogMessageLocKeyRestarted = "GE_RESTARTED_MODAL_MESSAGE";
        [SerializeField] private string _dialogMessageLocKeyPromoted = "GE_PROMOTED_MODAL_MESSAGE";
        [SerializeField] private string _dialogMessageLocKeyDemoted = "GE_DEMOTED_MODAL_MESSAGE";

        private IGameEventResourceManager _gameEventResourceManager;

        private SkeletonGraphic _characterInstance;
        private bool _characterSpawned;
        private string _cachedSkeleton;

        public override void Init(IContext previousContext)
        {
            base.Init(previousContext);

            _gameEventResourceManager = previousContext.Resolve<IGameEventResourceManager>();
        }

#if UNITY_EDITOR
        [ContextMenu("Debug Setup As Promotion State")]
        private void DebugSetupPromotion()
        {
            Setup(LeagueChangeMessageType.Promotion, gameEventUid: "fennecoscars", league: League.Silver, League.Golden);
        }

        [ContextMenu("Debug Setup As Demomotion State")]
        private void DebugSetupDemotion()
        {
            Setup(LeagueChangeMessageType.Demotion, gameEventUid: "fennecoscars", league: League.Golden, League.Silver);
        }

        [ContextMenu("Debug Setup As Restart State")]
        private void DebugSetupRestart()
        {
            Setup(LeagueChangeMessageType.Restart, gameEventUid: "fennecoscars", league: League.Fire, League.Fire);
        }
#endif

        public void Setup(LeagueChangeMessageType messageType, GameEventBase gameEvent)
        {
            var competitionEvent = gameEvent as CompetitionGameEvent;

            League currentLeague = League.Golden;
            string gameEventUid = gameEvent == null ? string.Empty : gameEvent.Uid;

            if (competitionEvent != null)
            {
                if (competitionEvent.EventLeaderboard != null)
                {
                    currentLeague = competitionEvent.EventLeaderboard.CurrentLeague;
                }
            }

            var targetLeague = League.Fire;
            switch (messageType)
            {
                case LeagueChangeMessageType.Promotion:
                    targetLeague = currentLeague.GetNextLeague();
                    break;
                case LeagueChangeMessageType.Demotion:
                    targetLeague = currentLeague.GetPreviousLeague();
                    break;
            }

            Setup(messageType, gameEventUid, currentLeague, targetLeague);
        }

        private void Setup(LeagueChangeMessageType messageType, string gameEventUid, League league, League targetLeague)
        {
            if (_leagueText is {Count: > 0})
            {
                foreach (var leagueText in _leagueText)
                {
                    var isTarget = leagueText.League == targetLeague;
                    foreach (var txt in leagueText.Text)
                    {
                        txt.enabled = isTarget;
                        if (isTarget)
                        {
                            txt.text = LocalizationManager.GetLocalizedText(targetLeague.ToNameLoc());
                        }
                    }
                }
            }

            var isDemotion = messageType == LeagueChangeMessageType.Demotion;

            // Promotion and demotion states objects contains their own version of Image objcect,
            // both of which should have same Badge sprite, but only one object will be active at a time.
            _promotionState.SetActive(!isDemotion);
            _demotionState.SetActive(isDemotion);
            
            var badgeSpriteName = targetLeague.ToBadgeSpriteName();
            var headerSpriteName = targetLeague.ToHeaderSpriteName();
            
            _gameEventResourceManager
                .GetSpriteAsync(gameEventUid, headerSpriteName)
                .ContinueWith(sprite =>
                {
                    if (_headerImage == null)
                        return;
                    
                    var hasSprite = sprite != null;
                    _headerImage.enabled = hasSprite;
                    if (hasSprite)
                    {
                        _headerImage.sprite = sprite;
                    }
                    else
                    {
                        BDebug.LogError(LogCat.Resources, $"Couldn't load header '{headerSpriteName}' for event {gameEventUid}");
                    }
                });

            _gameEventResourceManager
                .GetSpriteAsync(gameEventUid, badgeSpriteName)
                .ContinueWith(sprite =>
                {
                    if (sprite == null)
                    {
                        BDebug.LogError(LogCat.Resources, $"Couldn't load badge '{badgeSpriteName}' for event {gameEventUid}");
                    }

                    switch (messageType)
                    {
                        case LeagueChangeMessageType.Promotion:
                            _promotionShieldIcons.ForEach(img => img.sprite = sprite);
                            _promotionShieldIcon.enabled = sprite != null;
                            _titleText.text = LocalizationManager.GetLocalizedText(_titleLocKeyPromoted);
                            _subTitleText.text = LocalizationManager.GetLocalizedText(_subTitleLocKeyPromoted);
                            _messageText.text = LocalizationManager.GetLocalizedText(_dialogMessageLocKeyPromoted);
                            break;

                        case LeagueChangeMessageType.Demotion:
                            _demotionShieldIcon.sprite = sprite;
                            _demotionShieldIcon.enabled = sprite != null;
                            _titleText.text = LocalizationManager.GetLocalizedText(_titleLocKeyDemoted);
                            _subTitleText.text = LocalizationManager.GetLocalizedText(_subTitleLocKeyDemoted);
                            _messageText.text = LocalizationManager.GetLocalizedText(_dialogMessageLocKeyDemoted);
                            break;

                        case LeagueChangeMessageType.Restart:
                            _promotionShieldIcons.ForEach(img => img.sprite = sprite);
                            _promotionShieldIcon.enabled = sprite != null;
                            _titleText.text = LocalizationManager.GetLocalizedText(_titleLocKeyRestart);
                            _subTitleText.text = LocalizationManager.GetLocalizedText(_subTitleLocKeyRestarted);
                            _messageText.text = LocalizationManager.GetLocalizedText(_dialogMessageLocKeyRestarted);
                            break;
                    }
                });

            var settings = _gameEventResourceManager.GetSettings(gameEventUid);
            if (settings != null && !settings.MainCharacterNameLocalizationUid.IsNullOrEmpty())
            {
                _characterNameText.text = LocalizationManager.GetLocalizedText(settings.MainCharacterNameLocalizationUid);
                _characterNameText.enabled = true;
            }
            else
            {
                _characterNameText.enabled = false;
            }

            if (!_characterSpawned)
            {
                _characterSpawned = true;

                void OnResolved(GameObject prefab)
                {
                    if (this == null) return;
                    
                    UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{prefab.name}]");
                    var go = Instantiate(prefab, _characterHolderRoot, worldPositionStays: false);
                    UnityEngine.Profiling.Profiler.EndSample();
                    _characterInstance = go.GetComponentInChildren<SkeletonGraphic>();
                    RefreshCharacter(isDemotion, targetLeague, gameEventUid);
                }

                _cachedSkeleton = $"{gameEventUid}_success";
                GenericResourceProvider.CacheAndLoadAsync<GameObject>(this, _cachedSkeleton).ContinueWith(OnResolved);
            }
            else
            {
                RefreshCharacter(isDemotion, targetLeague, gameEventUid);
            }
        }

        public override void DisposeResources()
        {
            ReleaseResources();
            base.DisposeResources();
        }

        private void ReleaseResources()
        {
            if (_cachedSkeleton != null)
            {
                GenericResourceProvider?.ReleaseCached(_cachedSkeleton);
                _cachedSkeleton = null;
            }
        }

        private void RefreshCharacter(bool isDemoted, League league, string gameEventUid)
        {
            if (_characterInstance == null) return;
            if (_characterInstance.AnimationState == null)
            {
                _characterInstance.Initialize(overwrite: false);
            }

            var animName = string.Empty;
            
            var settings = _gameEventResourceManager.GetSettings(gameEventUid);
            if (settings != null)
                animName = isDemoted ? settings.DemotionAnimationName : settings.PromotionAnimationName;
            
            _characterInstance.AnimationState.SetAnimation(0, animName, loop: true);
            if (_allowSwitchCharacterSkinFromCurrentLeague)
            {
                var skinName = league.ToSkinName();
                var skin = _characterInstance.SkeletonData.FindSkin(skinName);
                if (skin != null)
                {
                    _characterInstance.Skeleton.SetSkin(skin);
                    _characterInstance.Skeleton.SetToSetupPose();
                }
                else
                {
                    BDebug.LogError(LogCat.General, $"Skin for league '{league}' not found for SkeletonGraphic in prefab '{gameEventUid}_success'");
                }
            }
        }

        protected override void OnHide()
        {
            _promotionShieldIcon.enabled = false;
            _demotionShieldIcon.enabled = false;
            _demotionShieldIcon.sprite = null;
            _promotionShieldIcons.ForEach(image => image.sprite = null);
            ReleaseResources();
            base.OnHide();
        }

        private void PlayShieldSound()
        {
            AudioProxy.PlaySound(GenericSoundIds.QuestHud_Settle);
        }

        private void PlaySpeechbubbleSound()
        {
            AudioProxy.PlaySound(GenericSoundIds.SpeechBubbleWithCharacterShow);
        }
    }
    
    [Serializable]
    public class LeagueText
    {
        [SerializeField]
        public League League;
        [SerializeField]
        public TextMeshProUGUI[] Text;
    }
}