using BBB.DI;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using BBB.Core;
using BBB.Core.UI;
using Cysharp.Threading.Tasks;

namespace BBB
{
    public class EventLeaderboardTopPanel : BbbMonoBehaviour
    {
        [SerializeField] private List<Image> _leagueIconImages;
        [SerializeField] private GameEventReplaceableGo _gameEventReplaceableGo;

        private IGameEventResourceManager _gameEventResourceManager;

        public void Init(IContext context)
        {
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _gameEventReplaceableGo.Init(_gameEventResourceManager);
        }

        public void Refresh(string eventUid, League league)
        {
            var spriteKey = league.ToSkinName();
            _gameEventResourceManager
                .GetSpriteAsync(eventUid, spriteKey)
                .ContinueWith(sprite =>
                {
                    if (sprite == null)
                    {
                        BDebug.LogError(LogCat.Resources, $"Couldn't load league skin '{spriteKey}' for event {eventUid}");
                    }
                    
                    _leagueIconImages.ForEach(image => image.sprite = sprite);
                });

            _gameEventReplaceableGo.Refresh(eventUid, GameEventResKeys.MainAnimatedComposition);
            _gameEventReplaceableGo.InvokeOnGo(go =>
            {
                var leagueViewApplier = go.GetComponent<ILeagueViewApplier>();
                leagueViewApplier?.Apply(league);
            });
        }

        public void Clear()
        {
            _leagueIconImages.ForEach(image => image.sprite = null);
        }
    }
}