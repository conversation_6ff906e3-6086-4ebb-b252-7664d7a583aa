using System;
using BBB.UI.Core;
using GameAssets.Scripts.UI;

namespace BBB
{
    public interface ISweepstakesMilestoneViewPresenter : IViewPresenter
    {
        event Action PlayButtonPressedEvent;
        event Action SignupButtonPressedEvent;
        event Action OnInfoButtonPressedEvent;
        event Action OnTermsButtonPressedEvent;
        event Action OnBannerButtonPressedEvent;
        event Action<VideoItemWidget, int> OnVideoPlayed;
        void Refresh(SweepstakesMilestoneViewModel viewModel);
        void HideMainVideoPlayer();
    }
}