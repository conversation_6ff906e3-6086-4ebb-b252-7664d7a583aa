using BBB.DI;
using BBB.RaceEvents;
using BBB.RaceEvents.UI;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class RaceEventDebugPanel : BbbMonoBehaviour
    {
        [SerializeField] private Button _addScoreButton;
        [SerializeField] private Button _setScoreButton;
        [SerializeField] private Button _resetEventButton;
        [SerializeField] private TMP_InputField _scoreInputField;
        [SerializeField] private Toggle _opponentsDebugToggle;

        private IRaceEventManager _raceEventManager;
        private string _eventUid;

        public void Init(IContext context)
        {
            _raceEventManager = context.Resolve<IRaceEventManager>();
        
            _addScoreButton.ReplaceOnClick(OnAddScoreButton);
            _setScoreButton.ReplaceOnClick(OnSetScoreButton);
            _resetEventButton.ReplaceOnClick(OnResetEventButton);

            _opponentsDebugToggle.isOn = RaceCompetitionViewModel.OpponentsDebug;
            _opponentsDebugToggle.onValueChanged.RemoveAllListeners();
            _opponentsDebugToggle.onValueChanged.AddListener(OnToggleValueChangedHandler);
        }

        private void OnToggleValueChangedHandler(bool value)
        {
            RaceCompetitionViewModel.OpponentsDebug = value;
        }

        public void Refresh(string eventUid)
        {
            _eventUid = eventUid;
        }

        private void OnAddScoreButton()
        {
            var score = ParseScoreInputField();

            if (score > 0)
            {
                _raceEventManager.IncrementScores(_eventUid, score);
            }
        }

        private void OnSetScoreButton()
        {
            var score = ParseScoreInputField();
            if (score <= 0) return;
            _raceEventManager.DebugSetScore(_eventUid, score);
        }

        private void OnResetEventButton()
        {
            _raceEventManager.DebugReleaseEvent(_eventUid);
        }

        private int ParseScoreInputField()
        {
            if (int.TryParse(_scoreInputField.text, out var result))
            {
                return result;
            }
            return -1;
        }
    }
}