using BebopBee.UnityEngineExtensions;
using UnityEngine;

namespace BBB
{
    public class GameEventVictoryView : BbbMonoBehaviour
    {
        [SerializeField] private Animator _animator;
        private static readonly int HideId = Animator.StringToHash("Hide");

        public void Show()
        {
            if (_animator)
            {
                _animator.Rebind();
            }
           
            gameObject.SetActive(true);
        }

        public void StartHiding()
        {
            _animator.SetTrigger(HideId);
        }

        public void Hide()
        {
            gameObject.SetActive(false);
        }

        public void Apply(GameEventBase gameEvent)
        {
            switch (gameEvent)
            {
                case CompetitionGameEvent competitionGameEvent:
                {
                    var leagueViewApplier = gameObject.GetComponent<ILeagueViewApplier>();
                    if (leagueViewApplier != null)
                    {
                        var eventLeaderboard = competitionGameEvent.EventLeaderboard;
                        var league = League.Silver;
                        if (eventLeaderboard.IsInitialized)
                        {
                            league = competitionGameEvent.EventLeaderboard.CurrentLeague;
                        }
                        leagueViewApplier.Apply(league);
                    }
                    break;
                }
            }
        }
    }
}