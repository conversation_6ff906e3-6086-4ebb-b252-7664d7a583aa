using BBB.DI;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class GameEventDebugPanel : BbbMonoBehaviour
    {
        [SerializeField] private Button _addScoreButton;
        [SerializeField] private Button _setScoreButton;
        [SerializeField] private Button _resetEventButton;
        [SerializeField] private TMP_InputField _scoreInputField;
        [SerializeField] private UnifiedGameEventWidget _unifiedProgressBar;

        private IGameEventManager _gameEventManager;
        private string _eventUid;

        public void Init(IContext context)
        {
            _gameEventManager = context.Resolve<IGameEventManager>();
        
            _addScoreButton.ReplaceOnClick(OnAddScoreButton);
            _setScoreButton.ReplaceOnClick(OnSetScoreButton);
            _resetEventButton.ReplaceOnClick(OnResetEventButton);
        }

        public void Refresh(string eventUid)
        {
            _eventUid = eventUid;
        }

        private void OnAddScoreButton()
        {
            var score = ParseScoreInputField();

            if (score > 0)
            {
                _gameEventManager.IncrementScores(_eventUid, score);
                if(_unifiedProgressBar != null)
                    _unifiedProgressBar.DebugAnimate(_eventUid, score);
            }
        }

        private void OnSetScoreButton()
        {
            var score = ParseScoreInputField();
            if (score <= 0) return;
            _gameEventManager.DebugSetScore(_eventUid, score);
            
            if(_unifiedProgressBar != null)
                _unifiedProgressBar.Refresh();
        }

        private void OnResetEventButton()
        {
            _gameEventManager.DebugReleaseEvent(_eventUid);
           
            if(_unifiedProgressBar != null)
                _unifiedProgressBar.Refresh();
        }

        private int ParseScoreInputField()
        {
            if (int.TryParse(_scoreInputField.text, out var result))
            {
                return result;
            }
            return -1;
        }
    }
}