using System;
using System.Collections.Generic;
using BBB.Core;
using FBConfig;
using GameAssets.Scripts.UI.OverlayDialog;
using PBGame;
using Spine.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace BBB.TeamEvents.UI
{
    public class MilestoneChestView : BbbMonoBehaviour
    {
        private const string ReachedWithoutYouTextLoc = "TEAM_COOP_CHEST_CONTRIBUTE_LOC";
        private const string ToBeReachedTextLoc = "TEAM_COOP_CHEST_TO_BE_REACHED_LOC";
        private const string CollectedTextLoc = "TEAM_COOP_CHEST_COLLECTED_LOC";
        public event Action<int> OnChestOpen;

        private static readonly int AllowCollectTrigger = Animator.StringToHash("AllowCollect");
        private static readonly int CollectTrigger = Animator.StringToHash("Collect");

        private const string CheckmarkStateName = "CheckmarkState";
        private const string DefaultState = "DefaultState";
        private const string ReadyToCollectState = "ReadyToCollectState";

        private const string SpineClosedAnim = "closed";
        private const string SpineOpenedAnim = "opened";

        [SerializeField] private Animator _chestAnimator;
        [SerializeField] private SkeletonGraphic _chestSkeletonGraphic;
        [SerializeField] private TextMeshProUGUI _milestoneScoreText;
        [SerializeField] private Button _openButton;
        [SerializeField] private Button _overlayOpenButton;
        [FormerlySerializedAs("_speechBubbleConfig")] [SerializeField] private OverlayDialogConfig _overlayDialogConfig;

        private IOverlayDialogManager _overlayDialogManager;
        private TeamCoopMilestoneConfig _milestoneConfig;
        private TeamEvent _teamEvent;
        private int _index;
        private MilestoneState _currentState;
        private string _stateToPlayOnEnable;

        private IEnumerable<Button> Buttons
        {
            get
            {
                yield return _openButton;
                yield return _overlayOpenButton;
            }
        }

        public void Refresh(IOverlayDialogManager overlayDialogManager, TeamEvent teamEvent,
            TeamCoopMilestoneConfig milestoneConfig, int index)
        {
            _overlayDialogManager = overlayDialogManager;
            _teamEvent = teamEvent;
            _index = index;
            _milestoneConfig = milestoneConfig;

            _milestoneScoreText.text = milestoneConfig.Scores.ToString();

            foreach (var button in Buttons)
            {
                button.ReplaceOnClick(() => OnButtonTapped(false));
                button.enabled = false;
            }
        }

        public void OnHide()
        {
            _stateToPlayOnEnable = null;
            _milestoneScoreText.enabled = true;
        }

        private Dictionary<string, int> GetRewards()
        {
            return _teamEvent.GetMilestoneRewardForIndex(_index);
        }

        public void OnButtonTapped(bool enableAutoHide)
        {
            _overlayDialogConfig.AutoHide = enableAutoHide;
            _overlayDialogConfig.DisplayType = DisplayType.RewardSpeechBubble;

            switch (_currentState)
            {
                case MilestoneState.ToBeReached:
                    var rewardsToBeReached = GetRewards();
                    if (rewardsToBeReached != null)
                    {
                        var scoresToNextMilestone = _teamEvent.GetScoreLeftToMilestone(_index);
                        _overlayDialogConfig.RewardToDisplay = rewardsToBeReached;
                        _overlayDialogConfig.TextToDisplay = ToBeReachedTextLoc;
                        _overlayDialogConfig.TextArgs = new object[] {scoresToNextMilestone};
                        _overlayDialogManager.ToggleOverlayDialog(_overlayDialogConfig);
                    }

                    break;
                case MilestoneState.ReachedWithoutYou:
                    _overlayDialogConfig.RewardToDisplay = null;
                    _overlayDialogConfig.TextToDisplay = ReachedWithoutYouTextLoc;
                    _overlayDialogConfig.TextArgs = null;
                    _overlayDialogManager.ToggleOverlayDialog(_overlayDialogConfig);
                    break;
                case MilestoneState.ReachedAndReadyToCollect:
                    OpenChest();
                    break;
                case MilestoneState.Collected:
                    var collectedRewards = GetRewards();
                    if (collectedRewards != null)
                    {
                        _overlayDialogConfig.RewardToDisplay = collectedRewards;
                        _overlayDialogConfig.TextToDisplay = CollectedTextLoc;
                        _overlayDialogConfig.TextArgs = new object[] {_milestoneConfig.Scores};
                        _overlayDialogManager.ToggleOverlayDialog(_overlayDialogConfig);
                    }

                    break;
            }
        }

        private void OpenChest()
        {
            OnChestOpen?.Invoke(_index);
        }

        protected override void OnEnable()
        {
            if (_stateToPlayOnEnable != null)
            {
                _chestAnimator.Play(_stateToPlayOnEnable);
            }
        }

        public void SetStateInstantly(MilestoneState milestoneState)
        {
            _currentState = milestoneState;

            var spineAnimToPlay = StateToSpineAnimName(milestoneState);

            if (spineAnimToPlay == null)
            {
                return;
            }

            _chestSkeletonGraphic.AnimationState.SetAnimation(0, spineAnimToPlay, true);

            var stateToPlay = StateToMecanimAnimStateName(milestoneState);
            if (_chestAnimator.gameObject.activeInHierarchy)
            {
                _chestAnimator.Play(stateToPlay);
            }
            else
            {
                _stateToPlayOnEnable = stateToPlay;
            }

            RefreshOpenButtons(milestoneState);
        }

        private void RefreshOpenButtons(MilestoneState milestoneState)
        {
            bool reachedAndReadyToCollect = milestoneState == MilestoneState.ReachedAndReadyToCollect;
            _openButton.enabled = reachedAndReadyToCollect;
            _overlayOpenButton.enabled = !reachedAndReadyToCollect;
            _overlayOpenButton.targetGraphic.raycastTarget = !reachedAndReadyToCollect;
        }

        private string StateToSpineAnimName(MilestoneState milestoneState)
        {
            switch (milestoneState)
            {
                case MilestoneState.ToBeReached:
                case MilestoneState.ReachedAndReadyToCollect:
                case MilestoneState.ReachedWithoutYou:
                    return SpineClosedAnim;
                case MilestoneState.Collected:
                    return SpineOpenedAnim;
            }

            BDebug.LogError(LogCat.Events,
                $"milestone state {milestoneState} has no spine animation for {gameObject.name}");
            return null;
        }

        private string StateToMecanimAnimStateName(MilestoneState milestoneState)
        {
            switch (milestoneState)
            {
                case MilestoneState.ReachedAndReadyToCollect:
                case MilestoneState.ReachedWithoutYou:
                    return ReadyToCollectState;
                case MilestoneState.Collected:
                    return CheckmarkStateName;
                case MilestoneState.ToBeReached:
                default:
                    return DefaultState;
            }
        }

        public void AnimateToState(MilestoneState milestoneState)
        {
            if (_currentState == milestoneState)
            {
                return; // No need to animate if the state is already the same
            }
            
            var prevState = _currentState;
            _currentState = milestoneState;

            var prevSpineAnimToPlay = StateToSpineAnimName(prevState);
            var curSpineAnimToPlay = StateToSpineAnimName(milestoneState);

            if (prevSpineAnimToPlay == null || curSpineAnimToPlay == null)
            {
                return;
            }

            if (prevSpineAnimToPlay != curSpineAnimToPlay)
            {
                _chestSkeletonGraphic.AnimationState.SetAnimation(0, prevSpineAnimToPlay, false);
                _chestSkeletonGraphic.AnimationState.AddAnimation(0, curSpineAnimToPlay, true, 0f);
            }

            switch (milestoneState)
            {
                case MilestoneState.ReachedAndReadyToCollect:
                case MilestoneState.ReachedWithoutYou:
                    _chestAnimator.SetTrigger(AllowCollectTrigger);
                    break;
                case MilestoneState.Collected:
                    _chestAnimator.SetTrigger(CollectTrigger);
                    break;
                case MilestoneState.ToBeReached:
                default:
                    _chestAnimator.Play(DefaultState);
                    break;
            }

            RefreshOpenButtons(milestoneState);
        }

        public void HideText()
        {
            _milestoneScoreText.enabled = false;
        }

        public void MakeButtonsInteractable(bool interactable)
        {
            foreach (var button in Buttons)
            {
                button.interactable = interactable;
            }
        }
    }
}