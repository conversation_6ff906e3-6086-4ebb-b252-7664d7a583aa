using BBB.Core;
using BBB.DI;
using Spine.Unity;
using UnityEngine;

namespace BBB.TeamEvents.UI
{
    public class CarAnimationView : BbbMonoBehaviour
    {
        [SerializeField] private RectTransform _skeletonGraphicHolder;
        
        private GameObject _carSkeletonGaphicObject;
        private SkeletonGraphic _skeletonGraphic;
        
        private IGameEventResourceManager _gameEventResourceManager;
        private int _lastSetMilestoneIndex;

        public void Init(IContext context)
        {
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
        }
        
        public void InstantiatePrefab(TeamEvent teamEvent)
        {
            if (_carSkeletonGaphicObject == null)
            {
                var prefab = _gameEventResourceManager.GetGenericAsset<GameObject>(teamEvent.Uid, GameEventResKeys.MainObject);
                _carSkeletonGaphicObject = Instantiate(prefab, _skeletonGraphicHolder);
                _skeletonGraphic = _carSkeletonGaphicObject.GetComponent<SkeletonGraphic>();
            }
        }

        private static string MilestoneIndexToInitialIdleAnimation(int milestoneIndex)
        {
            switch (milestoneIndex)
            {
                case 0: return CarAnimationNames.Idle0r;
                case 1: return CarAnimationNames.Idle1l;
                case 2: return CarAnimationNames.Idle2r;
                case 3: return CarAnimationNames.Idle3;
            }

            return null;
        }

        private static string MilestoneIndexToAfterMoveIdleAnimation(int milestoneIndex)
        {
            switch (milestoneIndex)
            {
                case 1: return CarAnimationNames.Idle1r;
                case 2: return CarAnimationNames.Idle2l;
                case 3: return CarAnimationNames.Idle3;
            }

            return null;
        }

        private static string MilestoneIndexToMoveIdleAnimation(int milestoneIndex)
        {
            switch (milestoneIndex)
            {
                case 1: return CarAnimationNames.Move0to1;
                case 2: return CarAnimationNames.Move1to2;
                case 3: return CarAnimationNames.Move2to3;
            }

            return null;
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="milestoneIndex">from 0 to 3, 0 meaning initial state, can be -1 detecting an error</param>
        public void SetToMilestoneIndex(int milestoneIndex)
        {
            _lastSetMilestoneIndex = milestoneIndex;
            if (milestoneIndex == -1)
            {
                BDebug.LogError(LogCat.General, $"TeamCoop: milestone -1 can not be used in CarAnimation");
                return;
            }

            if (_skeletonGraphic == null)
            {
                BDebug.LogError(LogCat.General, $"TeamCoop: milestone -1 can not be used in CarAnimation");
                return;
            }

            var initialIdleAnimationName = MilestoneIndexToInitialIdleAnimation(milestoneIndex);

            if (!string.IsNullOrEmpty(initialIdleAnimationName))
            {
                _skeletonGraphic.AnimationState.SetAnimation(0, initialIdleAnimationName, true);
            }
        }

        public void AnimateToMilestoneIndex(int milestoneIndex)
        {
            for (int i = _lastSetMilestoneIndex + 1; i <= milestoneIndex; i++)
            {
                var moveAnimationName = MilestoneIndexToMoveIdleAnimation(i);
            
                if (string.IsNullOrEmpty(moveAnimationName))
                {
                    BDebug.LogError(LogCat.General, $"TeamCoop: for milestone {milestoneIndex} move animation is not found in in CarAnimation");
                    return;
                }
            
                if(i == _lastSetMilestoneIndex + 1)
                    _skeletonGraphic.AnimationState.SetAnimation(0, moveAnimationName, false);
                else
                {
                    _skeletonGraphic.AnimationState.AddAnimation(0, moveAnimationName, false, 0f);
                }
            }

            var afterMoveIdleAnimationName = MilestoneIndexToAfterMoveIdleAnimation(milestoneIndex);
            if (!string.IsNullOrEmpty(afterMoveIdleAnimationName))
            {
                _skeletonGraphic.AnimationState.AddAnimation(0, afterMoveIdleAnimationName, true, 0f);
            }
        }

        public void ClearAnimations()
        {
            _skeletonGraphic.AnimationState.ClearTrack(0);
            _skeletonGraphic.Skeleton.SetToSetupPose();
        }
    }
}