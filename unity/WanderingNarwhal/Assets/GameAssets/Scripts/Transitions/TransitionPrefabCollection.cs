using System.Collections.Generic;
using JetBrains.Annotations;
using UnityEngine;

namespace BBB.UI.Transitions
{
    public class TransitionPrefabCollection : TransitionPrefab
    {
        // it is expected to be static and not to be reset on restart of the app
        private static bool _wasRandomized;
        private static int _globalIterationIndex = -1;

        [SerializeField] private List<GameObject> _prefabs;
        [SerializeField] private Transform _root;
        [SerializeField] private LocalizedTextPro _title;

        private TransitionPrefab[] _instantiatedTransitionPrefabs;

        protected override void Awake()
        {
            base.Awake();
            _instantiatedTransitionPrefabs = new TransitionPrefab[_prefabs.Count];

            if (!_wasRandomized)
            {
                _globalIterationIndex = Random.Range(0, _instantiatedTransitionPrefabs.Length);
                _wasRandomized = true;
            }
        }

        public override void Show()
        {
            base.Show();
            _globalIterationIndex++;
            var index = _globalIterationIndex % _instantiatedTransitionPrefabs.Length;
            var transitionPrefab = _instantiatedTransitionPrefabs[index];
            if (_instantiatedTransitionPrefabs[index] == null)
            {
                transitionPrefab = Instantiate(_prefabs[index], _root).GetComponent<TransitionPrefab>();
                transitionPrefab.Hide(true);

                _instantiatedTransitionPrefabs[index] = transitionPrefab;
            }

            transitionPrefab.Show();
            var transitionNameKey = transitionPrefab.TransitionNameKey;
            _title.gameObject.SetActive(!transitionNameKey.IsNullOrEmpty());
            _title.SetTextId(transitionNameKey);
        }

        public override void Hide(bool immediately = false)
        {
            var index = _globalIterationIndex % _instantiatedTransitionPrefabs.Length;
            if (_instantiatedTransitionPrefabs[index] != null)
                _instantiatedTransitionPrefabs[index].Hide(immediately);
            base.Hide(immediately);
        }

        // unity doesn't like virtual override methods for animation events
        [UsedImplicitly]
        private void ShownAnimationHandler()
        {
            OnShown();
        }

        // unity doesn't like virtual override methods for animation events
        [UsedImplicitly]
        private void HiddenAnimationHandler()
        {
            OnHidden();
        }

        protected override void OnHidden()
        {
            base.OnHidden();

            var index = _globalIterationIndex % _instantiatedTransitionPrefabs.Length;
            if (_instantiatedTransitionPrefabs[index] != null)
                _instantiatedTransitionPrefabs[index].Hide(true);
        }
    }
}