using BBB.DI;
using BebopBee;

namespace GameAssets.Scripts.Deeplink.Commands
{
    public abstract class DeepLinkCommand : CommandBase
    {
        protected override void CommandExecutionStart(IContext context)
        {
            base.CommandExecutionStart(context);
            // We set command to be failed or success when we end the flow so that we can make sure that only one command at a time is in process
            // So timeout is not needed, otherwise multiple parallel actions could be triggered and it will lead to unexpected flow 
            IsTimeConstraint = false;
        }
    }
}