using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Web;
using BBB;
using BBB.Core;
using BebopBee.Core.UI;
using BebopBee.Social;
using GameAssets.Scripts.Deeplink.Gifts;

namespace GameAssets.Scripts.Deeplink.Commands
{
    public sealed class AwardGiftCommand : DeepLinkValidationCommand
    {
        private string _optionalTitle;
        private string _optionalSubtitle;
        private string _message;

        private ShowMode _showMode;
        private GiftModalOptions _giftModalOptions;

        public AwardGiftCommand(DeepLinkData deepLinkData) : base(deepLinkData)
        {
        }

        protected override void OnExecute(Dictionary<string, object> deepLinkParams)
        {
            BDebug.Log(LogCat.DeepLink, $"AwardGiftCommand.Execute started");
            // We need to parse rewards again, just in case it was changed from the time url link was created
            var rawReward = Convert.ToString(deepLinkParams.GetSafe(SocialRequestKeys.Reward));
            if (rawReward.IsNullOrEmpty())
            {
                BDebug.LogError(LogCat.DeepLink, "AwardGiftCommand can't be executed, because link received after validation is missing rewards");
                MarkAsInvalid();
                return;
            }

            var deserializedReward = ParseRewards(rawReward);
            if (deserializedReward.IsNullOrEmpty())
            {
                BDebug.LogError(LogCat.DeepLink, $"AwardGiftCommand can't be executed, because no rewards could be parsed: {rawReward}");
                MarkAsInvalid();
                return;
            }

            _giftModalOptions = new GiftModalOptions
            {
                Title = _optionalTitle,
                Subtitle = _optionalSubtitle,
                Message = _message,
                ShowMode = _showMode,
            };

            _giftModalOptions.Rewarded -= GiftRewardedHandler;
            _giftModalOptions.Rewarded += GiftRewardedHandler;

            DeepLinkExecutionManager.ScheduleAwardDeeplink(deserializedReward, _giftModalOptions);
        }

        protected override void Deserialize(Dictionary<string, object> data)
        {
            base.Deserialize(data);

            _optionalTitle = HttpUtility.UrlDecode(Convert.ToString(data.GetSafe("title")));
            _optionalSubtitle = HttpUtility.UrlDecode(Convert.ToString(data.GetSafe("subtitle")));
            _message = HttpUtility.UrlDecode(Convert.ToString(data.GetSafe("message")));
            if (!data.TryGetValue("immediate", out var showMode) || (Convert.ToString(showMode) != "true" && !Enum.TryParse(Convert.ToString(showMode), out _showMode)))
            {
                _showMode = ShowMode.Delayed;
            }
        }

        private Dictionary<string, int> ParseRewards(string rawReward)
        {
            var deserializedReward = RewardsUtility.RewardStringToDict(rawReward);
            DeepLinkExecutionManager.FilterGifts(deserializedReward);
            if (deserializedReward is { Count: > 0 })
            {
                var isAnyPositive = false;
                foreach (var val in deserializedReward.Values)
                {
                    if (val > 0)
                    {
                        isAnyPositive = true;
                        break;
                    }
                }

                if (!isAnyPositive)
                {
                    BDebug.LogError(LogCat.DeepLink, "Gift reward has no valid amount: " + rawReward);
                    return null;
                }
            }
            else
            {
                BDebug.LogError(LogCat.DeepLink, "Gift reward failed to deserialize: " + rawReward);
                return null;
            }

            return deserializedReward;
        }

        private void GiftRewardedHandler()
        {
            _giftModalOptions.Rewarded -= GiftRewardedHandler;

            MarkAsExecuted();
        }
    }
}