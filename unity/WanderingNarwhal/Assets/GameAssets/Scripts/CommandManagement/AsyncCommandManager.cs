using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BebopBee;
using UnityEngine;

namespace BBB
{
    public class AsyncCommandManager : BbbMonoBehaviour, IAsyncCommandManager
    {
        private readonly Queue<CommandBase> _pendingCommands = new();

        private readonly HashSet<Type> _singleCmdTypeFilter = new()
        {
            typeof(GetAllMessagesCommand)
        };

        private bool _paused;
        private IContext _context;
        private IScreensManager _screenManager;
        private bool _isChecking;


        public void Launch(IContext context)
        {
            _context = context;
            _screenManager = context.Resolve<IScreensManager>();
            
            ExecuteCommands();
        }

        private void ExecuteCommands()
        {
            if (_isChecking)
            {
                return;
            }

            _isChecking = true;
            StartCoroutine(CheckCommands());
        }

        public void PushCmd(CommandBase cmd, bool ignorePauseCheck = false)
        {
            var cmdType = cmd.GetType();

            if (!ignorePauseCheck && _paused)
            {
                BDebug.LogFormat(LogCat.AsyncCommand, "Async command {0} dropped by pause", cmdType.Name);
                return;
            }

            var isSingleCmdType = _singleCmdTypeFilter.Contains(cmdType);

            if (isSingleCmdType)
            {
                foreach (var pendingCmd in _pendingCommands)
                {
                    if (pendingCmd.GetType() != cmdType) continue;
                    
                    BDebug.LogFormat(LogCat.AsyncCommand, "Async command {0} dropped by single type", cmdType.Name);
                    return;
                }
            }

            BDebug.LogFormat(LogCat.AsyncCommand, "Async command {0} enqueued", cmdType.Name);
            _pendingCommands.Enqueue(cmd);
        }


        public void Pause()
        {
            _paused = true;
        }

        public void DropCommands()
        {
            var str = string.Empty;
            foreach (var cmd in _pendingCommands)
                str += cmd.GetType().Name + " ";

            BDebug.LogFormat( LogCat.AsyncCommand,"AsyncCommandManager stop, commands dropped: {0}", str);

            _pendingCommands.Clear();
        }
        public void Resume()
        {
            BDebug.Log( LogCat.AsyncCommand,"AsyncCommandManager resumed");
            _paused = false;
        }

        private IEnumerator CheckCommands()
        {
            while (true)
            {
                var screenType = _screenManager.GetCurrentScreenType();
                if ((screenType & ScreenType.Levels) > 0)
                    yield return null;

                if (!_paused && _pendingCommands.Count > 0)
                {
                    var cmd = _pendingCommands.Dequeue();

                    while (cmd.CurrentStatus is CommandStatus.Running or CommandStatus.Pending)
                    {
                        try
                        {
                            cmd.Execute(_context);
                        }
                        catch (Exception e)
                        {
                            Debug.LogError("AsyncCommandManager exception " + e.Message + " " + e.StackTrace);
                            break;
                        }

                        if(!cmd.IsFinished()) yield return null;
                    }

                    if (_paused) continue;
                }

                yield return null;
            }
        }
    }
}