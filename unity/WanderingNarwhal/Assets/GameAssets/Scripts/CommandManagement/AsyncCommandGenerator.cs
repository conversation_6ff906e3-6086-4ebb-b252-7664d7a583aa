using System;
using System.Collections;
using BBB.DI;
using BebopBee;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core;
using UnityEngine;

namespace BBB
{
    /// <summary>
    /// Event for reset timer of Get All Messages loop process.
    /// </summary>
    /// <remarks>
    /// Get all messages is happening in cycle with constant delay.
    /// Trigger this event via Event Dispatcher to force cycle to start next loop sooner.
    /// </remarks>
    public class GetAllMessagesTimerResetEvent : IEvent
    {
    }

    public class AsyncCommandGenerator : BbbMonoBehaviour
    {
        /// <summary>
        /// Min duration in bacgkround after which the fetch command cooldown will be reset.
        /// </summary>
        /// <remarks>
        /// This is required for making Fetch operation happen immediately after restore from background,
        /// if background time was long enough.
        /// </remarks>
        private const float BackgroundDurationToResetTime = 5f;
        private const float WaitTime = 120f;
        private IAsyncCommandManager _asyncCommandManager;
        private IEventDispatcher _eventDispatcher;
        private float _timer;
        private DateTime _focusTime;

        public void Init(IContext context)
        {
            _asyncCommandManager = context.Resolve<IAsyncCommandManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            
            GenerateCommandsAsync().Forget();
            
            _eventDispatcher.RemoveListener<GetAllMessagesTimerResetEvent>(OnResetEventReceived);
            _eventDispatcher.AddListener<GetAllMessagesTimerResetEvent>(OnResetEventReceived);
        }

#if BBB_DEBUG
        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.W))
            {
                Debug.Log("Debug: 'W' key pressed - forcing reloading of events inbox from server.");
                OnResetEventReceived(null);
            }
        }
#endif

        private void OnApplicationFocus(bool hasFocus)
        {
            if (hasFocus)
            {
                if ((DateTime.Now - _focusTime).TotalSeconds > BackgroundDurationToResetTime)
                {
                    OnResetEventReceived(null);
                }
            }
            else
            {
                _focusTime = DateTime.Now;
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _eventDispatcher?.RemoveListener<GetAllMessagesTimerResetEvent>(OnResetEventReceived);
        }

        /// <summary>
        /// Reset fetch cooldown timer to make next fetch process immediately.
        /// </summary>
        private void OnResetEventReceived(GetAllMessagesTimerResetEvent obj)
        {
            _timer = WaitTime - 0.5f;
        }

        private async UniTaskVoid GenerateCommandsAsync()
        {
            while (true)
            {
                if (ConnectivityStatusManager.ConnectivityReachable)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(WaitTime), ignoreTimeScale: true);

                    var sequence = new CommandsSequence(true);
                    sequence.AddCommand(new FetchConfigsCommand());
                    sequence.AddCommand(new FetchBrainCloudConfigsCommand());
                    
                    _asyncCommandManager.PushCmd(sequence);
                }
                else
                {
                    await UniTask.Yield(PlayerLoopTiming.Update);
                }
            }
        }
    }
}