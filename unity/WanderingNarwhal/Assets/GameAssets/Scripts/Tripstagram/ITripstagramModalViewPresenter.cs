using System;
using BBB.UI.Core;

namespace GameAssets.Scripts.Tripstagram
{
    public interface ITripstagramModalViewPresenter : IModalsViewPresenter
    {
        event Action<string> OnScenePlayClicked; 
        event Action<string> OnSceneViewClicked; 
        event Action<string> OnSceneBuildClicked;
        event Action<string> OnSceneIconClicked; 
        void Setup(TripstagramModalViewModel tripstagramModalViewModel);
    }
}
