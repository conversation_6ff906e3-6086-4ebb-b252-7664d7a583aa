using System.Threading;
using BBB;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.UI;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Map;
using GameAssets.Scripts.Messages;

namespace GameAssets.Scripts.Tripstagram
{
    public class TripstagramModalController : BaseModalsController<ITripstagramModalViewPresenter>
    {
        private const int ThreadCount = 1;
        private static readonly SemaphoreSlim SceneItemSlim = new(ThreadCount, ThreadCount);
        private IPlayerManager _playerManager;

        private readonly TripstagramModalViewModel _tripstagramModalViewModel = new();
        private IEpisodicScenesManager _episodicScenesManager;
        private IModalsBuilder _modalsBuilder;
        private IEventDispatcher _eventDispatcher;
        private IEpisodeTaskManager _episodeTaskManager;
        private IScreensManager _screensManager;
        private ILocalizationManager _localizationManager;

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);
            _playerManager = context.Resolve<IPlayerManager>();
            _episodicScenesManager = context.Resolve<IEpisodicScenesManager>();
            _episodeTaskManager = context.Resolve<IEpisodeTaskManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _screensManager = context.Resolve<IScreensManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
        }

        protected override async void OnShow()
        {
            base.OnShow();
            Subscribe();
            await InitializeViewModel();
            View.Setup(_tripstagramModalViewModel);
        }

        private async UniTask InitializeViewModel()
        {
            var player = _playerManager.Player;
            _tripstagramModalViewModel.AllScenes ??= new();
            _tripstagramModalViewModel.AllScenes.Clear();
            var allScenes = _tripstagramModalViewModel.AllScenes;
            foreach (var episodeScene in _episodicScenesManager.OrderedScenes)
            {
                var episodeSceneUid = episodeScene.Uid;
                var scene = await GetOrCreateSceneModel(episodeSceneUid);
                scene.SceneProgress = _episodeTaskManager.GetProgressOf(episodeSceneUid).Count;
                scene.SceneTaskCount = _episodeTaskManager.GetOrCreateConfigsOf(episodeSceneUid).Count;

                if (episodeSceneUid == player.CurrentEpisodeScene && !_episodeTaskManager.IsSceneCompleted(episodeSceneUid))
                {
                    _tripstagramModalViewModel.CurrentScene = scene;
                    scene.TripstagramItemState = TripstagramItemState.InProgress;
                }
                else if (player.OpenedEpisodeScenes.Contains(episodeSceneUid))
                {
                    scene.TripstagramItemState = TripstagramItemState.Completed;
                }
                else
                {
                    scene.TripstagramItemState = TripstagramItemState.Locked;
                }
            }

            return;

            async UniTask<TripstagramSceneItemModel> GetOrCreateSceneModel(string episodeSceneUid)
            {
                await SceneItemSlim.WaitAsync();
                try
                {
                    if (!allScenes.TryGetValue(episodeSceneUid, out var scene))
                    {
                        scene = new TripstagramSceneItemModel
                        {
                            SceneUid = episodeSceneUid
                        };
                        allScenes.Add(episodeSceneUid, scene);
                    }

                    scene.SceneCityName = _localizationManager.getLocalizedText(_episodicScenesManager.CityNameOf(episodeSceneUid));
                    scene.SceneCountryName = _localizationManager.getLocalizedText(_episodicScenesManager.CountryNameOf(episodeSceneUid));
                    scene.SceneCityItemTitle = _episodicScenesManager.CityTitleNameOf(episodeSceneUid);
                    scene.SceneSubTitle = _episodicScenesManager.SubTitleOf(episodeSceneUid);
                    scene.SceneUniqueInfo = _episodicScenesManager.UniqueInfoOf(episodeSceneUid);
                    scene.SceneOrder = _episodicScenesManager.OrderOf(episodeSceneUid);
                    scene.SceneIcon = await _episodicScenesManager.IconOf(episodeSceneUid);

                    return scene;
                }
                finally
                {
                    SceneItemSlim.Release();
                }
            }
        }

        private void Subscribe()
        {
            Unsubscribe();
            View.OnScenePlayClicked += PlayClickHandler;
            View.OnSceneViewClicked += SceneViewClickHandler;
            View.OnSceneBuildClicked += BuildClickHandler;
            View.OnSceneIconClicked += IconClickHandler;
        }

        private void Unsubscribe()
        {
            View.OnScenePlayClicked -= PlayClickHandler;
            View.OnSceneViewClicked -= SceneViewClickHandler;
            View.OnSceneBuildClicked -= BuildClickHandler;
            View.OnSceneIconClicked -= IconClickHandler;
        }

        private void SceneViewClickHandler(string sceneUid)
        {
            if (sceneUid.IsNullOrEmpty())
                return;

            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnHud.Name, DauInteractions.TapOnHud.TravelJournal,
                DauInteractions.TapOnHud.TravelJournalView));
            var viewEpisodicSceneEvent = _eventDispatcher.GetMessage<ViewEpisodicSceneEvent>();
            viewEpisodicSceneEvent.Set(sceneUid, true, _screensManager.GetCurrentScreenType());
            _eventDispatcher.TriggerEvent(viewEpisodicSceneEvent);
        }

        private void PlayClickHandler(string sceneUid)
        {
            OnCloseClicked();
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnHud.Name, DauInteractions.TapOnHud.TravelJournal,
                DauInteractions.TapOnHud.TravelJournalPlay));
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<LevelFlowRequestedEvent>());
        }

        private void BuildClickHandler(string sceneUid)
        {
            OnCloseClicked();
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnHud.Name, DauInteractions.TapOnHud.TravelJournal,
                DauInteractions.TapOnHud.TravelJournalBuild));
            var viewEpisodicSceneEvent = _eventDispatcher.GetMessage<BuildEpisodicSceneEvent>();
            viewEpisodicSceneEvent.Set(sceneUid, true, _screensManager.GetCurrentScreenType());
            _eventDispatcher.TriggerEvent(viewEpisodicSceneEvent);
        }

        private async void IconClickHandler(string sceneUid)
        {
            if (sceneUid.IsNullOrEmpty() ||
                !_episodeTaskManager.IsSceneCompleted(sceneUid) ||
                !_playerManager.Player.OpenedEpisodeScenes.Contains(sceneUid)) return;

            var snapshotModalController = _modalsBuilder.CreateModalView<TripstagramSnapshotModalController>(ModalsType.TripstagramSnapshotModal);
            var snapshot = await _episodicScenesManager.SnapshotOf(sceneUid);
            var sceneCityName = _episodicScenesManager.CityNameOf(sceneUid);
            var sceneCountryName = _episodicScenesManager.CountryNameOf(sceneUid);
            var sceneSubTitle = _episodicScenesManager.SubTitleOf(sceneUid);
            var sceneUniqueInfo = _episodicScenesManager.UniqueInfoOf(sceneUid);

            snapshotModalController.Setup(snapshot, sceneCityName, sceneCountryName, sceneSubTitle, sceneUniqueInfo);
            snapshotModalController.Show();
        }

        protected override void OnHide()
        {
            base.OnHide();
            Unsubscribe();
        }
    }

    public class BuildEpisodicSceneEvent : Message<string, bool, ScreenType>
    {
    }

    public class ViewEpisodicSceneEvent : Message<string, bool, ScreenType>
    {
    }
}