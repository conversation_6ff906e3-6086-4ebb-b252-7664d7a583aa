using BBB.BrainCloud;
using PBConfig;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.DailyTrivia
{
    public class DailyTriviaQuestionCard : BbbMonoBehaviour
    {
        [SerializeField] private Animator _animator;
        [SerializeField] private LocalizedTextPro _triviaQuestionText;

        [SerializeField] private Image _image;
        [SerializeField] private AspectRatioFitter _imageAspectRatioFitter;

        public void Setup(TriviaData triviaData, TriviaImagesLoader triviaImagesLoader)
        {
            _triviaQuestionText.SetTextId(triviaData.Question);

            if (_image != null)
            {
                if (!triviaImagesLoader.IsImagesReady)
                {
                    Debug.LogError("Attempt to show card with images when images are not loaded yet, that should not be the case");
                    return;
                }

                var questionImage = triviaImagesLoader.QuestionImage;
                if (questionImage == null)
                {
                    Debug.LogError("QuestionImage is not ready");
                    return;
                }

                _image.sprite = questionImage;
                _imageAspectRatioFitter.aspectRatio = questionImage.rect.width / questionImage.rect.height;
            }
        }

        public void Show()
        {
            gameObject.SetActive(true);
        }

        public void Hide()
        {
            _animator.SetTrigger("Hide");
        }

        public void HideWithoutAnswer()
        {
            _animator.SetTrigger("HideWithoutAnswer");
        }
    }
}