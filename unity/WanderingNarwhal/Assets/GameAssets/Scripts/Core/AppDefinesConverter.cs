namespace BebopBee
{
    public enum EnvType
    {
        Dev = 0,
        Staging = 1,
        Prod = 2
    }

    public static partial class AppDefinesConverter
    {
#if BBB_PROD
        private static EnvType _selectedEnvType = EnvType.Prod;
#elif BBB_STAGING
        private static EnvType _selectedEnvType = EnvType.Staging;
#else
        private static EnvType _selectedEnvType = EnvType.Dev;
#endif

        public static bool MobileNotEditor => !UnityEditor && (UnityAndroid || UnityIos);

        public static void SelectEnvType(EnvType envType)
        {
            _selectedEnvType = envType;
        }
        
        public static bool BbbLine
        {
            get
            {
#if BBB_LINE
                return true;
#else
                return false;
#endif
            }
        }


        public static bool BbbAdsTestAndNotProd
        {
            get
            {
#if BBB_ADS_TEST
                return !Prod;
#else
                return false;
#endif
            }
        }
        
        public static bool BbbDebug =
#if BBB_DEBUG
            true;
#else
            false;
#endif

        public static bool BbbLog =
#if B<PERSON>_LOG
            true;
#else
            false;
#endif

        public static bool UnityEditor =
#if UNITY_EDITOR
            true;
#else
            false;
#endif

        public static bool UnityStandalone =
#if UNITY_STANDALONE
            true;
#else
            false;
#endif

        public static bool UnityIos =
#if UNITY_IOS
            true;
#else
            false;
#endif

        public static bool UnityAndroid =
#if UNITY_ANDROID
            true;
#else
            false;
#endif

        public static bool UnityAndroidDevice =
#if UNITY_ANDROID
            !UnityEditor;
#else
            false;
#endif

        public static bool UnityIphone =
#if UNITY_IPHONE
            true;
#else
            false;
#endif

        public static bool ConfigCheck =
#if BBB_CONFIGCHECK
            true;
#else
            false;
#endif
        public static bool RpcCommandFakeServer =
#if BBB_RPC_FAKE_SERVER
            true;
#else
            false;
#endif
        public static bool Cheats  =
#if BBB_CHEATS
            true;
#else
            false;
#endif
        public static bool Prod => _selectedEnvType == EnvType.Prod;
        public static bool Staging => _selectedEnvType == EnvType.Staging;

        public static bool MmpEnabled  =
#if !BBB_NO_MMP
            true;
#else
            false;
#endif
        public static bool AmplitudeLogs =
#if BBB_AMPLITUDE_LOGS
            true;
#else
            false;
#endif
        
        public static bool UnityCloudBuild =
#if UNITY_CLOUD_BUILD
            true;
#else
            false;
#endif
    }
}