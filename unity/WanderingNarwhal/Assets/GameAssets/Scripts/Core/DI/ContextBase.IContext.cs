using System;

namespace BBB.DI
{
    public partial class ContextBase : IContext
    {
        public IContextReleaser Releaser { get; }

        public T Resolve<T>(string tag = null, bool allowNull = false) where T : class
        {
            T ret = default; 
            if (AvailableContext.Resolvers.TryGetValue(new UniqType(typeof(T), tag), out var resolver))
            {
                var instance = resolver.Resolve<T>();
                if (resolver is not InstanceResolver && instance is IContextInitializable contextInitializable)
                {
                    contextInitializable.InitializeByContext(AvailableContext);
                }
                ret = instance;
            }

            if (_previousContext != null && ret.IsNull())
                ret = _previousContext.Resolve<T>(tag);
            
            if (!ret.IsNull() || allowNull)
                return ret;
            throw new Exception($"Can't find resolver by {new UniqType(typeof(T), tag)}");
        }

        public T Resolve<T, TArg1>(TArg1 arg1, string tag = null) where T : class
            where TArg1 : class
        {
            T ret = default;
            if (AvailableContext.Resolvers.TryGetValue(new UniqType(typeof(T), tag), out var resolver))
            {
                var instance = resolver.Resolve<T, TArg1>(arg1);
                if (resolver is not InstanceResolver && instance is IContextInitializable contextInitializable)
                {
                    contextInitializable.InitializeByContext(AvailableContext);
                }

                ret = instance;
            }

            if (_previousContext != null && ret.IsNull())
            {
                ret = _previousContext.Resolve<T, TArg1>(arg1, tag);
            }
            
            if (!ret.IsNull())
                return ret;
            throw new Exception($"Can't find resolver by {new UniqType(typeof(T), tag)}");
        }

        public T Resolve<T, TArg1, TArg2>(TArg1 arg1, TArg2 arg2, string tag = null) where T : class
            where TArg1 : class
            where TArg2 : class
        {
            if (AvailableContext.Resolvers.TryGetValue(new UniqType(typeof(T), tag), out var resolver))
            {
                var instance = resolver.Resolve<T, TArg1, TArg2>(arg1, arg2);
                if (resolver is not InstanceResolver && instance is IContextInitializable contextInitializable)
                {
                    contextInitializable.InitializeByContext(AvailableContext);
                }

                return instance;
            }

            if (_previousContext != null)
                return _previousContext.Resolve<T, TArg1, TArg2>(arg1, arg2, tag);

            throw new Exception($"Can't find resolver by {new UniqType(typeof(T), tag)}");
        }

        public T Resolve<T, TArg1, TArg2, TArg3>(TArg1 arg1, TArg2 arg2, TArg3 arg3, string tag = null) where T : class
            where TArg1 : class
            where TArg2 : class
            where TArg3 : class
        {
            if (AvailableContext.Resolvers.TryGetValue(new UniqType(typeof(T), tag), out var resolver))
            {
                var instance = resolver.Resolve<T, TArg1, TArg2, TArg3>(arg1, arg2, arg3);
                if (resolver is not InstanceResolver && instance is IContextInitializable contextInitializable)
                {
                    contextInitializable.InitializeByContext(AvailableContext);
                }

                return instance;
            }

            if (_previousContext != null)
                return _previousContext.Resolve<T, TArg1, TArg2, TArg3>(arg1, arg2, arg3, tag);

            throw new Exception($"Can't find resolver by {new UniqType(typeof(T), tag)}");
        }
    }
}