using System;
using System.Collections.Generic;
using GameAssets.Scripts.Promotions.Banners;

namespace BBB.Core.Analytics
{
    public static class DauInteractions
    {
        public const string Name = "dau_interactions";
        public const string Missing = "missing";

        public static class Poi
        {
            public const string Name = "poi";

            public const string PoiLike = "poi_like";
            public const string PoiShare = "poi_share";
            public const string PoiOpenLink = "poi_open_link";
        }

        public static class AutoPopups
        {
            public const string Name = "auto_popups";

            public const string Promotions = "promotions"; 
            public const string DailyTrivia = "daily_trivia"; 
            public const string AdLootBox = "ad_loot_box";
            public const string GameEvent = "game_event";
            public const string Passport = "passport";
            public const string CollectionIntro = "collections";
            public const string Intro = "intro";
            public const string DailyTasks = "daily_tasks";
            public const string SweepstakesDailyCollect = "sweepstakes_daily_collect";
        }

        public static class TapOnAutoPopups
        {
            public const string Name = "tap_on_auto_popups";

            public const string AdLootBoxClick = "ad_loot_box_clicks";
            public const string GameEventClick = "game_event_clicks";
            public const string DailyTriviaClicks = "daily_trivia_clicks";
            public const string PassportClick = "passport_clicks";
            public const string CollectionIntroClicks = "collections_clicks";
            public const string DailyTasksClicks = "daily_tasks_clicks";
            
            public const string AdLootBoxClose = "ad_loot_box_close";
            public const string GameEventClose = "game_event_close";
            public const string DailyTriviaClose = "daily_trivia_close";
            public const string PassportClose = "passport_close";

            private static readonly HashSet<string> _expectedLogs = new();
            
            public static void AwaitLogs(params string[] familyNames)
            {
                _expectedLogs.UnionWith(familyNames);
            }

            public static void ClearAwaitLogs(params string[] familyNames)
            {
                _expectedLogs.ExceptWith(familyNames);
            }

            private static bool IsAwaitingLog(string familyName)
            {
                return _expectedLogs.Contains(familyName);
            }

            public static void LogIfExpected(string family, string id = "")
            {
                if (IsAwaitingLog(family))
                    Analytics.LogEvent(new DauInteractionsEvent(Name, family, id));
            }
        }

        public static class Promotions
        {
            public const string Name = "promotions";

            public const string Survey = "survey";
            public const string Input = "input";
            public const string IceBreaker = "ice_breaker_survey";
        }

        public static class Collection
        {
            public const string Name = "collections";

            public const string Info = "info";
            public const string Set = "set";
            public const string Claim = "claim";
            public const string UseWildCArd = "use_wild_card";
            public const string WildCardTokenInfo = "use_wild_card";
        }
        
        public static class DailyTasks
        {
            public const string Name = "daily_tasks";

            public const string ClaimTask = "claim_task";
            public const string ClaimStreak = "claim_streak";
            public const string ClaimDailyStreak = "claim_daily_streak";
            public const string CompleteTask = "complete_task";
            public const string RefreshTask = "refresh_task";
            public const string TapOnTask = "tap_on_task";
        }

        public static class TapOnBanner
        {
            public const string Name = "tap_on_hud";

            public static string GetAnalyticsName(BannerPlacement bannerPlacement)
            {
                switch (bannerPlacement)
                {
                    case BannerPlacement.OutOfMoves:
                        return "out_of_moves";
                    case BannerPlacement.OutOfLives:
                        return "out_of_lives";
                    case BannerPlacement.None:
                    default:
                        return "none";
                }
            }
        }

        public static class TapOnHud
        {
            public const string Name = "tap_on_hud";

            public const string Passport = "passport";
            public const string PassportVIP = "passport_vip";
            public const string CitiesList = "cities_list";
            public const string LeaderBoards = "leader_boards";
            public const string Tripstagram = "tripstagram";
            public const string TripstagramLevel = "tripstagram_level";
            public const string Promotions = "promotions"; // promotion uid
            public const string Menu = "menu"; // menu element
            public const string Social = "social"; // social tab
            public const string Globe = "globe";
            public const string Home = "home";
            public const string Story = "story";
            public const string Level = "level";
            public const string Play = "play";
            public const string Rank = "rank";
            public const string Lives = "lives";
            public const string Settings = "settings";
            public const string SpecialPromo = "special_promo";
            public const string Wallet = "wallet";
            public const string Didi = "didi";
            public const string DailyTrivia = "daily_trivia";
            public const string DailyTasks = "daily_tasks";
            public const string DailyLogin = "daily_login";
            public const string GameEvent = "event";
            public const string Trophies = "trophies";
            public const string PlusButtonRegular = "plus_button_regular";
            public const string PlusButtonPremium = "plus_button_premium";
            public const string PlusButtonVIP = "plus_button_vip";
            public const string PlusButtonBoost = "plus_button_boost";
            public const string Shop = "shop";
            public const string Collection = "collections";
            
            public const string TravelJournal = "travel_journal";
            public const string TravelJournalView = "view";
            public const string TravelJournalPlay = "play";
            public const string TravelJournalBuild = "build";
            
            public const string EditMode = "edit_mode";
            public const string EditModeHammer = "hammer_button";
            public const string EditModeCamera = "camera_button";
            public const string NotEnoughStars = "not_enough_stars";
            public const string NotReadyTask = "not_ready_task";
            
            public const string Stars = "stars";
            public const string Profile = "profile";
            
            public enum MenuItems
            {
                Travel,
                Social,
                ComingSoon,
                Explore,
                Passport,
                TravelJournal,
                GetCoins,
                Friends,
                NextStop,
                Ranks,
                Settings,
                LikeUs,
                Achievements,
                HelpDesk,
                FAQ,
                Facebook,
                Didi,
                WebChat,
            }

            public const string CompleteButton = "complete_button";
            public const string RestoreButton = "restore_button";
            public const string PreviewButton = "preview_button";
            public const string DiscoverButton = "discover_button";
            
            
            public static string GetItemByPassportPage(PassportTab passportTab)
            {
                switch (passportTab)
                {
                    case PassportTab.Info:
                        return "stats_page";
                    case PassportTab.Achievements:
                        return "achievements_page";
                    default:
                        throw new ArgumentOutOfRangeException(nameof(passportTab), passportTab, null);
                }
            }
        }

        public static class TapOnSettings
        {
            public const string Name = "tap_on_settings";

            public const string Bgm = "bgm";
            public const string Sfx = "sfx";
            public const string Vo = "vo";
            public const string Vibration = "vibration";
            public const string Notifications = "notifs";
            public const string Hints = "hints";
            
            public const string Privacy = "privacy";
            public const string Faq = "faq";
            public const string Fb = "fb";
            public const string FbGroup = "fb_group";
            public const string Community = "community";
            public const string Insta = "insta";
            public const string Restore = "restore";
            public const string Snapimals = "snapimals";
            public const string Terms = "terms";
        }

        public static class BuddyGiftCard
        {
            public const string Name = "buddy_gift_card";

            public const string Rolled = "rolled";
            public const string Accepted = "accepted";
            public const string Sent = "sent";
            public const string RejectedToSend = "rejected_to_send";
            public const string RejectedToReceive = "rejected_to_receive";
        }

        public static class Sweepstakes
        {
            public const string Name = "sweepstakes";

            public const string ProgressBarReward = "progress_bar_reward_";
            public const string CarouselReward = "carousel_reward_";
            public const string SignUp = "sign_up";
            public const string KeepPlaying = "keep_playing";
            public const string Info = "info";
            public const string Video = "video_";
            public const string SweepstakesScreen = "sweepstakes_screen_";
            public const string SweepstakesRewardScreen = "sweepstakes_reward_screen_";
            public const string SweepstakesReward = "sweepstakes_reward_";
            public const string SweepstakesEventScreen = "sweepstakes_event_screen_";
            public const string Terms = "terms";
            public const string Carousel = "carousel";
            public const string ProgressBar = "progress_bar";
            public const string SweepstakesDailyCollect = "sweepstakes_daily_collect";
            public const string EmailInputSuccess = "email_input_success";
            public const string EmailInputFailure = "email_input_failure";
        }

        public static class Invite
        {
            public const string Name = "invite";

            public const string InviteFriends = "invite_friends";
            public const string WA = "whatsapp";
            public const string FB = "fb";
            public const string SMS = "sms";
        }

        public static class Replay
        {
            public const string Name = "replay";
            
            public const string ReplayHappened = "replay_interaction_happened";
            public const string ReplayShared = "replay_shared";
        }

        public static class Didi
        {
            public const string Name = "didi";

            public const string Reject = "reject_to_send";
            public const string Steal = "steal";
            public const string Revenge = "revenge";
            public const string NotRevenge = "not_revenge";

            public const string InfoScreen = "info_screen";
            public const string Info = "info";

            public const string EventFavorites = "event_favorites";
            public const string Add = "add";
            public const string Remove = "remove";

            public const string EventScreen = "event_screen";
            public const string Claim = "claim";
            public const string Play = "play";
            public const string Nudge = "nudge";
            
            public const string ChallengeScreen = "challenge_screen";
            public const string Challenge = "challenge";
            
            public const string SendScreen = "send_screen";
            public const string Send = "send";
            public const string OtherPlayers = "other_players";
        }

        public static class OutOfMoves
        {
            public const string CategoryName = "oom";
            public const string Screen1 = "oom_screen_1";
            public const string Screen2 = "oom_screen_2";
            public const string Screen3 = "oom_screen_3";
            public const string Close = "close";
            public const string ExtraMovesInventory = "extra_moves_inventory";
            public const string ExtraMovesCoins = "extra_moves_coins";
            public const string Store = "store";
        }
        
        public static class Banners
        {
            public const string CategoryName = "carousel";
            public const string Shown = "_shown";
            public const string Clicked = "_clicked";
        }

        public static class EditProfile
        {
            public const string CategoryName = "edit_profile";
            public const string Login = "login";
        }
    }
}