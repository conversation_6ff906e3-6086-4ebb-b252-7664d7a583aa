using System.Collections.Generic;

namespace BBB.Core.Analytics
{
    public class SurveyEvent : BaseEvent
    {
        public SurveyEvent(string surveyUid, string answerUid, string answerNumber)
        {
            var data = new Dictionary<string, object>()
            {
                { CustomEventParameters.SurveyUid, surveyUid },
                { CustomEventParameters.AnswerUid, answerUid },
                { CustomEventParameters.AnswerNumber, answerNumber },
            };

            Initialize(EventNames.Survey, data);
        }
    }
}