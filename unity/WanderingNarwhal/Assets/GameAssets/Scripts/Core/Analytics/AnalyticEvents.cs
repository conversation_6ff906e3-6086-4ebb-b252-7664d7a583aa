using System;
using System.Collections.Generic;
using BebopBee;
using UnityEngine;
using UnityEngine.Purchasing;

namespace BBB.Core.Analytics
{
    public class DeepLinkReceivedEvent : BaseEvent
    {
        public DeepLinkReceivedEvent(string feature, string url, string source)
        {
            var data = new Dictionary<string, object>()
            {
                { CustomEventParameters.DynamicLinkFeature, feature },
                { CustomEventParameters.DynamicLinkUrl, url },
                { CustomEventParameters.DynamicLinkSource, source }
            };

            Initialize(EventNames.DeepLinkReceived, data);
        }
    }

    public class PoiInstallEvent : BaseEvent
    {
        public PoiInstallEvent(string poiUid, string senderUid)
        {
            Initialize(EventNames.PoiInstall, new Dictionary<string, object>()
            {
                { CustomEventParameters.PoiUid, poiUid },
                { CustomEventParameters.SenderUid, senderUid }
            });
        }
    }

    public class ReplayInstall : BaseEvent
    {
        public ReplayInstall(string levelUid, string senderUid)
        {
            var dict = new Dictionary<string, object>
            {
                { CustomEventParameters.LevelUid, levelUid },
                { CustomEventParameters.SenderUid, senderUid }
            };

            Initialize(EventNames.ReplayInstall, dict);
        }
    }

    public class IAPPurchaseEvent : BaseEvent
    {
        public Product Product { get; }
        public double Price { get; }
        public decimal LocalizePrice { get; }
        public string ProductId { get; }
        public string StoreProductId { get; }
        public int Amount { get; }
        public string IsoCurrency { get; }

        public string EventId => Product.transactionID;
        public string ProductFamily { get; }

        public string Receipt => Product.receipt;
        public string Payload { get; private set; }
        public string PurchaseDataJson { get; private set; }
        public string Signature { get; private set; }

        public string PurchaseSource { get; }
        public string PurchasePathString { get; }

        public IAPPurchaseEvent(Product product, double price, decimal localizePrice, string productId,
            string storeProductId, string productFamily, int amount, string isoCurrency,
            string purchaseSource, int retries)
        {
            Product = product;
            Price = price;
            LocalizePrice = localizePrice;
            ProductId = productId;
            StoreProductId = storeProductId;
            ProductFamily = productFamily;
            Amount = amount;
            IsoCurrency = isoCurrency;
            PurchaseSource = purchaseSource;
            PurchasePathString = PurchasePath.GetPath();

            Initialize(EventNames.Iap, new Dictionary<string, object>()
            {
                { CustomEventParameters.EventId, EventId },
                { CustomEventParameters.PurchaseValue, price },
                { CustomEventParameters.LocalizedPrice, localizePrice },
                { CustomEventParameters.ProductId, productId },
                { CustomEventParameters.ProductFamily, productFamily },
                { CustomEventParameters.CurrencyName, isoCurrency },
                { CustomEventParameters.StoreProductId, storeProductId },
                { CustomEventParameters.PurchaseSource, purchaseSource },
                { CustomEventParameters.PurchasePath, PurchasePathString },
                { CustomEventParameters.NumberOfRetry, retries }
            });

            FillExtraData();
        }

        private void FillExtraData()
        {
            if (Receipt.IsNullOrEmpty() || (!AppDefinesConverter.UnityIos && !AppDefinesConverter.UnityAndroid)) return;

            try
            {
                BDebug.Log(LogCat.Iap, $"IAPPurchaseEvent receipt:{Receipt}");
                var json = MiniJSON.Json.Deserialize(Receipt) as Dictionary<string, object>;
                var payloadStr = json.GetSafe("Payload") as string;
                BDebug.Log(LogCat.Iap, $"IAPPurchaseEvent payloadStr:{payloadStr}");

                if (payloadStr.IsNullOrEmpty()) return;

                if (AppDefinesConverter.UnityIos)
                {
                    Payload = payloadStr;
                }
                else if (AppDefinesConverter.UnityAndroid)
                {
                    var payload = MiniJSON.Json.Deserialize(payloadStr) as Dictionary<string, object>;
                    Signature = payload?.GetSafe("signature") as string;
                    BDebug.Log(LogCat.Iap, $"Signature {Signature}");
                    PurchaseDataJson = payload?.GetSafe("json") as string;
                    BDebug.Log(LogCat.Iap, $"PurchaseDataJson {PurchaseDataJson}");
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }
    }

    public class IAPClickedEvent : BaseEvent
    {
        public IAPClickedEvent(string source, string id)
        {
            Initialize(EventNames.IapClicked, new Dictionary<string, object>() { { CustomEventParameters.PurchaseSource, source }, { CustomEventParameters.ProductId, id } });
        }
    }

    public class IAPNativeDialogCallBackReceivedEvent : BaseEvent
    {
        public const string StatusFail = "fail";
        public const string StatusSuccess = "success";

        public IAPNativeDialogCallBackReceivedEvent(string status, string id, string failureReason = null)
        {
            var eventParams = new Dictionary<string, object>() { { CustomEventParameters.Status, status }, { CustomEventParameters.ProductId, id } };

            if (failureReason != null)
            {
                eventParams[CustomEventParameters.FailureReason] = failureReason;
            }

            Initialize(EventNames.IapNativeDialogCallback, eventParams);
        }
    }

    public class IAPVerificationEvent : BaseEvent
    {
        public const string StatusFail = "fail";
        public const string StatusSuccess = "success";

        public IAPVerificationEvent(string status, string id)
        {
            Initialize(EventNames.IapVerification, new Dictionary<string, object>() { { CustomEventParameters.Status, status }, { CustomEventParameters.ProductId, id } });
        }
    }

    public class IAPResultEvent : BaseEvent
    {
        public const string StatusFail = "fail";
        public const string StatusSuccess = "success";

        public const string FailureReasonVerificationFailed = "VerificationFailed";
        public const string FailureReasonNoNetwork = "NoNetwork";
        public const string FailureReasonIAPNotReady = "IAPNotReady";
        public const string FailureReasonConfigProblem = "ConfigProblem";
        public const string FailureReasonNoValidProduct = "NoValidProduct";
        public const string FailureReasonInProgressPurchase = "InProgressPurchase";
        public const string FailureReasonUnknown = "Unknown";

        public IAPResultEvent(string status, string id, string failureReason = null)
        {
            var eventParams = new Dictionary<string, object>() { { CustomEventParameters.Status, status }, { CustomEventParameters.ProductId, id } };

            if (failureReason != null)
            {
                eventParams[CustomEventParameters.FailureReason] = failureReason;
            }

            Initialize(EventNames.IapResult, eventParams);
        }
    }
}