using System.Threading;
using BBB.Core.ResourcesManager.Asset;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace BBB.Core.ResourcesManager
{
    public enum AssetLoadPriority
    {
        Immediately,
        InQueue,
    }
    
    public interface IAssetsManager
    {
        UniTask<Sprite> LoadSpriteAsync(string spriteName, bool logError = true);
        UniTask<IAssetLoaded<T>> LoadAsync<T>(string assetName, AssetLoadPriority priority = AssetLoadPriority.InQueue, bool forceEnqueue = false, bool logError = true, CancellationToken externalToken = default) where T : Object;
        UniTask<IAssetLoaded<T>[]> LoadAllAsync<T>(string assetName, AssetLoadPriority priority = AssetLoadPriority.InQueue, bool forceEnqueue = false, bool logError = true, CancellationToken externalToken = default) where T : Object;
        void ReleaseAssets();
        void UnloadAsset(string assetName);
        void UnloadAsset(Object asset);
    }
}