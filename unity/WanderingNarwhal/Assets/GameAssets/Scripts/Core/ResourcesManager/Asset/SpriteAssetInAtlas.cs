using Object = UnityEngine.Object;
using UnityEngine.U2D;

namespace BBB.Core.ResourcesManager.Asset
{
    public class SpriteAssetInAtlas<T> : BaseAssetLoaded<T> where T : Object
    {
        private readonly IAssetLoaded<SpriteAtlas> _parentAtlas;

        public SpriteAssetInAtlas(string name, T obj, IAssetLoaded<SpriteAtlas> parentAtlas) : base(name, obj)
        {
            _parentAtlas = parentAtlas;
        }

        public override IAssetLoaded<T1> ConvertAsset<T1>()
        {
            var result = new SpriteAssetInAtlas<T1>(Name, Asset as T1, _parentAtlas)
            {
                ReferenceCounter = ReferenceCounter
            };
            return result;
        }

        public override void Dispose()
        {
            base.Dispose();
            if (CanFree())
            {
                Object.Destroy(Asset); 
                _parentAtlas.Dispose();
            }
        }
    }
}