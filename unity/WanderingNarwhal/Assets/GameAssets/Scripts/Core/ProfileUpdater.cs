using BBB;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.UI.Level;
using PBGame;

namespace BebopBee
{
    public class ProfileUpdater
    {
        private readonly ILocationManager _locationManager;
        private readonly IConfig _config;
        private readonly ILeaderboardManager _leaderboardManager;
        private readonly WeeklyLeaderboardManager _weeklyLeaderboardManager;
        private readonly IAccountManager _accountManager;
        private readonly IEventDispatcher _dispatcher;
        private readonly UserSettings _userSettings;

        public ProfileUpdater(IContext context, IAccountManager accountManager)
        {
            _config = context.Resolve<IConfig>();
            _locationManager = context.Resolve<ILocationManager>();
            _leaderboardManager = context.Resolve<ILeaderboardManager>();
            _weeklyLeaderboardManager = context.Resolve<WeeklyLeaderboardManager>();
            _dispatcher = context.Resolve<IEventDispatcher>();
            _userSettings = context.Resolve<UserSettings>();
            _accountManager = accountManager;
        }

        public void MonitorDataUpdated()
        {
            Subscribe();
            RefreshNotificationsEnabledSettings();
        }

        private void RefreshNotificationsEnabledSettings()
        {
            _accountManager.Profile.NotificationsEnabled = _userSettings.NotificationsON;
        }
        
        // Force refresh on providing because this data was corrupted previously
        public void RefreshHighestPassedLevel()
        {
            var location = _locationManager.MainProgressionLocation;
            var highestPassedLevelUid = location.GetHighestPassedLevelUid();

            if (_accountManager.Profile.HighestPassedLevelId != highestPassedLevelUid)
            {
                _accountManager.Profile.UpdateLevelAndLocation(location.Uid, highestPassedLevelUid);
            }
        }

        private void OnLevelPassed(LevelState levelState, bool nextStageReached, bool isStartedLevelPlay)
        {
            // Null level state is valid case on NUX level, we just ignore any profile update
            if (levelState == null)
                return;

            var currentLocation = _locationManager.MainProgressionLocation;
            var currentLocationUid = currentLocation.Uid;
            nextStageReached &= !_accountManager.RemoteProgressSelected;
            if (nextStageReached && _accountManager.Profile != null)
            {
                _accountManager.Profile.AddTrophyAndSetLevelPassed(levelState.SourceUid);
                
                if (IsPassedLevelHighest(levelState.SourceUid, currentLocation))
                {
                    _accountManager.Profile.UpdateLevelAndLocation(currentLocation.Uid, levelState.SourceUid);
                    var highestPassedLevelSortOrder = LevelHelper.GetLevelSortOrder(_config, levelState.SourceUid);
                    Analytics.SetUserProperty(AnalyticUserProperties.HighestPassedLevel, levelState.SourceUid);
                    Analytics.SetUserProperty(AnalyticUserProperties.HighestPassedLevelNum, highestPassedLevelSortOrder);
                }

                if (isStartedLevelPlay)
                {   
                    _weeklyLeaderboardManager.DecrementFailedAttempt();
                }

                var applyDoubleTrophy = _weeklyLeaderboardManager.IsMultiplierScoreStreakActive();
                var resultWeeklyTrophies = _weeklyLeaderboardManager.TryAddWeeklyTrophy(applyDoubleTrophy ? _weeklyLeaderboardManager.GetScoreMultiplier() : 1);


                var silentHandling = false;
                if (resultWeeklyTrophies > 0)
                {
                    var submitResult = _weeklyLeaderboardManager.TrySubmitToWeeklyLeaderboard();
                    if (submitResult)
                    {
                        //if submit rpc is called without problems, then we enable silent
                        //handling for UpdateLocalPlayerState, because the handling will be done by the rpc callback
                        silentHandling = true;   
                    }
                }

                _leaderboardManager.UpdateLocalPlayerState(new LocalPlayerUpdateData
                {
                    Trophies = _accountManager.Profile.Trophies,
                    LastLocation = _accountManager.Profile.LastUnlockedLocationId,
                    HighestLevel = _accountManager.Profile.HighestPassedLevelId,
                }, silentHandling);
            }

            if (_accountManager.Profile is { IsDirty: false }) return;
            
#if UNITY_EDITOR
            if (UnityEditor.EditorPrefs.GetBool("DontSave", false))
            {
                return;
            }
#endif
            
        }

        private static bool IsPassedLevelHighest(string levelUid, ILocation currentLocation)
        {
            var highestPassedLevelUid = currentLocation.GetHighestPassedLevelUid();
            return highestPassedLevelUid == levelUid;
        }

        public void Destroy()
        {
            Unsubscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();
            _locationManager.LevelPassed += OnLevelPassed;
            _locationManager.LocationsDataUpdated += RefreshHighestPassedLevel;
            _userSettings.NotificationsSettingsChanged += RefreshNotificationsEnabledSettings;
            
        }

        private void Unsubscribe()
        {
            _locationManager.LevelPassed -= OnLevelPassed;
            _locationManager.LocationsDataUpdated -= RefreshHighestPassedLevel;
            _userSettings.NotificationsSettingsChanged -= RefreshNotificationsEnabledSettings;
            
        }
    }
}