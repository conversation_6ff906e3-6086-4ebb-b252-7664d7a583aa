using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.MiniJSON;
using BebopBee.Social;
using GameAssets.Scripts.Database;
using GameAssets.Scripts.Database.Model;
using Newtonsoft.Json;
using PBGame;
using RPC.Teams;
using UnityEngine;

namespace BebopBee
{
    public class Profile : IPublicProfile
    {
        private const byte DetailedAnalyticsStateNotSet = 0;
        private const byte DetailedAnalyticsStateEnabled = 1;
        private const byte DetailedAnalyticsStateDisabled = 2;
        
        public ProfileDataModel ProfileDataModel { get; set; }

        public int Trophies
        {
            get => ProfileDataModel.Trophies;
            private set
            {
                DbManager.UpdateData<ProfileDataModel>((profileDataModel) =>
                {
                    profileDataModel.Trophies = value;
                });
            }
        }

        public bool IsDirty { get; set; }

        public string Uid
        {
            get => ProfileDataModel.Uid;
            set => UpdateProfileField(p => p.Uid, (p, v) => p.Uid = v, value);
        }

        public string Avatar
        {
            get => ProfileDataModel.Avatar;
            set => UpdateProfileField(p => p.Avatar, (p, v) => p.Avatar = v, value);
        }

        public string FacebookAvatar
        {
            get => ProfileDataModel.FacebookAvatar;
            set
            {
                DbManager.UpdateData<ProfileDataModel>(profileDataModel =>
                {
                    profileDataModel.FacebookAvatar = value;
                    if (ShouldCopyAvatarFromSocialNetwork(value))
                    {
                        profileDataModel.Avatar = value;
                    }
                });
                IsDirty = true;
            }
        }

        public string Name
        {
            get => DisplayName.IsNullOrEmpty() ? ProfileDataModel.Name : DisplayName;
            set
            {
                DbManager.UpdateData<ProfileDataModel>(profileDataModel =>
                {
                    profileDataModel.Name = value;
                });
                IsDirty = true;
            }
        }

        public string DisplayName
        {
            get => ProfileDataModel.DisplayName;
            set => UpdateProfileField(p => p.DisplayName, (p, v) => p.DisplayName = v, value);
        }

        public string Country
        {
            get => ProfileDataModel.Country;
            set => UpdateProfileField(p => p.Country, (p, v) => p.Country = v, value);
        }

        public string LastUnlockedLocationId
        {
            get => ProfileDataModel.LastUnlockedLocationId;
            set => UpdateProfileField(p => p.LastUnlockedLocationId, (p, v) => p.LastUnlockedLocationId = v, value);
        }

        public string HighestPassedLevelId
        {
            get => ProfileDataModel?.HighestPassedLevelId;
            set => UpdateProfileField(p => p.HighestPassedLevelId, (p, v) => p.HighestPassedLevelId = v, value);
        }

        public int LastActiveTimeStamp
        {
            get => ProfileDataModel.LastActiveTimeStamp;
            set => UpdateProfileField(p => p.LastActiveTimeStamp, (p, v) => p.LastActiveTimeStamp = v, value);
        }

        private TeamData _currentTeam;
        private bool _currentTeamChanged;
        public TeamData CurrentTeam
        {
            get
            {
                if (_currentTeamChanged || _currentTeam == null)
                {
                    _currentTeamChanged = false;
                    _currentTeam = ProfileDataModel?.TeamDataModel?.ToTeamData(); 
                }
                
                return _currentTeam?.TeamUid == null ? null : _currentTeam;
            }
            set 
            { 
                if(value is null && ProfileDataModel?.TeamDataModel is null) return;
                
                _currentTeam = value;
                _currentTeamChanged = true;
                var newTeamData = value is null ? null : new TeamDataModel(value);
                DbManager.UpdateData<ProfileDataModel>(profileDataModel =>
                {
                    profileDataModel.TeamDataModel = newTeamData;
                }); 
            }
        }

        public string Email => SignedEmail ?? FacebookEmail ?? AppleEmail ?? GoogleEmail ?? ProvidedEmail;

        public bool IsDisplayNameOverriden
        {
            get => ProfileDataModel.IsDisplayNameOverriden;
            set => UpdateProfileField(p => p.IsDisplayNameOverriden, (p, v) => p.IsDisplayNameOverriden = v, value);
        }
        
        public string FacebookName
        {
            get => ProfileDataModel.FacebookName;
            set
            {
                DbManager.UpdateData<ProfileDataModel>((profileDataModel) =>
                {
                    profileDataModel.FacebookName = value;
                    if(ShouldCopyDisplayNameFromSocialNetwork(value))
                    {
                        profileDataModel.DisplayName = value;
                    }
                });
                IsDirty = true;
            }
        }

        public string FacebookId
        {
            get => ProfileDataModel.FacebookId;
            set => UpdateProfileField(p => p.FacebookId, (p, v) => p.FacebookId = v, value);
        }

        public string FacebookEmail
        {
            get => ProfileDataModel.FacebookEmail;
            set => UpdateProfileField(p => p.FacebookEmail, (p, v) => p.FacebookEmail = v, value);
        }

        // To not override facebook credential email
        public string ProvidedEmail
        {
            get => ProfileDataModel.ProvidedEmail;
            set
            {
                DbManager.UpdateData<ProfileDataModel>((profileDataModel) =>
                {
                    profileDataModel.ProvidedEmail = value;
                });
                IsDirty = true;
            }
        }

        public string AppleId
        {
            get => ProfileDataModel.AppleId;
            set => UpdateProfileField(p => p.AppleId, (p, v) => p.AppleId = v, value);
        }

        public string AppleGivenName
        {
            get => ProfileDataModel.AppleGivenName;
            set
            {
                DbManager.UpdateData<ProfileDataModel>((profileDataModel) =>
                {
                    profileDataModel.AppleGivenName = value;
                    if(ShouldCopyDisplayNameFromSocialNetwork(value))
                    {
                        profileDataModel.DisplayName = value;
                    }
                });
                IsDirty = true;
            }
        }

        public string AppleFamilyName
        {
            get => ProfileDataModel.AppleFamilyName;
            set => UpdateProfileField(p => p.AppleFamilyName, (p, v) => p.AppleFamilyName = v, value);
        }

        public string AppleEmail
        {
            get => ProfileDataModel.AppleEmail;
            set => UpdateProfileField(p => p.AppleEmail, (p, v) => p.AppleEmail = v, value);
        }

        public string GoogleId
        {
            get => ProfileDataModel.GoogleId;
            set => UpdateProfileField(p => p.GoogleId, (p, v) => p.GoogleId = v, value);
        }

        public string GoogleEmail
        {
            get => ProfileDataModel.GoogleEmail;
            set => UpdateProfileField(p => p.GoogleEmail, (p, v) => p.GoogleEmail = v, value);
        }

        public string GoogleDisplayName
        {
            get => ProfileDataModel.GoogleDisplayName;
            set
            {
                DbManager.UpdateData<ProfileDataModel>((profileDataModel) =>
                {
                    profileDataModel.GoogleDisplayName = value;
                    if(ShouldCopyDisplayNameFromSocialNetwork(value))
                    {
                        profileDataModel.DisplayName = value;
                    }
                });
                IsDirty = true;
            }
        }

        public string GoogleAvatar
        {
            get => ProfileDataModel.GoogleAvatar;
            set
            {
                DbManager.UpdateData<ProfileDataModel>((profileDataModel) =>
                {
                    profileDataModel.GoogleAvatar = value;
                    if (ShouldCopyAvatarFromSocialNetwork(value))
                    {
                        profileDataModel.Avatar = value;
                    }
                });
                IsDirty = true;
            }
        }

        public string PhoneNumber
        {
            get => ProfileDataModel.PhoneNumber;
            set => UpdateProfileField(p => p.PhoneNumber, (p, v) => p.PhoneNumber = v, value);
        }

        public int CurrentLevelStage
        {
            get => ProfileDataModel.CurrentLevelStage;
            set => UpdateProfileField(p => p.CurrentLevelStage, (p, v) => p.CurrentLevelStage = v, value);
        }

        public bool NotificationsEnabled
        {
            get => ProfileDataModel.NotificationsEnabled;
            set => UpdateProfileField(p => p.NotificationsEnabled, (p, v) => p.NotificationsEnabled = v, value);
        }

        public int HelpCount
        {
            get => ProfileDataModel.HelpCount;
            set => UpdateProfileField(p => p.HelpCount, (p, v) => p.HelpCount = v, value);
        }

        public bool SdbEnabled
        {
            get => ProfileDataModel.SdbEnabled;
            set => UpdateProfileField(p => p.SdbEnabled, (p, v) => p.SdbEnabled = v, value);
        }
        
        public bool ChallengesEnabled
        {
            get => ProfileDataModel.ChallengesEnabled;
            set => UpdateProfileField(p => p.ChallengesEnabled, (p, v) => p.ChallengesEnabled = v, value);
        }
        
        private byte DetailedAnalyticsState
        {
            get => ProfileDataModel.DetailedAnalyticsState;
            set => UpdateProfileField(p => p.DetailedAnalyticsState, (p, v) => p.DetailedAnalyticsState = v, value);
        }
        
        public bool DetailedAnalyticsStateSet => ProfileDataModel.DetailedAnalyticsState != DetailedAnalyticsStateNotSet;
        public bool DetailedAnalyticsEnabled
        {
            get => DetailedAnalyticsState == DetailedAnalyticsStateEnabled;
            set => DetailedAnalyticsState = value ? DetailedAnalyticsStateEnabled : DetailedAnalyticsStateDisabled;
        }

        public string SignedDisplayName
        {
            get => ProfileDataModel.SignedDisplayName;
            set => UpdateProfileField(p => p.SignedDisplayName, (p, v) => p.SignedDisplayName = v, value);
        }

        public string SignedAvatar
        {
            get => ProfileDataModel.SignedAvatar;
            set => UpdateProfileField(p => p.SignedAvatar, (p, v) => p.SignedAvatar = v, value);
        }
        
        public string SignedEmail
        {
            get => ProfileDataModel.SignedEmail;
            set => UpdateProfileField(p => p.SignedEmail, (p, v) => p.SignedEmail = v, value);
        }

        // New properties from BCSummaryFriendData
        public double? InstallDate
        {
            get => ProfileDataModel.InstallDate;
            set => UpdateProfileField(p => p.InstallDate, (p, v) => p.InstallDate = v, value);
        }

        public int? FirstTryWins
        {
            get => ProfileDataModel.FirstTryWins;
            set => UpdateProfileField(p => p.FirstTryWins, (p, v) => p.FirstTryWins = v, value);
        }

        public int? HelpsReceived
        {
            get => ProfileDataModel.HelpsReceived;
            set => UpdateProfileField(p => p.HelpsReceived, (p, v) => p.HelpsReceived = v, value);
        }

        public int? ScenesCompleted
        {
            get => ProfileDataModel.ScenesCompleted;
            set => UpdateProfileField(p => p.ScenesCompleted, (p, v) => p.ScenesCompleted = v, value);
        }

        public int? SetsCompleted
        {
            get => ProfileDataModel.SetsCompleted;
            set => UpdateProfileField(p => p.SetsCompleted, (p, v) => p.SetsCompleted = v, value);
        }

        public int? LeaguesWon
        {
            get => ProfileDataModel.LeaguesWon;
            set => UpdateProfileField(p => p.LeaguesWon, (p, v) => p.LeaguesWon = v, value);
        }

        public bool? IsBot => ProfileDataModel.IsBot;

        public Dictionary<string, object> Serialize(string userId = null)
        {
            var profileData = new Dictionary<string, object>
            {
                ["uid"] = userId.IsNullOrEmpty() ? Uid : userId,
                ["avatar"] = Avatar,
                ["name"] = Name,
                ["display_name"] = DisplayName,
                ["last_unlocked_location_id"] = LastUnlockedLocationId,
                ["highest_passed_level_id"] = HighestPassedLevelId,
                ["new_trophies"] = Trophies,
                ["fb_name"] = FacebookName,
                ["fb_id"] = FacebookId,
                ["email"] = FacebookEmail,
                ["fb_avatar"] = FacebookAvatar,
                ["provided_email"] = ProvidedEmail,
                ["phone_number"] = PhoneNumber,
                ["current_level_stage"] = CurrentLevelStage,
                ["notifications_enabled"] = NotificationsEnabled,
                ["apple_id"] = AppleId,
                ["apple_email"] = AppleEmail,
                ["apple_given_name"] = AppleGivenName,
                ["apple_family_name"] = AppleFamilyName,
                ["google_id"] = GoogleId,
                ["google_email"] = GoogleEmail,
                ["google_display_name"] = GoogleDisplayName,
                ["google_avatar"] = GoogleAvatar,
                ["is_display_name_overridden"] = IsDisplayNameOverriden,
                ["current_team"] = JsonConvert.SerializeObject(CurrentTeam),
                ["help_count"] = HelpCount,
                ["signed_display_name"] = SignedDisplayName,
                ["signed_avatar"] = SignedAvatar,
                ["signed_email"] = SignedEmail,
                ["admin_sdb_enabled"] = SdbEnabled,
                ["challenges_enabled"] = ChallengesEnabled,
                // New fields from BCSummaryFriendData
                ["install_date"] = InstallDate,
                ["first_try_wins"] = FirstTryWins,
                ["helps_received"] = HelpsReceived,
                ["scenes_completed"] = ScenesCompleted,
                ["sets_completed"] = SetsCompleted,
                ["leagues_won"] = LeaguesWon,
                ["is_bot"] = IsBot
            };

            return profileData;
        }

#if UNITY_EDITOR
        public Dictionary<string, object> SerializeForEditor(string id)
        {
            var profileData = new Dictionary<string, object>
            {
                ["uid"] = id,
                ["avatar"] = string.Empty,
                ["name"] = string.Empty,
                ["display_name"] = string.Empty,
                ["last_unlocked_location_id"] = string.Empty,
                ["highest_passed_level_id"] = string.Empty,
                ["last_level_passed_id"] = string.Empty,
                ["new_trophies"] = 0,
                ["fb_name"] = string.Empty,
                ["fb_id"] = string.Empty,
                ["fb_avatar"] = string.Empty,
                ["email"] = string.Empty,
                ["provided_email"] = string.Empty,
                ["phone_number"] = string.Empty,
                ["current_level_id"] = string.Empty,
                ["current_level_stage"] = 0,
                ["notifications_enabled"] = false,
                ["apple_id"] = string.Empty,
                ["apple_email"] = string.Empty,
                ["apple_given_name"] = string.Empty,
                ["apple_family_name"] = string.Empty,
                ["google_id"] = string.Empty,
                ["google_email"] = string.Empty,
                ["google_display_name"] = string.Empty,
                ["google_avatar"] = string.Empty,
                ["is_display_name_overridden"] = false,
                ["current_team"] = JsonConvert.SerializeObject(CurrentTeam),
                ["help_count"] = 0,
                ["signed_display_name"] = string.Empty,
                ["signed_avatar"] = string.Empty,
                ["signed_email"] = string.Empty,
                ["challenges_enabled"] = false,
                // New fields from BCSummaryFriendData
                ["install_date"] = null,
                ["team_name"] = string.Empty,
                ["team_icon"] = string.Empty,
                ["first_try_wins"] = null,
                ["helps_received"] = null,
                ["scenes_completed"] = null,
                ["sets_completed"] = null,
                ["leagues_won"] = null,
                ["is_bot"] = false
            };

            return profileData;
        }
#endif
        public void AddTrophyAndSetLevelPassed(string levelPassed)
        {
            DbManager.UpdateData<ProfileDataModel>(profileData =>
            {
                profileData.HighestPassedLevelId = levelPassed;
                profileData.Trophies += 1;
            });
            IsDirty = true;
        }
        
        public void SetTrophies(int trophies, int maxProgressionLevel)
        {
            if (Trophies == trophies) return;
            
            if (Trophies > 0 && trophies < Trophies)
            { 
                LogTrophiesDown(trophies, maxProgressionLevel);
            }

            Trophies = trophies;
            IsDirty = true;
        }
        
        public void UpdateProfileFromGameState(PBPlayer pbPlayer, LocationManager locationManager, int maxProgressionLevel)
        {
            if (pbPlayer?.LevelStates == null || pbPlayer.LevelStates.Count == 0)
            {
                BDebug.LogError(LogCat.General, "UpdateProfile: pb state is empty!");
                return;
            }

            SetTrophies(GetTrophiesBasedOnLevelStates(pbPlayer), maxProgressionLevel);
            SetHighestLevelData(locationManager);
        }

        private int GetTrophiesBasedOnLevelStates(PBPlayer pbPlayer)
        {
            var trophies = 0;
            foreach (var levelState in pbPlayer.LevelStates)
            {
                trophies += levelState.Stage;
            }

            return trophies;
        }

        public void UpdateTrophies(IPlayer player)
        {
            if(player == null) 
                return;
            
            Trophies = GetTrophiesBasedOnLevelStates(player.PlayerDO);
            IsDirty = true;
        }

        private void SetHighestLevelData(LocationManager locationManager)
        {
            var highestLevelUid = locationManager.MainProgressionLocation.GetHighestPassedLevelUid();
#if BBB_DEBUG
            if (HighestPassedLevelId != highestLevelUid)
            {
                Debug.Log($"UpdateProfile: Change last level from {HighestPassedLevelId} to {highestLevelUid}");
            }
#endif
            
            if (highestLevelUid.IsNullOrEmpty()) return;

            DbManager.UpdateData<ProfileDataModel>(profileDataModel =>
            {
                if (!highestLevelUid.IsNullOrEmpty())
                {
                    profileDataModel.HighestPassedLevelId = highestLevelUid;
                }
            });
        }
        
        public void UpdateFromBrainCloudResponse(PlayerAuthData authData)
        {
            // We need to update country code even for new user, since otherwise profile country will be null in first session.
            Country = authData.CountryCode ?? PlatformUtil.GetCurrentCountryCode();
            
            // Don't update local profile if this is a new user, since data in the response will not be up to date
            // (we have just sent the correct user info in the Authentication call)
            if ("true".Equals(authData.NewUser)) return;
            
            var summaryFriendData = authData.SummaryFriendData;
            DbManager.UpdateData<ProfileDataModel>(profileDataModel =>
            {
                var newUid = summaryFriendData?.Uid;
                profileDataModel.Uid = newUid.IsNullOrEmpty() ? MultiDevice.GetUserId() : newUid;
                profileDataModel.Name = authData.Name;
                profileDataModel.Avatar = authData.Avatar;
                profileDataModel.DisplayName = authData.DisplayName;
                
                profileDataModel.HighestPassedLevelId = summaryFriendData?.HighestPassedLevelId ?? HighestPassedLevelId;
                profileDataModel.LastUnlockedLocationId = summaryFriendData?.LastUnlockedLocationId ?? LastUnlockedLocationId;
                profileDataModel.Trophies = summaryFriendData?.Trophies ?? Trophies;
                profileDataModel.HelpCount = summaryFriendData?.HelpCount ?? HelpCount;
                profileDataModel.ProvidedEmail = summaryFriendData?.ProvidedEmail ?? ProvidedEmail;
                
                // Update new fields from BCSummaryFriendData
                profileDataModel.InstallDate = summaryFriendData?.InstallDate ?? InstallDate;
                profileDataModel.FirstTryWins = summaryFriendData?.FirstTryWins ?? FirstTryWins;
                profileDataModel.HelpsReceived = summaryFriendData?.HelpsReceived ?? HelpsReceived;
                profileDataModel.ScenesCompleted = summaryFriendData?.ScenesCompleted ?? ScenesCompleted;
                profileDataModel.SetsCompleted = summaryFriendData?.SetsCompleted ?? SetsCompleted;
                profileDataModel.LeaguesWon = summaryFriendData?.LeaguesWon ?? LeaguesWon;
                profileDataModel.IsBot = false;
            });
        }
        
        public string ToJson()
        {
            var serialized = Serialize();
            return Json.Serialize(serialized);
        }
        
        private bool ShouldCopyAvatarFromSocialNetwork(string avatar)
        {
            return !avatar.IsNullOrEmpty() &&
                   (Avatar.IsNullOrEmpty() || GenericResourceProvider.IsDefaultAvatar(Avatar));
        }
        
        private bool ShouldCopyDisplayNameFromSocialNetwork(string displayName)
        {
            // if was overriden manually - no need to change it anymore
            return !IsDisplayNameOverriden && !displayName.IsNullOrEmpty();
        }

        public void ChangeNameAndAvatar(string avatar, string displayName)
        {
            if (ProfileDataModel.Avatar == avatar && ProfileDataModel.DisplayName == displayName)
                return;
            
            DbManager.UpdateData<ProfileDataModel>(profileDataModel =>
            {
                profileDataModel.Avatar = avatar;
                profileDataModel.DisplayName = displayName;
                profileDataModel.IsDisplayNameOverriden = true;
            });
            IsDirty = true;
        }
        
        public void ChangeDisplayName(string displayName)
        {
            if (ProfileDataModel.DisplayName == displayName)
                return;
            
            DbManager.UpdateData<ProfileDataModel>(profileDataModel =>
            {
                profileDataModel.DisplayName = displayName;
                profileDataModel.IsDisplayNameOverriden = true;
            });
            IsDirty = true;
        }

        public void UpdateLevelAndLocation(string lastUnlockedLocationId, string highestPassedLevelId)
        {
            if (ProfileDataModel.LastUnlockedLocationId == lastUnlockedLocationId && ProfileDataModel.HighestPassedLevelId == highestPassedLevelId)
                return;
            
            DbManager.UpdateData<ProfileDataModel>(profileDataModel =>
            {
                profileDataModel.LastUnlockedLocationId = lastUnlockedLocationId;
                profileDataModel.HighestPassedLevelId = highestPassedLevelId;
            });
            
            IsDirty = true;
        }

        public void UpdateEmailAndPhoneNumber(string email, string phoneNumber)
        {
            if ((email.IsNullOrEmpty() && phoneNumber.IsNullOrEmpty()) || (ProfileDataModel.ProvidedEmail == email && ProfileDataModel.PhoneNumber == phoneNumber))
                return;
            
            DbManager.UpdateData<ProfileDataModel>(profileDataModel =>
            {
                if (!email.IsNullOrEmpty())
                {
                    profileDataModel.ProvidedEmail = email;
                }

                if (!phoneNumber.IsNullOrEmpty())
                {
                    profileDataModel.PhoneNumber = phoneNumber;
                }
            });
            
            IsDirty = true;
        }

        public void UpdateAppleInfo(string id = "", string familyName = "", string givenName = "", string email = "")
        {
            if (ProfileDataModel.AppleId == id && ProfileDataModel.AppleFamilyName == familyName &&
                ProfileDataModel.AppleGivenName == givenName && ProfileDataModel.AppleEmail == email)
                return;
            
            DbManager.UpdateData<ProfileDataModel>(profileDataModel =>
            {
                profileDataModel.AppleId = id;
                profileDataModel.AppleFamilyName = familyName;
                profileDataModel.AppleGivenName = givenName;
                profileDataModel.AppleEmail = email;
                if (ShouldCopyDisplayNameFromSocialNetwork(givenName))
                {
                    profileDataModel.DisplayName = givenName;
                    profileDataModel.IsDisplayNameOverriden = true;
                }
            });
            
            IsDirty = true;
        }

        public void UpdateGoogleInfo(string id = "", string displayName = "", string avatar = "", string email = "")
        {
            if (ProfileDataModel.GoogleId == id && ProfileDataModel.GoogleDisplayName == displayName &&
                ProfileDataModel.GoogleAvatar == avatar && ProfileDataModel.GoogleEmail == email)
                return;
            
            DbManager.UpdateData<ProfileDataModel>(profileDataModel =>
            {
                profileDataModel.GoogleId = id;
                profileDataModel.GoogleDisplayName = displayName;
                profileDataModel.GoogleAvatar = avatar;
                profileDataModel.GoogleEmail = email;
                if (ShouldCopyDisplayNameFromSocialNetwork(displayName))
                {
                    profileDataModel.DisplayName = displayName;
                    profileDataModel.IsDisplayNameOverriden = true;
                }
                if (ShouldCopyAvatarFromSocialNetwork(avatar))
                {
                    profileDataModel.Avatar = avatar;
                }
            });
            
            IsDirty = true;
        }
        
        public void UpdateFacebookInfo(string id = "", string name = "", string avatar = "", string email = "")
        {
            if (ProfileDataModel.FacebookId == id && ProfileDataModel.FacebookName == name &&
                ProfileDataModel.FacebookAvatar == avatar && ProfileDataModel.FacebookEmail == email)
                return;
            
            DbManager.UpdateData<ProfileDataModel>(profileDataModel =>
            {
                profileDataModel.FacebookId = id;
                profileDataModel.FacebookName = name;
                profileDataModel.FacebookAvatar = avatar;
                profileDataModel.FacebookEmail = email;
                if (ShouldCopyDisplayNameFromSocialNetwork(name))
                {
                    profileDataModel.DisplayName = name;
                    profileDataModel.IsDisplayNameOverriden = true;
                }
                if (ShouldCopyAvatarFromSocialNetwork(avatar))
                {
                    profileDataModel.Avatar = avatar;
                }
            });
            
            IsDirty = true;
        }
        
        public void UpdateSignedInfo(string name, string avatar, string email)
        {
            if(ProfileDataModel.SignedDisplayName == name && ProfileDataModel.SignedAvatar == avatar && ProfileDataModel.SignedEmail == email)
                return;
            
            DbManager.UpdateData<ProfileDataModel>(profileDataModel =>
            {
                profileDataModel.SignedDisplayName = name;
                profileDataModel.SignedAvatar = avatar;
                profileDataModel.SignedEmail = email;
            });
            
            IsDirty = true;
        }

        private void LogTrophiesDown(int newTrophies, int maxProgressionLevel)
        {
            var message = $"Trophies down to {newTrophies} from:{Trophies}";
            
            if (AppDefinesConverter.BbbDebug || newTrophies == maxProgressionLevel)
            {
                BDebug.LogWarning(LogCat.General, message);
            }
            else
            {
                BDebug.LogError(LogCat.General, message);  
            }
        }
        
        private void UpdateProfileField<T>(Func<ProfileDataModel, T> getter, Action<ProfileDataModel, T> setter, T newValue)
        {
            if (EqualityComparer<T>.Default.Equals(getter(ProfileDataModel), newValue))
                return;

            DbManager.UpdateData<ProfileDataModel>(profile =>
            {
                setter(profile, newValue);
            });

            IsDirty = true;
        }
    }
}