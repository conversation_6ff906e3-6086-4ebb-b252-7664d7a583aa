using System;
using BBB;
using BBB.Controller;
using BBB.Core;
using BBB.Core.Crash;
using BBB.DI;
using BebopBee.Social;
using BBB.Social.SignIn;
using GameAssets.Scripts.Core;
using UnityEngine;
using UnityEngine.Profiling;

namespace BebopBee
{
    public class LoginGrimesCommand : CommandBase
    {
        public LoginGrimesCommand()
        {
            CommandCode = CommandCode.LoginGrimesCommand;
        }

        protected override void CommandExecutionStart(IContext context)
        {
            base.CommandExecutionStart(context);

            var accountManager = context.Resolve<IAccountManager>();
            Profiler.BeginSample("LoginGrimesCommand");
            accountManager.LoginGrimes();
            Profiler.EndSample();
            CurrentStatus = CommandStatus.Success;
            base.Execute(context);//trigger event
        }
    }

    public class SendLoginGrimesCommand : CommandBase
    {
        private readonly bool _applyChangesImmediately = false;
        public SendLoginGrimesCommand(bool applyChangesImmediately = false)
        {
            _applyChangesImmediately = applyChangesImmediately;
            CommandCode = CommandCode.LoginGrimesCommand;
        }

        protected override void CommandExecutionStart(IContext context)
        {
            base.CommandExecutionStart(context);

            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                CurrentStatus = CommandStatus.Failure;
                StatusMessage = "Network not reachable.";
                return;
            }

            var accountManager = context.Resolve<IAccountManager>();
            accountManager.SendLoginCommand(_applyChangesImmediately, success =>
            {
                if (success)
                {
                    // Since we are already in the screen transition, we need to refresh the user
                    accountManager.TryUpdatePlayer(() => {
                        CurrentStatus = CommandStatus.Success;
                    });
                }
                else
                {
                    CurrentStatus = CommandStatus.Failure;
                    StatusMessage = "Login failed.";
                }
            });

        }
    }

    public class CreateScreenCommand : CommandBase
    {
        private Func<IController> _createScreenOperation;
        private IController _screenController;

        public CreateScreenCommand(Func<IController> createScreenOperation)
        {
            _createScreenOperation = createScreenOperation;
            CommandCode = CommandCode.CreateScreenCommand;
        }

        public IController GetController()
        {
            return _screenController;
        }

        protected override void CommandExecutionStart(IContext context)
        {
            Profiler.BeginSample("CreateScreenCommand");
            _screenController = _createScreenOperation();
            Profiler.EndSample();
            CurrentStatus = CommandStatus.Success;
            base.Execute(context);//trigger event
        }

        public void Unsubscribe()
        {
            _screenController?.DisposeContext();
            _screenController = null;
            _createScreenOperation = null;
        }
    }

    public class RestartGameCommand : CommandBase
    {
        private IScreensManager _screenManager;

        public RestartGameCommand()
        {
            MaxExecutionTimeInternal = 300f;
            CommandCode = CommandCode.RestartCommand;
        }

        protected override void CommandExecutionStart(IContext context)
        {
            CrashLoggerService.Log("Restart command execution");
            var restarter = context.Resolve<IRestartable>();
            restarter.Restart();
            Time.timeScale = 1;
        }
    }
}