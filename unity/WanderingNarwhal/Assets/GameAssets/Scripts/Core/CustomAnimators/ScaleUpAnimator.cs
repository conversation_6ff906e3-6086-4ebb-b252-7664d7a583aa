using BBB;
using DG.Tweening;
using UnityEngine;

namespace GameAssets.Scripts.Core.CustomAnimators
{
    public class ScaleUpAnimator : BbbMonoBehaviour
    {
        [SerializeField] private float _forthDuration = 0.5f;
        [SerializeField] private AnimationCurve _forthAnimationCurve = 
            AnimationCurve.Linear(0f, 0f, 1f, 1f);
        
        [SerializeField] private float _backDuration = 0.5f;
        [SerializeField] private AnimationCurve _backAnimationCurve = 
            AnimationCurve.Linear(0f, 0f, 1f, 1f);
        
        [SerializeField] private Vector3 _targetScale = new (1,1,1);
        [SerializeField] private Vector3 _startScale = new (0,0,0);
        [SerializeField] private bool _playForthOnEnable = false;

        public bool LastPlayedForth { get; private set; }
        
        private Transform _selfTransform;

        private Tween _tween;
        
        private Transform SelfTransform
        {
            get
            {
                if (_selfTransform == null)
                {
                    _selfTransform = transform;
                }
                
                return _selfTransform;
            }
        }

        protected override void OnEnable()
        {
            if (_playForthOnEnable)
            {
                PlayForth_Internal();
            }
        }

        public void PlayForth()
        {
            PlayForth_Internal();
        }

        public void PlayBack()
        {
            PlayBack_Internal();
        }

        public void SetStartScaleInstantly()
        {
            SelfTransform.localScale = _startScale;
        }

        public void SetTargetScaleInstantly()
        {
            SelfTransform.localScale = _targetScale;
        }

        private void PlayForth_Internal()
        {
            LastPlayedForth = true;
            _tween?.Kill();
            _tween = SelfTransform.DOScale(_targetScale, _forthDuration)
                .SetEase(_forthAnimationCurve).OnComplete(() =>
                {
                    _tween = null;
                });
        }

        private void PlayBack_Internal()
        {
            LastPlayedForth = false;
            _tween?.Kill();
            _tween = SelfTransform.DOScale(_startScale, _backDuration).
                SetEase(_backAnimationCurve).OnComplete(() =>
            {
                _tween = null;
            });
        }
    }
}