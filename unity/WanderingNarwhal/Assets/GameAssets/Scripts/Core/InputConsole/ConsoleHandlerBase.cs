namespace BBB.InputConsole
{
    public abstract class ConsoleHandlerBase
    {
        public abstract string CommandName { get; }
        public abstract string[] Aliases { get; }
        public abstract string Handle(string argument, InputConsoleView caller);

        public virtual int WordsCountAllowed
        {
            get { return 2; }
        }

        protected bool IsValidArgument(string argument, out string reason, out int spacesCount)
        {
            spacesCount = 0;
            reason = null;
            if (string.IsNullOrEmpty(argument))
            {
                reason = $"{CommandName} got null or empty argument";
                return false;
            }

            var spacesAllowed = WordsCountAllowed - 1;

            foreach(var ch in argument)
                if (ch == ' ')
                    spacesCount++;

            if (spacesCount > spacesAllowed)
            {
                reason = $"{CommandName} got wrong argument {argument}, more than {WordsCountAllowed} words";
                return false;
            }

            return true;
        }
    }
}