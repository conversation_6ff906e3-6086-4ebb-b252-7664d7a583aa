using System;
using BBB.Core;
using BBB.DI;
using BBB.Generic.Modal;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core;

namespace BBB
{
    public class HelpDeskManager : IHelpDeskManager, IContextReleasable
    {
        private readonly IHelpDeskImpl _helpDeskImpl;
        private GenericHudManager _genericHudManager;
        private GenericModalFactory _genericModalFactory;

        public HelpDeskManager(IHelpDeskImpl helpDeskImpl)
        {
            if (helpDeskImpl == null)
                BDebug.LogError(LogCat.HelpDesk, "Hep desk implementation is null");

            _helpDeskImpl = helpDeskImpl;
        }

        public void Init(IContext context)
        {
            _genericHudManager = context.Resolve<GenericHudManager>();
            _genericModalFactory = context.Resolve<GenericModalFactory>();
            _helpDeskImpl.Initialize(context);
            
            BDebug.Log(LogCat.HelpDesk, "HelpDeskManager initialized");
        }

        public void SetUnreadCountChangedHandler(Action<int> onUnreadCountChanged)
        {
            _helpDeskImpl.SetUnreadCountChangedHandler(onUnreadCountChanged);
        }

        public async UniTask ShowHelpCenter()
        {
            if (ConnectivityStatusManager.ConnectivityReachable)
            {
                _genericHudManager.SetLoadingViewVisibility(true);
                await _helpDeskImpl.ShowHelpCenter();
                _genericHudManager.SetLoadingViewVisibility(false);
            }
            else
            {
                _genericModalFactory.ShowNoConnectionModal();
            }
        }

        public async UniTask ShowTicketOrHelp()
        {
            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                _genericModalFactory.ShowNoConnectionModal();
                return;
            }
            
            if (await HasNewTicketResponse())
            {
                await ShowAllMyTickets();
            }
            else
            {
                await ShowHelpCenter();
            }
        }
        
        public async UniTask ShowAllMyTickets()
        {
            if (ConnectivityStatusManager.ConnectivityReachable)
            {
                _genericHudManager.SetLoadingViewVisibility(true);
                await _helpDeskImpl.ShowRequestList();
                _genericHudManager.SetLoadingViewVisibility(false);
            }
            else
            {
                _genericModalFactory.ShowNoConnectionModal();
            }
        }

        public async UniTask<bool> HasNewTicketResponse(Action<int> onUnreadCountChanged = null)
        {
            return await _helpDeskImpl.HasUnreadMessage(onUnreadCountChanged);
        }

        public async UniTask<int> GetUnreadCountAsync(Action<int> onUnreadCountChanged)
        {
            return await _helpDeskImpl.GetUnreadCountAsync(onUnreadCountChanged);
        }

        public void ReleaseByContext(IContext context)
        {
            _genericHudManager = null;
            _genericModalFactory = null;
        }

        public void Restart(bool logout)
        {
            _helpDeskImpl.Restart(logout);
        }
    }
}