using BBB.DI;
using Core.RPC;
using GameAssets.Scripts.Core;
using UnityEngine;

namespace BBB
{
	/// <summary>
	/// Component for listenning for internet connection errors, which will then wait for reconnect and force reload game.
	/// </summary>
	public class AppRestartOnRPCErrorDaemon : BbbMonoBehaviour
	{
		private const float CHECK_DURATION = 3f;

		/// <summary>
		/// Checking timer.
		/// </summary>
		/// <remarks>
		/// default value is negative to make first check later.
		/// </remarks>
		private float _timer = -5f;

		private IRPCService _rpcService;
		private IRpcComm _rpcComm;
		private IRestartable _restartable;

		private void Start()
		{
			// We need updates only if errors occured.
			enabled = false;
		}

		public void Init(IContext context, IRestartable appController)
		{
			_rpcService = context.Resolve<IRPCService>();
			_rpcComm = context.Resolve<IRpcComm>();
			_restartable = appController;
			if (_restartable == null)
			{
				throw new System.NullReferenceException();
			}

			_rpcService.OnConnectionErrorDetails -= OnError;
			_rpcService.OnConnectionErrorDetails += OnError;
		}

		private void OnError(string error)
		{
			// Start checking for internet connection;
			enabled = true;
		}

		private void Update()
		{
			_timer += Time.unscaledDeltaTime;
            if (_timer < CHECK_DURATION) return;
            
            _timer = 0f;
            if (ConnectivityStatusManager.ConnectivityReachable)
            {
                ForceRestartAll();
            }
        }

		[ContextMenu("Force reload all")]
		private void ForceRestartAll()
		{
			_restartable.Restart();
		}

		protected override void OnDestroy()
		{
			base.OnDestroy();
            
			if (_rpcService != null)
			{
				_rpcService.OnConnectionErrorDetails -= OnError;
			}
		}
	}
}