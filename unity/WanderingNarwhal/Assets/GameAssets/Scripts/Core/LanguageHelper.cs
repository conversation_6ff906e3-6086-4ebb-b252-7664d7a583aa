using UnityEngine;
using System.Collections.Generic;

public class LanguageHelper
{

    /// <summary>
    /// Helps to convert Unity's Application.systemLanguage to a
    /// 2 letter ISO country code. There is unfortunately not more
    /// countries available as Unity's enum does not enclose all
    /// countries.
    /// </summary>
    /// <returns>The 2-letter ISO code from system language.</returns>

    private static Dictionary<SystemLanguage, string> _rllangs;

    private static Dictionary<SystemLanguage, string> _rlLanguages
    {
        get
        {
            CheckLanguagesDictionary();
            return _rllangs;
        }
    }

    private static void InitLanguagesDictionary()
    {
        _rllangs = new Dictionary<SystemLanguage, string>();

        _rllangs.Add(SystemLanguage.English, "EN");
        _rllangs.Add(SystemLanguage.French, "FR");
        _rllangs.Add(SystemLanguage.Spanish, "ES");
        _rllangs.Add(SystemLanguage.German, "DE");
        _rllangs.Add(SystemLanguage.Portuguese, "PT");
        _rllangs.Add(SystemLanguage.ChineseSimplified, "ZHS");
//        _rllangs.Add(SystemLanguage.ChineseTraditional, "ZHT");
        _rllangs.Add(SystemLanguage.Italian, "IT");
        _rllangs.Add(SystemLanguage.Russian, "RU");
        _rllangs.Add(SystemLanguage.Ukrainian, "UA");
        _rllangs.Add(SystemLanguage.Japanese, "JA");
        _rllangs.Add(SystemLanguage.Korean, "KO");
        _rllangs.Add(SystemLanguage.Polish, "PL");
    }

    public static Dictionary<SystemLanguage, string> GetRLLanguages()
    {
        return _rlLanguages;
    }

    public static string GetSystemLanguageTextConfigName()
    {
        return GetTextConfigName(GetISOCodeFromSystemLanguageEx());
    }

    public static string GetTextConfigName(string languageIsoCode)
    {
        return $"FBTextConfig-{languageIsoCode}";
    }

    public static void CheckLanguagesDictionary()
    {
        if (_rllangs == null) InitLanguagesDictionary();
    }

	// Ex version handles the distinction between chinese simplified and chinese traditional.
    public static string GetISOCodeFromSystemLanguageEx()
    {
        var strLanguage = PlayerPrefs.GetInt("CurrentLanguage");
        var currentLanguage = strLanguage == 0 ? Application.systemLanguage : (SystemLanguage)strLanguage;
        string result = "";

        if (_rlLanguages.ContainsKey(currentLanguage))
        {
            result = _rlLanguages[currentLanguage];
        }
        else
        {
            result = "EN";
        }

        var code = GetISOCodeFromSystemLanguage();
        if (!string.IsNullOrEmpty(code))
        {
            result = code;
        }
        return result;
    }
	
    public static string GetISOCodeFromSystemLanguage()
    {
        var currentLanguage = (SystemLanguage)PlayerPrefs.GetInt("CurrentLanguage", (int)SystemLanguage.Unknown);
        currentLanguage = currentLanguage == SystemLanguage.Unknown ? Application.systemLanguage : currentLanguage;
        return GetISOCodeFromSystemLanguage(currentLanguage);
    }

    public static string GetISOCodeFromSystemLanguage(SystemLanguage lang)
    {
        string res = "";
        switch (lang)
        {
            // case SystemLanguage.Afrikaans: res = "AF"; break;
            // case SystemLanguage.Arabic: res = "AR"; break;
            // case SystemLanguage.Basque: res = "EU"; break;
            // case SystemLanguage.Belarusian: res = "BY"; break;
            // case SystemLanguage.Bulgarian: res = "BG"; break;
            // case SystemLanguage.Catalan: res = "CA"; break;
            case SystemLanguage.ChineseSimplified: res = "ZHS"; break;
            case SystemLanguage.ChineseTraditional: res = "ZHS"; break;
            case SystemLanguage.Chinese: res = "ZHS"; break;
            // case SystemLanguage.Czech: res = "CS"; break;
            // case SystemLanguage.Danish: res = "DA"; break;
            // case SystemLanguage.Dutch: res = "NL"; break;
            case SystemLanguage.English: res = "EN"; break;
            // case SystemLanguage.Estonian: res = "ET"; break;
            // case SystemLanguage.Faroese: res = "FO"; break;
            // case SystemLanguage.Finnish: res = "FI"; break;
            case SystemLanguage.French: res = "FR"; break;
            case SystemLanguage.German: res = "DE"; break;
            // case SystemLanguage.Greek: res = "EL"; break;
            // case SystemLanguage.Hebrew: res = "IW"; break;
            // case SystemLanguage.Hugarian: res = "HU"; break;
            // case SystemLanguage.Icelandic: res = "IS"; break;
            // case SystemLanguage.Indonesian: res = "IN"; break;
            case SystemLanguage.Italian: res = "IT"; break;
            case SystemLanguage.Japanese: res = "JA"; break;
            case SystemLanguage.Korean: res = "KO"; break;
            // case SystemLanguage.Latvian: res = "LV"; break;
            // case SystemLanguage.Lithuanian: res = "LT"; break;
            // case SystemLanguage.Norwegian: res = "NO"; break;
            // case SystemLanguage.Polish: res = "PL"; break;
            case SystemLanguage.Portuguese: res = "PT"; break;
            // case SystemLanguage.Romanian: res = "RO"; break;
            case SystemLanguage.Russian: res = "RU"; break;
            // case SystemLanguage.SerboCroatian: res = "SH"; break;
            // case SystemLanguage.Slovak: res = "SK"; break;
            // case SystemLanguage.Slovenian: res = "SL"; break;
            case SystemLanguage.Spanish: res = "ES"; break;
                // case SystemLanguage.Swedish: res = "SV"; break;
                // case SystemLanguage.Thai: res = "TH"; break;
                // case SystemLanguage.Turkish: res = "TR"; break;
            case SystemLanguage.Ukrainian: res = "UA"; break;
                // case SystemLanguage.Unknown: res = "EN"; break;
                // case SystemLanguage.Vietnamese: res = "VI"; break;
        }
        return res;
    }
}
