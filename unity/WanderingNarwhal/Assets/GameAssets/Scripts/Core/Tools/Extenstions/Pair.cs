namespace BBB.Core.Tools.FSM.Extenstions
{
    public class Pair<TFirst, TSecond>
    {
        public readonly TFirst First;
        public readonly TSecond Second;

        public Pair(TFirst first, TSecond second)
        {
            First = first;
            Second = second;
        }
    }

    public class CyclicList<TItem>
    {
        private readonly TItem[] _array;

        private int _counter;

        public CyclicList(TItem[] array)
        {
            _array = array;
        }

        public TItem GetNextItem()
        {
            var index = _counter < _array.Length ? _counter : (_counter = 0);
            _counter++;
            return _array[index];
        }

    }
}
