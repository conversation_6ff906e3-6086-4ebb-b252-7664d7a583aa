using UnityEngine;

namespace BBB.Tools
{
    public static class ObjectUtils
    {   
        public static GameObject Instantiate(GameObject prefab, Transform parent,
            bool worldPositionStays = true)
        {
            UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{prefab.name}]");
            var go = Object.Instantiate(prefab);
            UnityEngine.Profiling.Profiler.EndSample();
            go.transform.SetParent(parent, worldPositionStays);
            return go;
        }
        
        public static TComponent Instantiate<TComponent>(GameObject prefab, Transform parent,
            bool worldPositionStays = true)
        {
            UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{prefab.name}]");
            var go = Object.Instantiate(prefab);
            UnityEngine.Profiling.Profiler.EndSample();
            go.transform.SetParent(parent, worldPositionStays);
            return go.GetComponent<TComponent>();
        }
    }
}