using UnityEngine;
using UnityEngine.UI;

namespace BBB.Core
{
    public class BDebugReporterProxy : BbbMonoBehaviour
    {
        [SerializeField] private Canvas _canvas;
        [SerializeField] private GraphicRaycaster _raycaster;
        
        void OnShowReporter()
        {
            BDebug.AnyErrorPendingToView = false;
            _canvas.enabled = true;
            _raycaster.enabled = true;
        }

        void OnHideReporter()
        {
            _canvas.enabled = false;
            _raycaster.enabled = false;
        }
    }
}