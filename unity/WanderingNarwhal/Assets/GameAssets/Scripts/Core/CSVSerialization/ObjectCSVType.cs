using System;
using System.IO;
using System.Reflection;
using Newtonsoft.Json;

namespace BBB.CSVSerialization
{
    public class ObjectCSVType : CSVTypeBase
    {
        private readonly JsonSerializer _jsonSerializer = new JsonSerializer();
        public override string TypeName
        {
            get { return "Object"; }
        }

        public override bool IsMatch(PropertyInfo property)
        {
            return !property.PropertyType.IsValueType;
        }

        public override string Serialize(object obj)
        {
            if (obj == null)
            {
                return "";
            }

            using (var writer = new StringWriter())
            {
                _jsonSerializer.Serialize(writer, obj);
                return writer.ToString();
            }
        }

        public override object Deserialize(string data, PropertyInfo property)
        {
            if (string.IsNullOrEmpty(data)) return null;

            try
            {
                using (var reader = new StringReader(data))
                {
                    return _jsonSerializer.Deserialize(reader, property.PropertyType);
                }
            }
            catch
            {
                throw new CSVParseException(data, property.PropertyType);
            }
        }

        public override bool IsMatch(FieldInfo field)
        {
            return !field.FieldType.IsValueType;
        }

        public override object Deserialize(string data, FieldInfo field)
        {
            if (string.IsNullOrEmpty(data)) return null;

            try
            {
                using (var reader = new StringReader(data))
                {
                    return _jsonSerializer.Deserialize(reader, field.FieldType);
                }
            }
            catch
            {
                throw new CSVParseException(data, field.FieldType);
            }
        }
    }
}