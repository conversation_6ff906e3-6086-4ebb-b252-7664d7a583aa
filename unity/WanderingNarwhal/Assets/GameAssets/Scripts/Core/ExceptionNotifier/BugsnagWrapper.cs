using System;
using System.Collections.Generic;
using BBB.DI;
using BugsnagUnity;
using BugsnagUnityPerformance;
using GameAssets.Scripts.Utils;

namespace BBB.Core.Crash
{
    public class BugsnagWrapper : IExceptionNotifier
    {
        
        private bool _initialized;
        private bool _userInitialized;
        private ErrorReportingUserMainProperties _userProperties;
        private readonly Dictionary<string, object> _customProperties = new();

        public void Initialize(IContext context)
        {
            InitializeBugsnagManually();
        }

        /// <summary>
        /// Starts Bugsnag Service manually
        /// </summary>
        private void InitializeBugsnagManually()
        {
            if (_initialized) return;
            
            var config = BugsnagSettingsObject.LoadConfiguration();

            config.ApiKey = GameConstants.BugsnagApiKey;
            config.MaxPersistedEvents = 50;

            Bugsnag.Start(config);
            InitializeBugsnagPerformanceManually();
            _initialized = true;
        }

        /// <summary>
        /// Starts Bugsnag Performance Service manually
        /// </summary>
        private void InitializeBugsnagPerformanceManually()
        {
            var config = BugsnagPerformanceSettingsObject.LoadConfiguration();
            config.ApiKey = GameConstants.BugsnagApiKey;
            BugsnagPerformance.Start(config);
        }

        public void InitializeUser(ErrorReportingUserMainProperties userProperties)
        {
            if (!_initialized || _userInitialized)
            {
                return;
            }
            _userProperties = userProperties;

            Bugsnag.SetUser(userProperties.UserId, string.Empty, string.Empty);
            AddMetadata("user", userProperties.GetPropertiesDictionary());
            _userInitialized = true;
        }

        public void Notify(Exception ex)
        {
            if(!_initialized) return;
            
            Bugsnag.AddOnError(BeforeNotify);
            Bugsnag.Notify(ex);
            Bugsnag.RemoveOnError(BeforeNotify);
            return;

            bool BeforeNotify(BugsnagUnity.IEvent @event)
            {
                if(_userProperties != null)
                {
                    if (!string.IsNullOrEmpty(_userProperties.UserId) && @event.GetUser().Id != _userProperties.UserId)
                    {
                        Bugsnag.SetUser(_userProperties.UserId, string.Empty, string.Empty);
                    }

                    AddMetadata("user", _userProperties.GetPropertiesDictionary());
                }

                AddMetadata("customProperties", _customProperties);

                return true;

            }
        }

        private static void AddMetadata(string section, Dictionary<string, object> addingMetadata)
        {
            if (addingMetadata.Count == 0)
            {
                return;
            }

            if (Bugsnag.GetMetadata(section) == null || Bugsnag.GetMetadata(section).Count == 0)
            {
                Bugsnag.AddMetadata(section, addingMetadata);
                return;
            }

            foreach (var metaData in addingMetadata)
            {
                if (Bugsnag.GetMetadata(section, metaData.Key) == null)
                {
                    Bugsnag.AddMetadata(section, metaData.Key, metaData.Value);
                    continue;
                }

                if (Bugsnag.GetMetadata(section, metaData.Key) == metaData.Value) continue;
                
                Bugsnag.ClearMetadata(section, metaData.Key);
                Bugsnag.AddMetadata(section, metaData.Key, metaData.Value);
            }
        }

        public void Log(string message)
        {
           
        }

        public void Restart()
        {
            _initialized = false;
        }

        public void SetCustomProperty(string key, string value)
        {
            if (!_initialized) return;

            _customProperties[key] = value;
        }
    }
}