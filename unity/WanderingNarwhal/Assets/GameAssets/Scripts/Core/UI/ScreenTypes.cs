using System;

namespace BBB
{
    [Flags]
    public enum ScreenType
    {
        None = 0,
        LoadingScreen = 1 << 0,
        // one free place to add new a screen here
        LevelScreen = 1 << 6,
        DebugScreen = 1 << 7,
        // one free place to add new a screen here
        SideMapLevelScreen = 1 << 13,
        SideMapScreen = 1 << 14,
        EpisodeScreen = 1 << 20,

        SideMap = SideMapLevelScreen | SideMapScreen,
        Map = SideMapScreen | EpisodeScreen,
        Levels = LevelScreen | SideMapLevelScreen,
        FullHudScreen = SideMapScreen | EpisodeScreen,
        All = ~None // Keep this one at the end
    }

    public class ScreenTypeConstants
    {
        public static readonly ScreenType PrimaryMap = ScreenType.EpisodeScreen;
    }

    public enum ModalsType
    {
        None,
        LevelStart,
        LevelSuccess,
        LevelSuccessVerticalLB,
        LevelSuccessNoLB,
        LevelOutOfMoves,
        MapNoMoreLives,
        Generic,
        Store,
        GachaClaim,
        SettingsModal,
        Social,
        GenericModal,
        NoConnectionModal,
        DebugModal,
        DialogModal,
        OutOfMovesGacha,
        StoreGacha,
        CurrenciesRewardModal,
        WatchVideoAdModal,
        OutOfMovesBoosterOfferModal,
        StartLevelAdReward,
        AdLootbox,
        BuddyGiftCard,
        GameEvent,
        GameEventLoseLevelWarning,
        Passport,
        CompetitionEventGeneric,
        EventLeaderboard,
        DailyTrivia,
        CompetitionPromotionDemotion,
        GenericPromo,
        WeeklyEventCongrats,
        Basket,
        SdbReward,
        VipProductsModal,
        DailyCollectInfo,
        CityLoading,
        ChangeName,
        EndlessTreasure,
        IapPurchase,
        RaceEventCompetition,
        SaveProgress,
        ClaimGift,
        FoodEventCompetition,
        DiscoEventCompetition,
        RoundStart,
        SideMapLeaderboard,
        LeaderboardModal,
        QrCodeReaderModal,
        TrackPermissionModal,
        RoyaleEventMainModal,
        CardsRewardModal,
        CollectionMainModal,
        TeamCoopEventMainModal,
        TeamVsTeamEventMainModal,
        CollectionContentModal,
        WildCardTokenModal,
        WildCardModal,
        WildCardRewardModal,
        WildCardUseModal,
        WildCardTransformModal,
        NotEnoughStarsModal,
        TripstagramModal,
        TripstagramSnapshotModal,
        SnapshotTaskModal,
        SceneCompletionModal,
        AssetsLoadingModal,
        DeepLinkValidation,
        ComingSoon,
        EndOfContentIntroModal,
        EndOfContentLeaderboardModal,
        DailyTasksModal,
        ChallengeModal,
        GlobeModal,
        ChallengeTrivia,
        GenericInfoModal,
        GenericTutorialModal,
        TapToSkipModal,
        SweepstakesEventModal,
        SweepstakesMilestoneModal,
        SweepstakesMilestoneRewardModal,
        DailyLoginModal,
        ConfirmationModal,
        SweepstakesEmailModal,
        SweepstakesSubmitModal,
    }
}