using System;
using BBB.Controller;
using BBB.Core;
using BBB.UI.Core;
using BebopBee;

namespace BBB
{
    public interface IScreensManager : IViewsManager
    {
        event Action<ScreenType, IScreensController> OnScreenChangingStarted;
        event Action<ScreenType, IScreensController> NextScreenPreShown;
        event Action<ScreenType, IScreensController> ScreenCreated;
        event Action<ScreenType> OnScreenTransitionComplete;
        event Action<ScreenType> OnCurrentScreenTransitionComplete;
        event Action<ScreenType, IScreensController, IViewPresenter> OnScreenChanged;
        event Action<ScreenType> OnFailedScreenTransition;
        event Action<ScreenType> OnSuccessedScreenTransition;

        ScreenType ShowScreen(ScreenType screenType, CommandBase transitionCommands, CreateScreenCommand createScreenCommand);
        void HideScreen(IController controller);
        IScreensController GetCurrentController();
        ScreenType GetCurrentScreenType();
        ScreenType GetPreviousScreenType();
        ScreenType GetTransitionTargetScreenType();
        int GetVisitsCountPerSession(ScreenType screen);

        bool IsTransitionInProgress { get; }
        float TransitionProgress { get; }
        string GetTrackingPreviousScreenType();
        void EnforceTransition();
    }
}