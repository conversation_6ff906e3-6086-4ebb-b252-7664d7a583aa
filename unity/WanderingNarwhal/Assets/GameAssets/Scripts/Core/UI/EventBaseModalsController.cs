using BBB.Core.Analytics;
using BBB.UI.Core;

namespace BBB.Core
{
    public abstract class EventBaseModalsController<TViewPresenter> : BaseModalsController<TViewPresenter>
        where TViewPresenter : IViewPresenter
    {
        protected virtual string EventUid { get; set; }

        protected override void OnPostHide()
        {
            base.OnPostHide();

            DauInteractions.TapOnAutoPopups.LogIfExpected(
                ClosedIntentionally
                    ? DauInteractions.TapOnAutoPopups.GameEventClose
                    : DauInteractions.TapOnAutoPopups.GameEventClick, EventUid);
            DauInteractions.TapOnAutoPopups.ClearAwaitLogs(DauInteractions.TapOnAutoPopups.GameEventClose,
                DauInteractions.TapOnAutoPopups.GameEventClick);
        }
    }
}