using System.Collections.Generic;
using BBB.Controller;
using BBB.Core;
using BBB.DI;
using BBB.Modals;
using BBB.UI.Core;
using BebopBee.Core.UI;
using Core.Debug;
using Cysharp.Threading.Tasks;
using UnityEngine.Profiling;

namespace BBB
{
    public sealed class ModalsBuilder : ViewsBuilder<ModalsType>, IModalsBuilder
    {
        private IModalsManager _modalsManager;

        private readonly Dictionary<ModalsType, IController> _cacheDict = new Dictionary<ModalsType, IController>();

        public override void InitializeByContext(IContext context)
        {
            base.InitializeByContext(context);
            _modalsManager = context.Resolve<IModalsManager>();
            
            // TODO: Disabled for now, until we can properly fix all the issues related to destroying modal game objects
            // For example:
            // 1. listeners that are not being removed properly (OnDestroy may not be called if the object was never active)
            // 2. Modals that appear on top of others: the hidden modal is being removed (OOM modal) but it shouldn't
            //      since it's still active
            // MemoryAdviceApi.RegisterMemoryStateWarn(OnLowMemoryHandler);

            Manager = _modalsManager;
        }
        
        private void RemoveModals(List<ModalsType> modalsToRemove)
        {
            foreach (var modalsType in modalsToRemove)
            {
                _cacheDict.Remove(modalsType);
            }
        }

        private void OnLowMemoryHandler()
        {
            RemoveModals(_modalsManager.OnLowMemory());
        }

        //TODO call this method when changing screen only if low-memory situation detected (checked by MemoryTracker.IsShouldDisposeUnusedAssets())
        private void RemoveRareModals()
        {
            RemoveModals(_modalsManager.RemoveRareModals());
        }

        public void Show<TController>(TController ctrl, string uid = null,
            ShowMode showMode = ShowMode.Immediate, ModalSetupParamsBase setupParams = null)
            where TController : IController
        {
            var logStopWatch = new LogStopwatch();
            logStopWatch.Start();
            _modalsManager.PushModal(ctrl, showMode, setupParams);
            logStopWatch.StopLog(string.Format("Modal {0} shown", ctrl.GetType().Name));
        }

        public override void Hide<TController>(TController ctrl)
        {
            _modalsManager.HideModal(ctrl);
        }

        public TController CreateModalView<TController>(ModalsType modalsType)
            where TController : class, IController, new()
        {
            if (_cacheDict.TryGetValue(modalsType, out var ctrl))
                return (TController)ctrl;

            var newController = CreateView<TController>(modalsType, null);
            _cacheDict.Add(modalsType, newController);
            return newController;
        }

        public IController TryCreateModalView(ModalsType modalsType)
        {
            if (_cacheDict.TryGetValue(modalsType, out var ctrl))
                return ctrl;
            
            var newController = CreateView(modalsType, null);
            if (newController != null)
                _cacheDict.Add(modalsType, newController);
            return newController;
        }

        public override void ReleaseByContext(IContext context)
        {
            base.ReleaseByContext(context);
            // TODO: Disabled for now, until we can properly fix all the issues related to destroying modal game objects
            // Should be enabled alongside with logic commented out in InitializeByContext
            // MemoryAdviceApi.UnRegisterMemoryStateWarn(OnLowMemoryHandler);
            
            foreach (var controller in _cacheDict.Values)
            {
                controller.GetView?.DisposeResources();
                controller.GetView?.DisposeContext();
                controller.DisposeContext();
            }
            _cacheDict.Clear();
        }

        public void RegisterView<TController, TViewPresenter>(ModalsType type)
            where TController : class, IController<TViewPresenter>, IController, new()
            where TViewPresenter : class, IViewPresenter
        {
            //TODO: Lambda function to method?
            _viewsFactory.Register<IContext>(type, (context) =>
            {
#if BBB_DEBUG
                var logStopWatch = new LogStopwatch();
                logStopWatch.Start();
#endif

                Profiler.BeginSample($"Manager.GetOrCreateController {typeof(TController).FullName}");
                var ctrl = Manager.GetOrCreateController<TController, TViewPresenter, ModalsType>(type);
                Profiler.EndSample();

                Profiler.BeginSample($"ctrl.Init {typeof(TController).FullName}");
                ctrl.Init(context);
                Profiler.EndSample();

                Profiler.BeginSample($"TryGetCachedView {typeof(TController).FullName}");
                var cachedView = Manager.TryGetCachedView<TController, TViewPresenter, ModalsType>(type, ctrl);
                Profiler.EndSample();

                if (cachedView != null)
                {
                    Profiler.BeginSample($"OnViewLoaded {typeof(TController).FullName}");
                    OnViewLoaded(cachedView);
                    Profiler.EndSample();
                    return ctrl;
                }

                Manager.PreloadView<TController, TViewPresenter, ModalsType>(type, ctrl).ContinueWith(OnViewLoaded);
                
                void OnViewLoaded(IViewPresenter view)
                {
                    if (view == null)
                        BDebug.LogErrorFormat(LogCat.CoreViews, "Can't find view for type:{0} ctrl:{1} view:{2}", 
                            type,
                            typeof(TController),
                            typeof(TViewPresenter));

                    ctrl.SetView((TViewPresenter)view);

#if BBB_DEBUG
                    logStopWatch.StopLog("View " + type + " created");
#endif
                }

                return ctrl;
            });
        }
    }
}