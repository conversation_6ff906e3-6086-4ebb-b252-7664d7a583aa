using System.Collections.Generic;
using BBB.Core.AssetBundles.Handlers;
using BebopBee;
using Cysharp.Threading.Tasks;
using UnityEngine.Profiling;

namespace BBB.Core.AssetBundles
{
    public class PriorityLoader : IPriorityLoader
    {
        private readonly List<ILoadHandler>[] _priorityList;
        private readonly HashSet<ILoadHandler> _handlersInProgress;
        private const int PoolSize = (int) LoadPriority.End;
        private const int MaxConcurrentHandlers = 2;

        private readonly int[] _handlersInExecution;

        public PriorityLoader()
        {
            Profiler.BeginSample("new PriorityLoader");
            _handlersInProgress = new HashSet<ILoadHandler>();
            _priorityList = new List<ILoadHandler>[PoolSize];
            _handlersInExecution = new int[PoolSize];

            for (var i = 0; i < PoolSize; i++)
            {
                _priorityList[i] = new List<ILoadHandler>();
            }
            Profiler.EndSample();
        }

        public void Execute(ILoadHandler handler, LoadPriority priority = LoadPriority.High)
        {
            if (_handlersInProgress.Contains(handler))
                return;
            LoadPriority oldPriority;
            var oldPrio = string.Empty;
            if (ShouldChangePriority(handler, priority, out oldPriority) && priority != oldPriority)
            {
                _priorityList[(int) oldPriority].Remove(handler);
                oldPrio = $", oldPriority: {oldPriority}, handler oldPriority: {handler.Priority}";
                handler.UpdatePriority(priority);
            }
            BDebug.Log(LogCat.AssetBundle, $"PriorityLoader TryExecute {handler} - runPriority: {priority}, handler priority: {handler.Priority}{oldPrio}");
            if (priority == LoadPriority.Immediate)
            {
                ExecuteHandler(handler, 0);
            }
            else if (!_priorityList[(int) priority].Contains(handler))
            {
                _priorityList[(int) priority].Add(handler);
            }
            CleanCompleted();
            StartExecution();
            if (AppDefinesConverter.BbbDebug)
            {
                DebugList();
            }
        }

        public void Tick()
        {
            CleanCompleted();
            StartExecution();
        }

        public void Restart()
        {
            CleanCompleted();
        }

        private void DebugList()
        {
            for (var i = 1; i < PoolSize; i++)
            {
                var handlers = new List<ILoadHandler>(_priorityList[i]);
                var count = handlers.Count;
                for (var j = 0; j < count; j++)
                {
                    var handler = handlers[j];
                    BDebug.Log(LogCat.AssetBundle, $"Priority list pIndex: {i}, item: {j+1}/{count} {handler}: {handler.IsRunning} {handler.Priority} {handler.IsDone} ");
                }
            }
        }

        private void CleanCompleted()
        {
            var itemsToRemove = new List<ILoadHandler>();
            for (var i = 0; i < PoolSize; i++)
            {
                itemsToRemove.Clear();

                var handlers = _priorityList[i];
                foreach (var handler in handlers)
                {
                    if (handler.IsDone)
                        itemsToRemove.Add(handler);
                }

                itemsToRemove.Map(handlers.Remove);
            }
        }

        private void StartExecution()
        {
            var higherPriorityLoadsCount = _handlersInExecution[0];
            for (var i = 1; i < PoolSize; i++)
            {
                if (higherPriorityLoadsCount + _handlersInExecution[i] >= MaxConcurrentHandlers) return;
                
                var handlers = new List<ILoadHandler>(_priorityList[i]);
                foreach (var handler in handlers)
                {
                    if (handler.IsRunning || handler.IsDone) continue;

                    ExecuteHandler(handler, i);
                    if (higherPriorityLoadsCount + _handlersInExecution[i] >= MaxConcurrentHandlers) return;
                }
                
                higherPriorityLoadsCount += _handlersInExecution[i];
            }
        }

        private void ExecuteHandler(ILoadHandler handler, int priorityIndex)
        {
            BDebug.Log(LogCat.AssetBundle, $"PriorityLoader ExecuteHandler {handler} ({handler.triggeredByAsset}) - priority: {handler.Priority}, pIndex: {priorityIndex} ");
            _handlersInProgress.Add(handler);
            _handlersInExecution[priorityIndex]++;
            handler.GetHandleAsync().ContinueWith(_ =>
            {
                _handlersInProgress.Remove(handler);
                _handlersInExecution[priorityIndex]--;
            });
            BDebug.Log(LogCat.AssetBundle, $"PriorityLoader Completed {handler} ({handler.triggeredByAsset}) - priority: {handler.Priority}, pIndex: {priorityIndex}, isDone: {handler.IsDone} ");
            
            handler.Execute();
        }

        private bool ShouldChangePriority(ILoadHandler handler, LoadPriority priority, out LoadPriority oldPriority)
        {
            oldPriority = priority;
            for (var i = (int) priority + 1; i < PoolSize; i++)
            {
                if (!_priorityList[i].Contains(handler)) continue;
                
                oldPriority = (LoadPriority) i;
                return true;
            }

            return false;
        }
    }
}