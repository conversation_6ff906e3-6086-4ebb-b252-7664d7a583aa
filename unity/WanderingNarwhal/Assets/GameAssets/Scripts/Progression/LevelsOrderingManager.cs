using System.Collections.Generic;
using System;
using BBB.Core;
using BBB.GameAssets.Scripts.Player;
using BBB.Map;
using Core.Configs;
using FBConfig;
using UnityEngine.Profiling;

namespace BBB.Navigation
{
    public class LevelsOrderingManager : ILevelsOrderingManager
    {
        private static readonly Type[] RequiredConfigs =
        {
            typeof(ProgressionLevelConfig)
        };

        private IDictionary<string, ProgressionLevelConfig> _levelConfigs;
        
        private readonly Dictionary<string, (string, int)> _parsedLevelUids = new();
        private readonly Dictionary<string, string> _cachedNameByLevelUid = new ();
        const string MainLevelPrefix = "level";

        public void SetupConfig(IConfig config)
        {
            Profiler.BeginSample("LevelOrderingManager SetupConfigs");
            SetupConfigs(config);
            Profiler.EndSample();

            Config.OnConfigUpdated -= SetupConfigs;
            Config.OnConfigUpdated += SetupConfigs;

            Profiler.BeginSample("LevelOrderingManager SetOrdering");

            LevelConfigExtension.SetOrdering(this);
            Profiler.EndSample();
        }

        private void SetupConfigs(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            _levelConfigs = config.Get<ProgressionLevelConfig>();
        }

        /// <summary>
        /// Return previous level based on Sort Order
        /// If checkForLocation is on, it will return levels only from the same location
        /// Returns empty if there is no level before
        /// </summary>
        /// <param name="levelUid"></param>
        /// <param name="checkForLocation"></param>
        /// <param name="ignoreVip"></param>
        /// <returns></returns>
        public string GetPreviousLevelUid(string levelUid)
        {
            if (!levelUid.IsNullOrEmpty() && TryParseLevelUid(levelUid, out var location, out var number))
            {
                if (location.IsNullOrEmpty())
                {
                    location = MainLevelPrefix;
                }

                var prevLevelUid = $"{location}{number - 1}";
                return _levelConfigs.ContainsKey(prevLevelUid) ? prevLevelUid : string.Empty;
            }

            BDebug.LogError(LogCat.General, "Couldn't find level with uid: " + levelUid);
            return string.Empty;
        }

        /// <summary>
        /// Return next level based on Sort Order
        /// If checkForLocation is on, it will return levels only from the same location
        /// Returns empty if there is no level after
        /// </summary>
        /// <param name="levelUid"></param>
        /// <param name="checkForLocation"></param>
        /// <param name="ignoreVip"></param>
        /// <returns></returns>
        public string GetNextLevelUid(string levelUid)
        {
            if (!levelUid.IsNullOrEmpty() && TryParseLevelUid(levelUid, out var location, out var number))
            {
                if (location.IsNullOrEmpty())
                {
                    location = MainLevelPrefix;
                }

                var nextLevelUid = $"{location}{number + 1}";
                return _levelConfigs.ContainsKey(nextLevelUid) ? nextLevelUid : string.Empty;
            }

            BDebug.LogError(LogCat.General, "Couldn't find level with uid: " + levelUid);
            return string.Empty;
        }
        
        public bool TryParseLevelUid(string levelUid, out string location, out int number)
        {
            Profiler.BeginSample("LevelsOrderingManager.TryParseLevelUid");
            location = null;
            number = 0;
            if (levelUid.IsNullOrEmpty())
            {
                BDebug.LogError(LogCat.Config, "[TryParseLevelUid] levelUid is null or empty");
                Profiler.EndSample();
                return false;
            }

            if (_parsedLevelUids.TryGetValue(levelUid, out var cachedResult))
            {
                location = cachedResult.Item1;
                number = cachedResult.Item2;
                Profiler.EndSample();
                return true;
            }
            var index = levelUid.Length - 1;
            while (index >= 0 && char.IsDigit(levelUid[index]))
            {
                index--;
            }

            if (index == levelUid.Length - 1)
            {
                BDebug.LogError(LogCat.Config, $"[TryParseLevelUid] can't parse {levelUid}");
                Profiler.EndSample();
                return false;
            }

            location = index >= 0 ? levelUid.Substring(0, index + 1) : string.Empty;
            if (location == MainLevelPrefix)
                location = string.Empty;
            number = Int32.Parse(levelUid.Substring(index + 1, levelUid.Length - index - 1));
            _parsedLevelUids[levelUid] = (location, number);
            Profiler.EndSample();
            return true;
        }

        public bool HasLevel(string levelUid)
        {
            return _levelConfigs.ContainsKey(levelUid);
        }

        /// <summary>
        /// Return LevelConfig.Name
        /// It is equal to number in index for non vip levels
        /// and equal to base level for VIP
        /// For example for VIP level after 45 and before 46 it will be 45
        /// </summary>
        /// <param name="levelUid"></param>
        /// <returns></returns>
        public string GetLevelName(string levelUid)
        {
            if (!levelUid.IsNullOrEmpty())
            {
                if (_cachedNameByLevelUid.TryGetValue(levelUid, out var name))
                    return name;

                if (_levelConfigs.TryGetValue(levelUid, out var levelConfig))
                {
                    name = levelConfig.GetLevelName();
                    _cachedNameByLevelUid[name] = name;
                    return name;
                }
            }

            BDebug.LogError( LogCat.Config,$"Couldn't find level with uid: {levelUid}");
            return string.Empty;
        }

        /// <summary>
        /// Return LevelConfig.SortOrder raw value
        /// For VIP levels it is base level number + 0.5f
        /// </summary>
        /// <param name="levelUid"></param>
        /// <returns></returns>
        public float GetLevelSortOrder(string levelUid)
        {
            if (!levelUid.IsNullOrEmpty() && _levelConfigs.TryGetValue(levelUid, out var config))
            {
                return config.SortOrder;
            }

            BDebug.LogError( LogCat.Config,$"Couldn't find level with uid: {levelUid}");
            return float.MaxValue;
        }

        /// <summary>
        /// Returns 0 if levels have the same sortOrder, return > 0 if A has higher sortOrder than level B, returns < 0 if B has higher sortOrder than level A
        /// </summary>
        /// <param name="levelAUid"></param>
        /// <param name="levelBUid"></param>
        /// <returns></returns>
        public int CompareLevels(string levelAUid, string levelBUid)
        {
            if (levelAUid.IsNullOrEmpty() || !_levelConfigs.TryGetValue(levelAUid, out var levelAConfig))
            {
                if (!levelBUid.IsNullOrEmpty() && _levelConfigs.ContainsKey(levelBUid)) return -1;
                
                BDebug.LogError( LogCat.Config,$"Couldn't find level with uid: {levelBUid}");
                return 0;
            }

            if (!levelBUid.IsNullOrEmpty() && _levelConfigs.TryGetValue(levelBUid, out var levelBConfig))
                return levelAConfig.SortOrder.CompareTo(levelBConfig.SortOrder);

            BDebug.LogError( LogCat.Config,$"Couldn't find level with uid: {levelBUid}");
            return 1;
        }

        public int GetLevelsDifference(string levelAUid, string levelBUid)
        {
            if (levelAUid == null && levelBUid == null)
            {
                return 0;
            }

            if (levelAUid == null)
            {
                return (int)GetLevelSortOrder(levelBUid);
            }

            if (levelBUid == null)
            {
                return (int)GetLevelSortOrder(levelAUid);
            }

            return Math.Abs((int)(GetLevelSortOrder(levelAUid) - GetLevelSortOrder(levelBUid)));
        }

        /// <summary>
        /// Returns true if levelAuid has higher Sort Order value (means it is further by progression)
        /// If orEqual is on, it will return true if levelA has the same Sort Order
        /// </summary>
        /// <param name="levelAUid"></param>
        /// <param name="levelBUid"></param>
        /// <param name="orEqual"></param>
        /// <returns></returns>
        public bool IsLevelAFurtherThanLevelB(string levelAUid, string levelBUid, bool orEqual = false)
        {
            var compareDifference = CompareLevels(levelAUid, levelBUid);

            return compareDifference switch
            {
                > 0 => true,
                0 => orEqual,
                _ => false
            };
        }

        public ProgressionLevelConfig GetFirstWhereFalse(string levelUid, Predicate<ProgressionLevelConfig> goPrevPredicate)
        {
            while (!levelUid.IsNullOrEmpty())
            {
                if (_levelConfigs.TryGetValue(levelUid, out var levelConfig))
                {
                    if (goPrevPredicate(levelConfig))
                    {
                        levelUid = GetPreviousLevelUid(levelUid);
                    }
                    else
                    {
                        return levelConfig;
                    }
                }
                else
                {
                    BDebug.LogError( LogCat.Config,$"Couldn't find level with uid: {levelUid}");
                    return FlatBufferHelper.DefaultProgressionLevelConfig;
                }
            }

            return FlatBufferHelper.DefaultProgressionLevelConfig;
        }
    }
}