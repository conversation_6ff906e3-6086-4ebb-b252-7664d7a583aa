using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.DailyTrivia;
using BBB.DI;
using BBB.RaceEvents;
using BebopBee;
using Bebopbee.Core.Systems.RpcCommandManager;
using Bebopbee.Core.Systems.RpcCommandManager.Core;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Actions;
using UnityEngine;
using RPC.Core;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.DailyTask;
using GameAssets.Scripts.Deeplink;
using GameAssets.Scripts.Utils;
using LocalNotificationsConfig = FBConfig.LocalNotificationsConfig;

namespace BBB
{
    public class LocalNotificationsScheduler
    {
        private ILocalizationManager _localizationManager;
        private ILivesManager _livesManager;
        private IConfig _config;
        private INotificationManager _notificationManager;
        private IGameEventManager _gameEventManager;
        private IRaceEventManager _raceGameEventManager;
        private TimeManager _timeManager;
        private List<NotificationData> _notificationsDTO = new();
        private RpcCommandManager _rpcCommandManager;
        private DailyTriviaManager _dailyTriviaManager;
        private WeeklyLeaderboardManager _weeklyLeaderboardManager;
        private GachaManager _gachaManager;
        private IDailyTasksManager _dailyTasksManager;
        private readonly List<Notification> _localDebugNotifications = new();

        public void ProvideNotificationManager(INotificationManager notificationManager)
        {
            _notificationManager = notificationManager;
        }

        public void Init(IContext context)
        {
            _config = context.Resolve<IConfig>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _gameEventManager = context.Resolve<IGameEventManager>();
            _raceGameEventManager = context.Resolve<IRaceEventManager>();
            _dailyTriviaManager = context.Resolve<DailyTriviaManager>();

            _livesManager = context.Resolve<ILivesManager>();
            _timeManager = context.Resolve<TimeManager>();
            _rpcCommandManager = context.Resolve<RpcCommandManager>();
            _weeklyLeaderboardManager = context.Resolve<WeeklyLeaderboardManager>();
            _gachaManager = context.Resolve<GachaManager>();
            _dailyTasksManager = context.Resolve<IDailyTasksManager>();
        }

        public void RemoveAllPendingNotifications()
        {
            _notificationManager.RemoveAllPendingNotifications();
        }

        public void Schedule()
        {
            _notificationsDTO.Clear();

            ScheduleDailyNotifications(false);
            ScheduleGameEventsNotifications(false);
            ScheduleDailyTriviaNotifications(false);
            ScheduleLivesRefillNotifications(true);
            ScheduleGachaRefillsNotifications(true);
            ScheduleDailyTasksNotifications(true);
            ScheduleDailyTasksStreakNotifications(true);

            if (AppDefinesConverter.BbbDebug)
            {
                ScheduleLocalDebugNotifications();
            }

            SendNotifications();
        }

        private void SendNotifications()
        {
            // RPC call to send _notificationsDTO.ToArray() -> binary serialize?
            BDebug.Log(LogCat.Notification, $"Sent to server {_notificationsDTO.Count} notifications");
            RpcCommands.SendAsync<ScheduleNotificationsCommand>(_notificationsDTO).Forget();
            _rpcCommandManager?.SendAll().Forget();
        }

        private void ScheduleDailyNotifications(bool localSchedulingOnly)
        {
            var localPushNotificationsTimingConfig = _config.Get<FBConfig.LocalPushNotificationsTimingConfig>();
            var conf = localPushNotificationsTimingConfig.GetSafe("default");
            var secondsInNotifDay = !conf.IsNull() ? conf.SecondsInNotifDay : 0;

            if (secondsInNotifDay <= 0)
            {
                Debug.LogError("Seconds in notif day is 0, 86400 is set instead");
                secondsInNotifDay = 86400;
            }

            int[] daysInterval = { 1, 2, 3, 4, 5, 6, 7, 14, 15, 21, 30, 45, 60, 75, 90 };
            foreach (var day in daysInterval)
            {
                var notificationId = $"DAILY_D{day}";

                var inTime = day * secondsInNotifDay;
                if (inTime <= 0)
                {
                    continue;
                }

                ScheduleById(notificationId, "daily", inTime, localSchedulingOnly, string.Empty, string.Empty);
            }
        }

        private void ScheduleLivesRefillNotifications(bool localSchedulingOnly)
        {
            var remTime = _livesManager.TimeTillAllLivesRefill;
            if (remTime > 1f)
            {
                ScheduleById("LIFES_REFILLED", "lives", (int)remTime, localSchedulingOnly, DeepLinkFactory.Modal, ContinueLevelFlowAction.Name);
            }
        }

        private void ScheduleGachaRefillsNotifications(bool localSchedulingOnly)
        {
            var oomGachaRefilled = _gachaManager.GetTimeUntilFreeRollForNotification(GachaType.OutOfMoves);
            const string gachaRefilledNotificationId = "GACHA_REFILLED";
            if (oomGachaRefilled > 1f)
            {
                ScheduleById(gachaRefilledNotificationId, "gacha", oomGachaRefilled, localSchedulingOnly, DeepLinkFactory.Modal, ContinueLevelFlowAction.Name);
            }
        }

        private void ScheduleGameEventsNotifications(bool localSchedulingOnly)
        {
            ScheduleGameEventStartNotifications(localSchedulingOnly);
            ScheduleGameEventEndNotifications(localSchedulingOnly);
        }

        private void ScheduleDailyTriviaNotifications(bool localSchedulingOnly)
        {
            if (!_dailyTriviaManager.IsLocallyScheduled)
                return;

            if (!_dailyTriviaManager.IsInitialized)
            {
                Debug.LogWarning("Can't schedule daily trivia notifications as it is not initialized");
                return;
            }

            var triviaDay = _dailyTriviaManager.GetTotalDaysSinceTriviaStart();
            var remainingTrivias = _dailyTriviaManager.GetRemainingTrivias(triviaDay);

            var offsetPerTrivia = _dailyTriviaManager.IsDebugEnabled ? 10 * 60 : 24 * 60 * 60;

            var title = _localizationManager.getLocalizedText("DAILY_TRIVIA_NOTIFICATION_TITLE");
            // Start scheduling notifications from next Trivia onwards
            for (var i = 1; i < remainingTrivias.Count; i++)
            {
                var trivia = remainingTrivias[i];
                // triviaDay + i is the actual day of the trivia, since i starts from 1
                var actualTriviaDay = triviaDay + i;
                var remainingTime = _dailyTriviaManager.CycleStartDate + actualTriviaDay * offsetPerTrivia + _dailyTriviaManager.GetLocalNotificationTimeOffsetInSeconds - Util.UnixUtcTimestamp();
                var message = _localizationManager.getLocalizedText(trivia.NotificationDescription);
                // Trivia id is DAILY_TRIVIA_{actualTriviaDay + 1} since actualTriviaDay starts from 0
                ScheduleById($"DAILY_TRIVIA_{actualTriviaDay + 1}", "trivia", Convert.ToInt32(remainingTime), title,
                    string.Empty, message, trivia.NotificationImageURL.IsNullOrEmpty() ? GameConstants.DailyTriviaDefaultNotificationImageUrl : trivia.NotificationImageURL,
                    DeepLinkFactory.Modal, "action=notify_daily_trivia", localSchedulingOnly);
            }
        }

        private void ScheduleDailyTasksStreakNotifications(bool localSchedulingOnly)
        {
            if (_dailyTasksManager.AreAllTasksClaimed())
                return;

            const string id = "DAILY_TASKS_PENDING";
            const string notificationType = "dailyTasks";

            foreach (var dailyTasksNotifierSettings in _dailyTasksManager.NotifierSettingsList)
            {
                if (_dailyTasksManager.CurrentStreakDay >= dailyTasksNotifierSettings.StreakToNotify)
                {
                    var remainingTime = _dailyTasksManager.GetTimeLeft() -
                                        dailyTasksNotifierSettings.TimeOffsetInMinutes * 60;

                    if (remainingTime <= 0) continue;

                    var title = _localizationManager.getLocalizedText(dailyTasksNotifierSettings
                        .NotifierHeaderLocalizedKey);
                    var message =
                        _localizationManager.getLocalizedText(dailyTasksNotifierSettings.NotifierBodyLocalizedKey);

                    ScheduleById(id, notificationType, Convert.ToInt32(remainingTime), title,
                        string.Empty, message, null, null, null, localSchedulingOnly);
                }
            }
        }

        private void ScheduleDailyTasksNotifications(bool localSchedulingOnly)
        {
            if (_dailyTasksManager.AreAllTasksClaimed() || !_dailyTasksManager.IsAnyTaskCompleted || _dailyTasksManager.CurrentStreakDay > 1)
                return;

            var remainingTime = _dailyTasksManager.GetTimeLeft() - _dailyTasksManager.GetLocalNotificationTimeOffsetInSeconds;
            if (remainingTime <= 0)
                return;

            var title = _localizationManager.getLocalizedText("DAILY_TASKS_NOTIFICATION_TITLE");
            var message = _localizationManager.getLocalizedText("DAILY_TASKS_NOTIFICATION_BODY");
            const string id = "DAILY_TASKS_PENDING";
            const string notificationType = "dailyTasks";

            ScheduleById(id, notificationType, Convert.ToInt32(remainingTime), title,
                string.Empty, message, null, DeepLinkFactory.Modal, "action=DailyTasksModal", localSchedulingOnly);
        }

        private void ScheduleGameEventStartNotifications(bool localSchedulingOnly)
        {
            var eventTimePairs = _gameEventManager
                .GetFutureNotifiableEvents(gameEvent => gameEvent.GetNextStartTime());
            var raceEventTimePairs =
                _raceGameEventManager.GetFutureNotifiableEvents(raceEvent => raceEvent.GetNextStartTime());

            var combinedEventTimePairs = new List<(INotifiableEvents gameEvent, DateTime dateTime)>(eventTimePairs);

            foreach (var raceEventTimePair in raceEventTimePairs)
            {
                if (!combinedEventTimePairs.Contains(raceEventTimePair))
                {
                    combinedEventTimePairs.Add(raceEventTimePair);
                }
            }

            foreach (var eventTimePair in combinedEventTimePairs)
            {
                var gameEvent = eventTimePair.gameEvent;
                var startTime = eventTimePair.dateTime;
                var id = "GAME_EVENT_START_NOTIFICATION_" + gameEvent.Uid.ToUpper();
                var currentUtcTime = _timeManager.GetCurrentDateTime();
                var remainingTime = startTime.Subtract(currentUtcTime).TotalSeconds;

                if (remainingTime <= 0)
                {
                    continue;
                }

                var title = gameEvent.GetStartNotificationTitle();
                var message = gameEvent.GetStartNotificationMessage();
                ScheduleById(id, "event", (int)remainingTime, title, string.Empty,
                    message, null, null, null, localSchedulingOnly);
            }
        }

        private void ScheduleGameEventEndNotifications(bool localSchedulingOnly)
        {
            var timeBeforeEndToNotify = _gameEventManager.TimeBeforeEventEndNotification;
            var eventTimePairs = _gameEventManager.GetFutureNotifiableEvents(gameEvent => gameEvent.GetNextEndTime());

            var weeklyEndTime = _weeklyLeaderboardManager.GetEndTimeOfLastJoinedPeriod();

            if (weeklyEndTime == default)
                return;
            var currentUtcTime = _timeManager.GetCurrentDateTime();
            var remainingTime = weeklyEndTime.Subtract(currentUtcTime).TotalSeconds;
            const string wlId = "GAME_EVENT_END_NOTIFICATION_WEEKLYLEADERBOARD";
            if (remainingTime <= timeBeforeEndToNotify)
                return;

            string wlTitle;
            string wlMessage;
            if (BebopBee.AppDefinesConverter.UnityIos)
            {
                wlTitle = LocalizedNotificationTextForIOS("WEEKLY_LEADERBOARD_END_NOTIFICATION_TITLE");
                wlMessage = LocalizedNotificationTextForIOS("WEEKLY_LEADERBOARD_END_NOTIFICATION_MESSAGE");
            }
            else
            {
                wlTitle = _localizationManager.getLocalizedText("WEEKLY_LEADERBOARD_END_NOTIFICATION_TITLE");
                wlMessage = _localizationManager.getLocalizedText("WEEKLY_LEADERBOARD_END_NOTIFICATION_MESSAGE");
            }

            ScheduleById(wlId, "event", (int)remainingTime - timeBeforeEndToNotify,
                wlTitle, string.Empty, wlMessage, null, null, null, localSchedulingOnly);

            foreach (var eventTimePair in eventTimePairs)
            {
                var gameEvent = eventTimePair.Item1;
                var endTime = eventTimePair.Item2;
                var id = "GAME_EVENT_END_NOTIFICATION_" + gameEvent.Uid.ToUpper();

                currentUtcTime = _timeManager.GetCurrentDateTime();
                remainingTime = endTime.Subtract(currentUtcTime).TotalSeconds;

                if (remainingTime <= timeBeforeEndToNotify)
                {
                    continue;
                }

                string title;
                string message;
                if (BebopBee.AppDefinesConverter.UnityIos)
                {
                    title = LocalizedNotificationTextForIOS("GAME_EVENT_END_NOTIFICATION_TITLE");
                    message = LocalizedNotificationTextForIOS("GAME_EVENT_END_NOTIFICATION_MESSAGE",
                        gameEvent.ShortNameText);
                }
                else
                {
                    title = _localizationManager.getLocalizedText("GAME_EVENT_END_NOTIFICATION_TITLE");
                    message = _localizationManager.getLocalizedTextWithArgs("GAME_EVENT_END_NOTIFICATION_MESSAGE",
                        gameEvent.ShortNameText);
                }

                ScheduleById(id, "event", (int)remainingTime - timeBeforeEndToNotify,
                    title, string.Empty, message, null, null, null, localSchedulingOnly);
            }
        }

        private string LocalizedNotificationTextForIOS(string key, params object[] args)
        {
            var iosKey = $"{key}_IOS";
            if (_localizationManager.ContainsLocalizableText(iosKey))
            {
                key = iosKey;
            }

            return args.Length > 0 ? _localizationManager.getLocalizedTextWithArgs(key, args) : _localizationManager.getLocalizedText(key);
        }

        private void ScheduleById(string id, string notificationType, int time, bool localScheduling, string action, string actionParams)
        {
            if (localScheduling)
            {
                InternalScheduleById(id, notificationType, time, action, actionParams);
            }
            else
            {
                _notificationsDTO.Add(new NotificationData()
                {
                    Id = id,
                    Time = time,
                    ParamsConfigBased = true,
                    CustomAction = action,
                    CustomActionParams = actionParams
                });
            }
        }

        private void ScheduleById(string id, string notificationType, int time,
            string title, string subtitle, string message,
            string largeIcon, string action, string actionParams, bool localScheduling)
        {
            if (localScheduling)
            {
                InternalScheduleById(id, notificationType, time, title, subtitle, message, largeIcon, action, actionParams);
            }
            else
            {
                _notificationsDTO.Add(new NotificationData()
                {
                    Id = id,
                    Time = time,
                    ParamsConfigBased = false,
                    CustomTitle = title,
                    CustomSubtitle = subtitle,
                    CustomMessage = message,
                    CustomIcon = largeIcon,
                    CustomAction = action,
                    CustomActionParams = actionParams,
                });
            }
        }

        private void InternalScheduleById(string id, string notificationType, int time, string action, string actionParams)
        {
            var localNotificationsConfig = _config.Get<LocalNotificationsConfig>();

            if (!localNotificationsConfig.TryGetValue(id, out var notificationConfig))
            {
                BDebug.LogError(LogCat.Notification, $"ScheduleByID :{id}  local notification config not found");
                return;
            }

            if (notificationConfig.IsNull())
            {
                BDebug.LogError(LogCat.Notification, $"ScheduleByID :{id}  local notification config is empty");
                return;
            }

            var titleKey = notificationConfig.Title;
            if (BebopBee.AppDefinesConverter.UnityIos && _localizationManager.ContainsLocalizableText(titleKey + "_IOS"))
            {
                titleKey += "_IOS";
            }

            var notificationTitle = _localizationManager.getLocalizedText(titleKey);
            var messageKey = notificationConfig.Message;
            if (BebopBee.AppDefinesConverter.UnityIos && _localizationManager.ContainsLocalizableText(messageKey + "_IOS"))
            {
                messageKey += "_IOS";
            }

            var notificationMessage = _localizationManager.getLocalizedText(messageKey);
            BDebug.Log(LogCat.Notification, $"ScheduleByID :{id}  title: {notificationConfig.Title} time: {time}");

            if (action.IsNullOrEmpty())
            {
                action = notificationConfig.Action;
            }

            if (actionParams.IsNullOrEmpty())
            {
                actionParams = notificationConfig.ActionParams;
            }

            InternalScheduleById(id, notificationType, time, notificationTitle, notificationConfig.Subtitle, notificationMessage, null, action, actionParams);
        }

        private void InternalScheduleById(string id, string notificationType, int time, string title, string subtitle, string message, string largeIcon, string action, string actionParams)
        {
            if (time <= 0)
            {
                return;
            }

            var notif = new NotificationBuilder()
                .Title(title)
                .Subtitle(subtitle)
                .Message(message)
                .ScheduleIn(time)
                .WithLoadingAction(action, actionParams)
                .WitId(id)
                .WitType(notificationType)
                .WithImage(string.Empty)
                .WithBadgeNumber(1)
                .WithLargeIcon(largeIcon)
                .Build();

            _notificationManager.Schedule(notif);
        }

        public void AddLocalDebugNotifications(Notification notification)
        {
            _localDebugNotifications.Add(notification);
        }

        private void ScheduleLocalDebugNotifications()
        {
            foreach (var notification in _localDebugNotifications)
            {
                _notificationManager.Schedule(notification);
            }

            _localDebugNotifications.Clear();
        }
    }
}