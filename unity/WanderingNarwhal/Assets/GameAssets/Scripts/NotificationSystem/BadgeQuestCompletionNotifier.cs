using BBB.DI;
using BBB.Quests;
using BBB.Quests.Objectives;
using FBConfig;

namespace BBB
{
    public class BadgeQuestCompletionNotifier : BaseNotifier
    {
        private const string BadgesQuestUid = "travel_badge";

        private QuestManager _questManager;
        private IEventDispatcher _eventDispatcher;

        public void Init(IContext context)
        {
            _questManager = context.Resolve<QuestManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();

            _eventDispatcher.Unsubscribe(this);
            _eventDispatcher.AddListener<QuestStartedEvent>(QuestStartedHandler);
            _eventDispatcher.AddListener<ObjectiveCompletedEvent>(ObjectiveCompletedHandler);
            _eventDispatcher.AddListener<ObjectiveClaimedEvent>(ObjectiveClaimedHandler);
        }

        public override void DeInit()
        {
            base.DeInit();

            if (_eventDispatcher != null)
            {
                _eventDispatcher.RemoveListener<QuestStartedEvent>(QuestStartedHandler);
                _eventDispatcher.RemoveListener<ObjectiveCompletedEvent>(ObjectiveCompletedHandler);
                _eventDispatcher.RemoveListener<ObjectiveClaimedEvent>(ObjectiveClaimedHandler);
            }
        }

        private void RefreshState()
        {
            var quest = _questManager.GetQuest(BadgesQuestUid);
            var status = 0;

            foreach (var config in quest.GetObjectives())
            {
                if (quest.GetObjectiveState(config.Uid) == ObjectiveState.Done)
                {
                    status++;
                }
            }

            SetNotifier(status, status);
        }


        private void QuestStartedHandler(QuestStartedEvent obj)
        {
            if (obj.Arg0.QuestConfig.Uid != BadgesQuestUid)
                return;

            RefreshState();
        }

        private void ObjectiveCompletedHandler(ObjectiveCompletedEvent obj)
        {
            if (obj.Arg0.QuestConfig.Uid != BadgesQuestUid)
                return;

            RefreshState();
        }

        private void ObjectiveClaimedHandler(ObjectiveClaimedEvent obj)
        {
            if (obj.Arg0.QuestId != BadgesQuestUid)
                return;

            RefreshState();
        }
    }
}