using System;
using Bebopbee.Core.Extensions.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class NotifierWidget : BbbMonoBehaviour
    {
        private static readonly int HighlightAnimationHash = Animator.StringToHash("Highlighted");

        [SerializeField] private Animator _highlightAnimator;

        [SerializeField] private GameObject _visualRoot;
        [SerializeField] private TextMeshProUGUI _notificationText;
        [SerializeField] private bool _boolNotifier;

        [SerializeField] private GameObject _highlightObjectToActive;
        [SerializeField] private GameObject _highlightObjectPrefab;
        [SerializeField] private Transform _highlightRoot;

        [SerializeField] private GameObject[] _objectsToActivate;
        [SerializeField] private GameObject[] _objectsToDeactivate;
        [SerializeField] private Image _image;
        [SerializeField] private Sprite _normalSprite;
        [SerializeField] private Sprite _rewardSprite;
        [SerializeField] private Material _normalMaterial;
        [SerializeField] private Material _rewardMaterial;
        [Serialize<PERSON>ield] private Image _normalBoolNotifierImage;
        [SerializeField] private Image _rewardBoolNotifierImage;

        private INotifierStatus _notifier;

        private bool _isAllowedEnabling = true;
        private bool _enabled;

        private const string BoolNotifierText = "!";
        private const string DefaultNumericNotifierText = "1";
        private const int MaxNotifierCount = 10;
        private const string MaxNotifierCountToDisplay = "9+";

        private void OnValidate()
        {
            ResetNotifierIcons();
            if (_notificationText != null && _normalBoolNotifierImage != null)
            {
                _notificationText.text = DefaultNumericNotifierText;
                _notificationText.enabled = !_boolNotifier;
                _notificationText.fontSharedMaterial = _normalMaterial;

                _normalBoolNotifierImage.enabled = _boolNotifier;
            }

            _image.sprite = _normalSprite;
        }

        public void Init(INotifierStatus notifier)
        {
            if (_notifier == notifier)
                return;

            // forcing update
            _enabled = true;
            SetState(false);

            if (_notifier != null)
            {
                Unsubscribe();
            }

            _notifier = notifier;
            if (gameObject.activeInHierarchy)
            {
                Subscribe();
            }

            UpdateStatus();
        }

        public virtual void UpdateStatus()
        {
            var status = _notifier.GetStatus();
            var rewardStatus = _notifier.GetRewardStatus();
            SetState(status > 0 && _isAllowedEnabling, status, rewardStatus);
        }

        private void SetState(bool enable, int status = 0, int rewardStatus = 0)
        {
            ResetNotifierIcons();

            if (enable && _notificationText != null && _normalBoolNotifierImage != null && _rewardBoolNotifierImage != null)
            {
                var isRewardState = rewardStatus > 0;
                _notificationText.enabled = !_boolNotifier;
                if (!_boolNotifier)
                {
                    if (isRewardState)
                    {
                        _notificationText.text = rewardStatus.ToString();
                        _notificationText.fontSharedMaterial = _rewardMaterial;
                    }
                    else
                    {
                        _notificationText.text = status >= MaxNotifierCount ? MaxNotifierCountToDisplay : status.ToString();
                        _notificationText.fontSharedMaterial = _normalMaterial;
                    }
                }

                _normalBoolNotifierImage.enabled = _boolNotifier && !isRewardState;
                _rewardBoolNotifierImage.enabled = _boolNotifier && isRewardState;
                _image.sprite = isRewardState ? _rewardSprite : _normalSprite;
            }

            if (_enabled == enable)
            {
                return;
            }

            _enabled = enable;

            if (_visualRoot != null)
            {
                _visualRoot.SetActive(enable);
            }

            if (_highlightAnimator != null)
            {
                _highlightAnimator.SetBool(HighlightAnimationHash, enable);
            }

            _objectsToActivate.Enable(enable);
            _objectsToDeactivate.Enable(!enable);

            if (enable && _highlightObjectToActive == null && _highlightObjectPrefab != null)
            {
                _highlightObjectToActive = Instantiate(_highlightObjectPrefab, _highlightRoot);
            }

            if (_highlightObjectToActive != null)
            {
                _highlightObjectToActive.SetActive(enable);
            }
        }

        /// <summary>
        /// Enable or disable ability to change to active visual state (when status>0).
        /// This, however, will not block change to disabled state (when status == 0).
        /// </summary>
        /// <param name="enabledAllowed">Is enabled positive switch.</param>
        /// <remarks>
        /// It may be useful when we need to disable refresh of widget,
        /// when it's still visible.
        /// Used in social tabs notifiers, when we need current tab to not update notifier while it's opened.
        /// It is not possible to use instead the Monobehaviour.enabled property because it will affect running coroutines in widget.
        /// </remarks>
        public void SetEnabledChangeToActiveStatus(bool enabledAllowed)
        {
            _isAllowedEnabling = enabledAllowed;
        }

        protected override void OnEnable()
        {
            if (_notifier == null)
                return;

            Subscribe();
            UpdateStatus();
        }

        private void Subscribe()
        {
            Unsubscribe();

            if (_notifier != null)
            {
                _notifier.StatusUpdated += UpdateStatus;
            }
        }

        private void ResetNotifierIcons()
        {
            if (_notificationText != null)
            {
                _notificationText.enabled = false;
            }

            if (_normalBoolNotifierImage != null)
            {
                _normalBoolNotifierImage.enabled = false;
            }

            if (_rewardBoolNotifierImage != null)
            {
                _rewardBoolNotifierImage.enabled = false;
            }
        }

        protected override void OnDisable()
        {
            Unsubscribe();
        }

        private void Unsubscribe()
        {
            if (_notifier != null)
            {
                _notifier.StatusUpdated -= UpdateStatus;
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Unsubscribe();
        }
    }
}