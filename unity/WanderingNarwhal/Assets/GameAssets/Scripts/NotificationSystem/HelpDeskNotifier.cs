using BBB.DI;
using Cysharp.Threading.Tasks;

namespace BBB
{
    public class HelpDeskNotifier : BaseNotifier
    {
        private IHelpDeskManager _helpDeskManager;
        private int _lastCount;

        public async UniTaskVoid Init(IContext context)
        {
            _helpDeskManager = context.Resolve<IHelpDeskManager>();
            _helpDeskManager.SetUnreadCountChangedHandler(TicketRespondedStateChangedHandler);
            
            var unreadCount = await _helpDeskManager.GetUnreadCountAsync(TicketRespondedStateChangedHandler);
            TicketRespondedStateChangedHandler(unreadCount);
        }
        
        private void TicketRespondedStateChangedHandler(int newCount)
        {
            if (_lastCount == 0 && newCount > 0)
            {
                SetNotifier(newCount);
            }
            else if (newCount > _lastCount)
            {
                IncrementNotifier();
            }
            else if(newCount != 0 && newCount < _lastCount)
            {
                DecrementNotifier();
            }
            else if (newCount == 0)
            {
                ResetNotifier();
            }
            
            _lastCount = newCount;
        }

        public override void ResetNotifiersActions()
        {
            base.ResetNotifiersActions();
            _helpDeskManager.SetUnreadCountChangedHandler(null);
        }
    }
}