using BBB.Core.Social;
using BebopBee.Social;
using BBB.DI;

namespace BBB
{
    public class ReceiveBuddyGiftEventHandler : IPlayerComEventHandler
    {
        private readonly InboxManager _inboxManager;
        
        public ReceiveBuddyGiftEventHandler(IContext context)
        {
            _inboxManager = context.Resolve<InboxManager>();
        }

        public void HandleEvent(PlayerComEventBase playerComEvent)
        {
            var giftEvent = playerComEvent as BuddyGiftReceivedEvent;
            _inboxManager.AddInboxItem(giftEvent);
        }
    }
}