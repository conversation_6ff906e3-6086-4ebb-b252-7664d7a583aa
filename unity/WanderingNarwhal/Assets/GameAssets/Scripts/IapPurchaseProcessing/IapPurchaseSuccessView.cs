using System;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class IapPurchaseSuccessView : BbbMonoBehaviour
    {
        [SerializeField] private Animator _successAnimator;
        [SerializeField] private Button _successButton;

        [SerializeField] private AsyncLoadableImage _icon;
        [SerializeField] private AsyncLoadablePrefab _iconPrefab;

        private Action _closeCallback;
        private static readonly int HideId = Animator.StringToHash("Hide");

        private void Start()
        {
            _successButton.ReplaceOnClick(SuccessButtonClickHandler);
        }

        public void Setup(Action closeCallback)
        {
            _closeCallback = closeCallback;
        }

        public void Show(FBConfig.IAPStoreMarketItemConfig storeMarketItemConfig)
        {
            gameObject.SetActive(true);

            _icon.Hide();
            _iconPrefab.Hide();

            if (!storeMarketItemConfig.Icon.IsNullOrEmpty())
            {
                _icon.Show(storeMarketItemConfig.Icon);
            }
            else if (!storeMarketItemConfig.IconPrefab.IsNullOrEmpty())
            {
                _iconPrefab.Show(storeMarketItemConfig.IconPrefab);
            }
        }

        public void Hide()
        {
            if (gameObject.activeSelf)
            {
                _successAnimator.SetTrigger(HideId);
            }
        }

        public void ForceHide()
        {
            gameObject.SetActive(false);
        }

        private void SuccessButtonClickHandler()
        {
            _closeCallback?.Invoke();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _closeCallback = null;
        }
    }
}