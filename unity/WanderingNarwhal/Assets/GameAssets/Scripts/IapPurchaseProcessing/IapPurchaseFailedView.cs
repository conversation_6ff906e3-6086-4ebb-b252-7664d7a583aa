using System;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class IapPurchaseFailedView : BbbMonoBehaviour
    {
        [SerializeField] private Animator _failedAnimator;
        [SerializeField] private Button _failedButton;

        private Action _closeCallback;
        private static readonly int HideId = Animator.StringToHash("Hide");

        private void Start()
        {
            _failedButton.ReplaceOnClick(FailedButtonClickHandler);
        }

        public void Setup(Action closeCallback)
        {
            _closeCallback = closeCallback;
        }

        public void Show()
        {
            gameObject.SetActive(true);
        }

        public void Hide()
        {
            if (gameObject.activeSelf)
            {
                _failedAnimator.SetTrigger(HideId);
            }
        }

        public void ForceHide()
        {
            gameObject.SetActive(false);
        }

        private void FailedButtonClickHandler()
        {
            _closeCallback?.Invoke();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _closeCallback = null;
        }
    }
}