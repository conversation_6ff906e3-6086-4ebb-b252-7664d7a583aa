using System;
using BBB.Audio;
using BBB.Screens;
using BebopBee;
using BebopBee.Core;
using BebopBee.Core.Audio;
using DG.Tweening;
using Spine;
using Spine.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class IapPurchaseViewPresenter : ModalsViewPresenter, IIapPurchaseViewPresenter
    {
        [SerializeField] private GameObject _dimmerHolder;

        [SerializeField] private SkeletonGraphic _dimmerSpine;

        [SerializeField] private IapPurchaseSuccessView _defaultIapPurchaseSuccessView;

        [SerializeField] private IapPurchaseFailedView _defaultIapPurchaseFailedView;

        [SerializeField] private Transform _customViewsRoot;

        [SerializeField] private Button _invalidateSession;

        [SerializeField] private TextMeshProUGUI _invalidateSessionText;

        private IapPurchaseSuccessView _currentIapPurchaseSuccessView;
        private IapPurchaseFailedView _currentIapPurchaseFailedView;

        private GameObject _successViewPrefab;
        private GameObject _failedViewPrefab;
        private Tweener _debugDelay;

        public event Action OnDimmerVisible = delegate { };

        private void InitViews()
        {
            if (_successViewPrefab != null)
            {
                _currentIapPurchaseSuccessView = Instantiate(_successViewPrefab, _customViewsRoot).GetComponent<IapPurchaseSuccessView>();
                _currentIapPurchaseSuccessView.ForceHide();
            }

            if (_failedViewPrefab != null)
            {
                _currentIapPurchaseFailedView = Instantiate(_failedViewPrefab, _customViewsRoot).GetComponent<IapPurchaseFailedView>();
                _currentIapPurchaseFailedView.ForceHide();
            }

            _currentIapPurchaseSuccessView.Setup(TriggerOnCloseClicked);
            _currentIapPurchaseFailedView.Setup(TriggerOnCloseClicked);

            _invalidateSession.gameObject.SetActive(AppDefinesConverter.BbbDebug);
            _invalidateSession.ReplaceOnClick(OnResetSession);
        }

        private void OnResetSession()
        {
            _invalidateSessionText.text = "Invalidating...";
            _invalidateSession.enabled = false;
            AppController.GetBrainCloudManager().InvalidateSession();
        }

        protected override void OnShow()
        {
            base.OnShow();
            ResetViews();
        }

        public void SetupCustomViews(GameObject successViewPrefab, GameObject failedViewPrefab)
        {
            _successViewPrefab = successViewPrefab;
            _failedViewPrefab = failedViewPrefab;

            InitViews();
        }

        protected override void OnHide()
        {
            base.OnHide();

            _currentIapPurchaseSuccessView.Hide();
            _currentIapPurchaseFailedView.Hide();

            _successViewPrefab = null;
            _failedViewPrefab = null;
            _invalidateSession.gameObject.SetActive(false);
        }

        public void ShowSuccess(FBConfig.IAPStoreMarketItemConfig storeMarketItemConfig)
        {
            AudioProxy.PlaySound(GenericSoundIds.ClaimPopupAppearing);
            _currentIapPurchaseSuccessView.Show(storeMarketItemConfig);
        }

        public void ShowFailed()
        {
            _currentIapPurchaseFailedView.Show();
        }

        public void StartDimmer()
        {
            _dimmerHolder.SetActive(true);

            _dimmerSpine.AnimationState.Complete -= AnimationStateOnComplete;
            _dimmerSpine.AnimationState.Complete += AnimationStateOnComplete;
            _dimmerSpine.AnimationState.SetAnimation(0, "main", true);
        }

        public void StopDimmer()
        {
            _dimmerHolder.SetActive(false);
        }

        private void AnimationStateOnComplete(TrackEntry trackentry)
        {
            // Adding some delay when it's a debug version in order to have some room to invalidate 
            // a session
            if (AppDefinesConverter.BbbDebug)
            {
                if (_debugDelay == null)
                {
                    _debugDelay = Rx.Invoke(0.5f, l =>
                    {
                        _debugDelay = null;
                        OnDimmerVisible();
                    });
                }
            }
            else
            {
                OnDimmerVisible();
            }
        }

        private void ResetViews()
        {
            if (_currentIapPurchaseSuccessView != _defaultIapPurchaseSuccessView)
            {
                if (_currentIapPurchaseSuccessView != null)
                    Destroy(_currentIapPurchaseSuccessView.gameObject);
            }

            if (_currentIapPurchaseFailedView != _defaultIapPurchaseFailedView)
            {
                if (_currentIapPurchaseFailedView != null)
                    Destroy(_currentIapPurchaseFailedView.gameObject);
            }

            _defaultIapPurchaseSuccessView.ForceHide();
            _defaultIapPurchaseFailedView.ForceHide();

            _currentIapPurchaseSuccessView = _defaultIapPurchaseSuccessView;
            _currentIapPurchaseFailedView = _defaultIapPurchaseFailedView;
            _invalidateSession.gameObject.SetActive(AppDefinesConverter.BbbDebug);
            _invalidateSession.enabled = AppDefinesConverter.BbbDebug;
            _invalidateSessionText.text = "Invalidate Session";
            _debugDelay = null;
        }
    }
}