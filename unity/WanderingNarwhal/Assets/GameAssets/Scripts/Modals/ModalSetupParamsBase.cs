namespace BBB
{
    public abstract class ModalSetupParamsBase
    {
        public static bool operator ==(ModalSetupParamsBase left, ModalSetupParamsBase right)
        {
            if (ReferenceEquals(left, null))
            {
                //if both null then true, if only one of then is null then false
                return ReferenceEquals(right, null);
            }
            
            //if only right is null then false
            if (ReferenceEquals(right, null))
                return false;

            //if both are not null use polymorphic equality comparison
            return left.Equals(right);
        }

        public static bool operator !=(ModalSetupParamsBase left, ModalSetupParamsBase right)
        {
            return !(left == right);
        }

        public override bool Equals(object other)
        {
            return other != null && GetType() == other.GetType();
        }
    }
}