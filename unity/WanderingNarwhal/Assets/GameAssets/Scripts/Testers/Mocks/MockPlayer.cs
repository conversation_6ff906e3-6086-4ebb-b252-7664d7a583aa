using System;
using System.Collections.Generic;
using BBB.Player;
using GameAssets.Scripts.Player;
using PBConfig;
using PBGame;
using TeamEventConfig = FBConfig.TeamEventConfig;

namespace BBB.Testers.Mocks
{
    public class MockPlayer : IPlayer
    {
        public bool HasUnlockedPOI(string entity)
        {
            throw new NotImplementedException();
        }

        public PBPlayer PlayerDO { get; }
        public long NextLifeTimestamp { get; set; }
        public double TotalSpendOnIAP { get; }
        public double InstallDate { get; }
        public IInventory Inventory { get; }
        public int MaxLives { get; set; }
        public PBGacha Gacha { get; }
        public IObjectivesProgress ObjectivesProgress { get; }
        public IDictionary<string, PBQuestProgress> QuestProgress { get; }
        public List<string> ExpiredQuests { get; }
        public List<string> CompletedQuests { get; }
        public IDictionary<string, PBMapPlaceable> Placeables { get; }
        public bool IsPayer { get; }
        public bool IsWhale { get; }
        public bool IsSuperWhale { get; }
        public int ButlerStreak { get; set; }
        public int ButlerWinStreak { get; set; }
        public int ButlerLongestWinStreak { get; }
        public List<PBCollectionSet> CollectionSets { get; }
        public IDictionary<string, List<PBCollectionCard>> CollectionCards { get; }
        public IDictionary<string, PBEpisodeSceneProgress> EpisodeScenesProgress { get; }
        public string CurrentEpisodeScene { get; set; }
        public List<string> OpenedEpisodeScenes { get; }
        public int WildCardTokenAmount { get; set; }
        public int WildCardTokenAmountDelta { get; set; }
        public double SaveTime { get; }
        public ISocialInfo SocialInfo { get; }
        public bool IsFBConnectRewardReceived { get; set; }
        public PBWallet Wallet { get; }
        public PBTutorialPersistentData TutorialPersistentData { get; }
        public PBPromotionData PromotionData { get; }
        public long InfiniteLivesEndTimestamp { get; set; }
        public event Action<string> ModalVisited;
        public void AddIAPRecord(string uid, double priceUSD, double timestamp)
        {
            throw new NotImplementedException();
        }

        public void AddNuxStepCompleted(string name)
        {
            throw new NotImplementedException();
        }

        public bool IsNuxStepCompleted(string name)
        {
            throw new NotImplementedException();
        }

        public bool TryGetAdCooldownState(string key, out PlayerAdData adData)
        {
            throw new NotImplementedException();
        }

        public void SetAdCooldownState(string key, PlayerAdData adData)
        {
            throw new NotImplementedException();
        }

        public bool IsVisited(string modalTypeName)
        {
            throw new NotImplementedException();
        }

        public void MarkAsVisited(ModalsType modalType)
        {
            throw new NotImplementedException();
        }

        public void MarkAsVisited(string name)
        {
            throw new NotImplementedException();
        }

        public void CompleteTutorialStep(string step)
        {
            throw new NotImplementedException();
        }

        public bool HasCompletedTutorialStep(string step)
        {
            throw new NotImplementedException();
        }

        public bool HasPurchased(string productUid)
        {
            throw new NotImplementedException();
        }

        public bool HasPurchasedAnyOf(string productFamilyUid)
        {
            throw new NotImplementedException();
        }

        public int GetNumberOfSessions()
        {
            throw new NotImplementedException();
        }

        public int LossStreak { get; }
        public int WinStreak { get; }
        public int FirstTryWinsCount { get; }

        public void TryIncrementFirstTryWinsCount()
        {
            throw new NotImplementedException();
        }
        
        public void ResetLossStreak()
        {
            throw new NotImplementedException();
        }

        public void IncLossStreak()
        {
            throw new NotImplementedException();
        }

        public int LongestWinStreak { get; }
        public void ResetWinStreak()
        {
            throw new NotImplementedException();
        }

        public void IncCurrentWinStreak()
        {
            throw new NotImplementedException();
        }

        public void ResetButlerWinStreak()
        {
            throw new NotImplementedException();
        }

        public void IncrementCurrentButlerWinStreak(int cachedWinStreak)
        {
            throw new NotImplementedException();
        }

        public GameEventStateProxy GameEventStateProxy { get; }

        public GenericEventStateProxy<PBRaceEventState, RaceGameEventConfig> RaceEventStateProxy { get; }
        public GenericEventStateProxy<PBRoyaleEventState, RoyaleGameEventConfig> RoyaleEventStateProxy { get; }
        public GenericEventStateProxy<PBTeamCoopEventState, TeamEventConfig> TeamEventStateProxy { get; }
        public ChallengeTriviaStateProxy ChallengeTriviaStateProxy { get; }
        public SdbStateProxy SdbStateProxy { get; }
        public TeamDataCacheProxy TeamDataCacheProxy { get; }
        public bool SdbMigrated { get; }
        public PassportInfo PassportInfo { get; }
        public void AddWatchedAd()
        {
            throw new NotImplementedException();
        }

        public int GetAdsWatched()
        {
            throw new NotImplementedException();
        }

        public Dictionary<string, int> UsedDailyTriviaUids { get; }
        public bool LifeLifterApplied { get; set; }
        public POIRewardsInfo POIRewardsInfo { get; }
        public int DaysSinceLastVisit { get; set; }
        public string CurrentVersion { get; }
        public string PreviousVersion { get; }
        public int NumberOfCompletedDailyTours { get; set; }
        public PBBasketData BasketData { get; }
        public List<string> NotValidatedVIPWonders { get; }
        public WeeklyLeaderboardState WeeklyLeaderboardState { get; }

        public void UpdateEconomy()
        {
            throw new NotImplementedException();
        }

        public void AddAbTestGroup(string abTest, string group)
        {
            throw new NotImplementedException();
        }

        public void RemoveMissingAbTestGroups(Predicate<string> isPresentPredicate)
        {
            throw new NotImplementedException();
        }

        public void RemoveAbTestGroup(string abTest)
        {
            throw new NotImplementedException();
        }

        public bool ContainsAbTest(string abTest)
        {
            throw new NotImplementedException();
        }

        public string GetActiveGroupFor(string abTest)
        {
            throw new NotImplementedException();
        }

        public bool BelongsToAbTestGroup(string abTest, string group)
        {
            throw new NotImplementedException();
        }

        public IEnumerable<(string, string)> GetActiveAbTestGroups()
        {
            throw new NotImplementedException();
        }

        public Dictionary<string, string> EndlessTreasureProgress { get; }
        public PBDailyLoginData DailyLoginData { get; }
        public List<string> AnsweredSurveys { get; }
        public List<string> InteractedPromotions { get; }
        public Dictionary<string, int> AdminGifts { get; set; }
        public Dictionary<string, double> LastAppliedGiftEntity { get; }
        public long LastSeenMessageTimestamp { get; set; }
        public long LastSeenIceBreakerAnswersTimestamp { get; set; }
        public double GetLastInteractionTimestamp(string system)
        {
            throw new NotImplementedException();
        }

        public void UpdateLastInteractionTimestamp(string system, double timestamp)
        {
            throw new NotImplementedException();
        }

        public int WinRateAfterIapLevels => 0;
        public int WinAfterIapLevels => 0;
        public int AssistUidAfterIapLevels => 0;

        public void SetAssistAfterIap(FBConfig.SystemConfig systemConfig)
        {
        }

        public void DecreaseAssistAfterIap()
        {
        }
        
        public int CollectionCardsCount()
        {
            throw new NotImplementedException();
        }

        public int WildCardTokensCount()
        {
            throw new NotImplementedException();
        }

        public int WildCardsCount()
        {
            throw new NotImplementedException();
        }

        public List<string> PendingToBeExecutedDeepLinks { get; }
        public List<string> PendingToBeConsumedDeepLinks { get; }
        public List<double> LastTeamVsTeamNudges { get; }
        public DailyTaskState DailyTaskState { get; }
        public Dictionary<string, int> ChallengesStartedByPlayerUid { get; }
        public List<string> DiscardedChallengePlayerUids { get; }
        public Dictionary<string, int> ChallengeTriviaSeenTimes { get; }
    }
}