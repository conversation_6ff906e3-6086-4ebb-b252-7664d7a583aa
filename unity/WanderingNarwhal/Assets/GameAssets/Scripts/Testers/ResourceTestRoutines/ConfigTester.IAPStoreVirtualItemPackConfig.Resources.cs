using System.Collections;
using BBB.Core.ResourcesManager;
using FBConfig;
using UnityEngine;

namespace BBB.Testers
{
    public partial class ConfigTesterBehaviour
    {
        private IEnumerator TestIAPStoreVirtualItemPackConfig(IAssetsManager assetsManager)
        {
            const string configName = "IAPStoreVirtualItemPackConfig";
            Debug.LogWarningFormat("Checking {0}", configName);
            var iapStoreVirtualItemPackConfig = _config.Get<IAPStoreVirtualItemPackConfig>();

            var index = 0f;
            foreach (var entry in iapStoreVirtualItemPackConfig.Values)
            {
                var uid = entry.Uid;

                yield return CheckIfAssetIsMissingAsync<Sprite>(assetsManager, entry.Icon, configName, uid, "Icon");
                
                _progressCallback.SafeInvoke(index / iapStoreVirtualItemPackConfig.Values.Count, configName);
                index++;
            }
        }
    }
}