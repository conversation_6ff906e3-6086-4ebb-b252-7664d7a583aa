using BBB;
using BBB.DI;
using BBB.Quests;
using BBB.UI;
using FBConfig;
using GameAssets.Scripts.Core.TimeManager;
using UnityEngine;
using IapManager = BBB.IAP.IapManager;

namespace GameAssets.Scripts.Promotions.Banners
{
    public class IAPBannerView : BannerView
    {
        [SerializeField] private bool _skipClaimModal = false;
        
        [SerializeField] protected string _storeItemUid;
        [SerializeField] private IAPInfo _iapInfo;
        [Space]
        [SerializeField] private GameObject _timerHolder;
        [SerializeField] private UITimerComponent _timer;

        private IapManager _iapManager;
        private IAPStoreMarketItemConfig _storeItemConfig;
        private string _titleLocalizationUid;
        private PromotionManager _promotionManager;
        private TimeManager _timeManager;

        public override string TitleLocalizationUid => _titleLocalizationUid;
        protected BannerManager BannerManager { get; private set; }

        protected override void InitWithContextInternal(IContext context)
        {
            _iapManager = context.Resolve<IapManager>();
            BannerManager = context.Resolve<BannerManager>();
            _promotionManager = context.Resolve<PromotionManager>();
            _timeManager = context.Resolve<TimeManager>();
        }

        public override bool ShouldBeShown()
        {
            // something super weird happens with _initialized state of banners being tru all the time
            // so forcing the init
            InitWithContextInternal(_context);
            return _iapManager != null && _iapManager.Ready;
        }

        protected override void OnSetup()
        {
            _storeItemConfig = _iapManager.GetIapConfig(_storeItemUid);
            if (_iapManager.IsDefaultOrNull(_storeItemConfig))
            {
                Debug.LogError($"IAPStoreMarketItemConfig is missing for {_storeItemUid} on prefab {name}");
                return;
            }

            _titleLocalizationUid = _storeItemConfig.Name;
            _iapInfo.Setup(_storeItemConfig, _iapManager.GetPurchasableItemByConfigId(_storeItemUid));
            InitTimer();
        }

        private void InitTimer()
        {
            var isExpirable = Banner.Promotion.IsExpirable;

            var shouldShowTimer = isExpirable && _timer != null;
            if (_timerHolder != null)
                _timerHolder.SetActive(shouldShowTimer);

            if (isExpirable && _timer != null)
            {
                var expirationTime = Banner.Promotion.GetExpirationTime(_timeManager, _promotionManager);
                _timer.SetupTime(_timeManager, (long)(expirationTime - _timeManager.CurrentTimeStamp()) + 1);
            }
        }

        protected override void ActionButtonHandler()
        {
            BannerManager.LogBannerClickedAnalytics(Banner.Promotion?.Uid);
            BannerManager.DoBannerPurchase(_storeItemUid, Banner.Promotion?.Uid, new IapPurchaseParams()
            {
                SkipClaim = _skipClaimModal,
            });
        }
    }
}