using BBB;
using BBB.DI;
using BebopBee.Core.UI;
using GameAssets.Scripts.IAP.Baskets;
using TMPro;
using UnityEngine;

namespace GameAssets.Scripts.Promotions.Banners
{
    public class BasketBannerView : IAPBannerView
    {
        [SerializeField] private string _basketUid;
        [SerializeField] private TextMeshProUGUI _coinsTotalNumberText;

        private IAPBasketManager _basketManager;

        protected override void InitWithContextInternal(IContext context)
        {
            base.InitWithContextInternal(context);
            _basketManager = context.Resolve<IAPBasketManager>();
        }

        protected override void OnSetup()
        {
            base.OnSetup();
            var basket = _basketManager.GetBasket(_basketUid);
            if (basket == null)
                return;

            _coinsTotalNumberText.text = basket.GetTotalCoins().ToString();
        }

        protected override void ActionButtonHandler()
        {
            BannerManager.LogBannerClickedAnalytics(Banner.Promotion?.Uid);
            BannerManager.DoBasketPurchase(_basketManager.GetBasket(_basketUid), _storeItemUid, Banner.Promotion?.Uid);
        }
    }
}