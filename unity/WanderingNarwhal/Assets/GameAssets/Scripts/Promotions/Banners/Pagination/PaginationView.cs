using System;
using System.Collections.Generic;
using BBB;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Promotions.Banners.Pagination
{
    [RequireComponent(typeof(HorizontalOrVerticalLayoutGroup))]
    public sealed class PaginationView : IPaginationView
    {
        public override event Action<int> ElementClicked;
    
        private int _activeIndex = -1;
        private GoPool _paginationPool;
        private readonly List<PaginationToggleView> _progressElementInstances = new();
    
        public override void Setup(int elementCount, GameObject paginationToggleViewPrefab)
        {
            Release();
            //Prewarm the pagination pool
            _paginationPool ??= new GoPool(paginationToggleViewPrefab, transform, 1);
            for (var i = 0; i < elementCount; i++)
            {
                var paginationToggleView = _paginationPool.Spawn().GetComponent<PaginationToggleView>();
                paginationToggleView.SetActive(false);
                paginationToggleView.Index = i;
                paginationToggleView.Button.onClick.AddListener(() => OnElementClicked(paginationToggleView.Index));
                _progressElementInstances.Add(paginationToggleView);
            }
        }

        public override void SetActiveIndex(int elementIndex)
        {
            if (_activeIndex == elementIndex || elementIndex == -1 || elementIndex > _progressElementInstances.Count - 1)
                return;

            foreach (var toggleView in _progressElementInstances)
            {
                if (toggleView.IsActive)
                {
                    toggleView.SetActive(false);
                }
            }

            _progressElementInstances[elementIndex].SetActive(true);
            _activeIndex = elementIndex;
        }

    
        private void OnElementClicked(int index)
        {
            ElementClicked?.Invoke(index);
        }
    
        public override void Release()
        {
            foreach (var go in _progressElementInstances)
            {
                go.Button.RemoveOnClickListeners();
                _paginationPool.Release(go.gameObject);
            }
            _activeIndex = -1;
            _progressElementInstances.Clear();
        }
    }
}

