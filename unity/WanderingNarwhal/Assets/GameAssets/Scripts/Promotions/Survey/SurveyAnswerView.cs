using System;
using BBB;
using Bebopbee.Core.Extensions.Unity;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Promotions.Survey
{
    public class SurveyAnswerView : BbbMonoBehaviour
    {
        [SerializeField] private GameObject[] _notSelectedHolders;
        [SerializeField] private GameObject[] _selectedHolders;

        [SerializeField] private LocalizedTextPro _answerText;

        [SerializeField] private Button _actionButton;

        private Action<SurveyAnswerView> _selectedCallback;

        public bool IsSelected { get; private set; }

        public SurveyAnswer SurveyAnswer { get; private set; }

        public void Setup(SurveyAnswer surveyAnswer, Action<SurveyAnswerView> selectedCallback)
        {
            SurveyAnswer = surveyAnswer;
            _selectedCallback = selectedCallback;
            _answerText.SetTextId(surveyAnswer.Text);

            _actionButton.ReplaceOnClick(ToggleSelection);
        }

        public void SetSelected(bool selected)
        {
            IsSelected = selected;
            _notSelectedHolders.Enable(!selected);
            _selectedHolders.Enable(selected);
        }

        private void ToggleSelection()
        {
            _selectedCallback?.Invoke(this);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _selectedCallback = null;
        }
    }
}