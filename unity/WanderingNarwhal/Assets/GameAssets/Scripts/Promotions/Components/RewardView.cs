using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.UI;
using BBB.UI.Core;
using BebopBee.Core;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Promotions.Components
{
    public class RewardView : ContextedUiBehaviour
    {
        [SerializeField] private UICurrencyBaseComponent _rewardComponent;
        [SerializeField] private GameObject _giftBoxHolder;

        [SerializeField] private UIRewardComponent _rewardSpeechBubble;
        [SerializeField] private GenericSpeechBubble _speechBubble;
        [SerializeField] private Button _giftBoxSpeechBubbleButton;
        [SerializeField] private float _giftBoxAutoShowSpeechBubbleDelay = 1f;

        [SerializeField] private bool _autoSet = false;
        [SerializeField] private string _reward;
        [SerializeField] private bool _useGiftBox = true;

        private CurrencyIconsLoader _currencyIconsLoader;

        private void Start()
        {
            if (_autoSet)
                SetupReward(_reward);
        }

        private void OnValidate()
        {
            _giftBoxHolder.SetActive(_useGiftBox);
            _rewardComponent.gameObject.SetActive(!_useGiftBox);
        }

        protected override void InitWithContextInternal(IContext context)
        {
            _currencyIconsLoader = context.Resolve<CurrencyIconsLoader>();
            if (_rewardSpeechBubble != null)
                _rewardSpeechBubble.Init(context);
        }

        public virtual void SetupReward(string reward)
        {
            LazyInit();

            _giftBoxSpeechBubbleButton.ReplaceOnClick(GiftBoxClickedHandler);
            _giftBoxHolder.SetActive(false);
            _rewardComponent.gameObject.SetActive(false);

            if (reward.IsNullOrEmpty()) return;
            
            var parsedReward = RewardsUtility.RewardStringToDict(reward).FilterRewards();
            var rewardCount = 0;
            KeyValuePair<string, int> singleReward = default;

            foreach (var kvp in parsedReward)
            {
                if (rewardCount == 0)
                {
                    singleReward = kvp;
                }
                rewardCount++;
            }

            if (rewardCount == 1 && !_useGiftBox)
            {
                _currencyIconsLoader?.LoadAndGetCurrencySpriteAsync(singleReward.Key).ContinueWith(sprite =>
                    _rewardComponent.Setup(sprite, singleReward.Value));

                _rewardComponent.gameObject.SetActive(true);
            }
            else
            {
                _giftBoxHolder.SetActive(true);

                if (_rewardSpeechBubble == null) return;

                _rewardSpeechBubble.SetupReward(parsedReward);
                Rx.Invoke(_giftBoxAutoShowSpeechBubbleDelay, _ =>
                {
                    if (!_speechBubble.IsShown)
                    {
                        _speechBubble.ShowSpeechBubble();
                    }
                });
            }
        }

        private void GiftBoxClickedHandler()
        {
            if (_speechBubble.IsShown)
            {
                _speechBubble.HideSpeechBubble();
                return;
            }

            _speechBubble.ShowSpeechBubble();
        }
    }
}