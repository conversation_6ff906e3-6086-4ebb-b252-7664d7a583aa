using BBB;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Wallet;
using BebopBee.Core.UI;
using GameAssets.Scripts.CurrenciesRewardModalUI;
using GameAssets.Scripts.Promotions.Components;
using GameAssets.Scripts.Promotions.Modal;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Promotions.Announcement
{
    public class AnnouncementPromotionView : PromotionView
    {
        [Space]
        [SerializeField] protected AnnouncementPromotionConfig _config;
        [Space]
        [SerializeField] private LocalizedTextPro _titleText;
        [SerializeField] private LocalizedTextPro _imageText;
        [SerializeField] private Image _image;

        [SerializeField] private Button _rewardButton;
        [SerializeField] private RewardView _rewardView;

        private IWalletManager _walletManager;
        private IUIWalletManager _uiWalletManager;
        protected IUrlOpener URLOpener;
        protected IExternalLinksOpener ExternalLinksOpener;

        private void OnValidate()
        {
            if (_config != null)
            {
                _titleText.SetTextId(_config.Title);
                _imageText.SetTextId(_config.ImageText);
                _image.sprite = _config.Image;
            }
        }

        protected override void OnInitWithContextInternal(IContext context)
        {
            _walletManager = context.Resolve<IWalletManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            URLOpener = context.Resolve<IUrlOpener>();
            ExternalLinksOpener = context.Resolve<IExternalLinksOpener>();
        }

        protected override void OnSetup()
        {
            SetupByConfig();
            _rewardButton.AddOnClick(RewardButtonHandler);
        }

        private void SetupByConfig()
        {
            _titleText.SetTextId(_config.Title);
            _imageText.SetTextId(_config.ImageText);
            _image.sprite = _config.Image;
            if (_rewardView != null)
                _rewardView.SetupReward(_config.Reward);
        }

        private void RewardButtonHandler()
        {
            LogTapEvent();
            MarkAsInteracted();
            CloseButtonHandler();

            RewardButtonAction();
        }

        protected virtual void RewardButtonAction()
        {
            if (!_config.Reward.IsNullOrEmpty())
            {
                var parsedReward = RewardsUtility.RewardStringToDict(_config.Reward).FilterRewards();
                var transaction = new Transaction()
                    .AddTag(TransactionTag.Promotion)
                    .SetAnalyticsData(CurrencyFlow.Promotions.Name, CurrencyFlow.Promotions.WhatsNew, _config.Uid)
                    .Earn(parsedReward);

                _walletManager.TransactionController.MakeTransaction(transaction);
                _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.Promotion);
            }

            ButtonAction();
        }

        protected virtual void ButtonAction()
        {
            if (!_config.Link.IsNullOrEmpty())
            {
                URLOpener.OpenUrl(_config.Link);
            }
        }
    }
}