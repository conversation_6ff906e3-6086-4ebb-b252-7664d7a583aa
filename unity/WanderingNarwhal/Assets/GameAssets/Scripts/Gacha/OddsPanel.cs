using System.Collections.Generic;
using BBB.GameAssets.UI.Gacha.Scripts.Views;
using BBB.Tools;
using BebopBee.UnityEngineExtensions;
using UnityEngine;

namespace BBB.Gacha
{
    public class OddsPanel : BbbMonoBehaviour
    {
        [SerializeField] private GameObject _gachaOddsItem;
        [SerializeField] private Transform _oddsContainer;
        [SerializeField] private PrizesGOFactory _factory;

        private readonly List<GameObject> _instantiatedObjects = new();
        private readonly List<ProbabilityData> _probabilityDataList = new();

        private class ProbabilityData
        {
            public GachaPrizeType GachaPrizeType;
            public int Value;

            public int Weight;
            public Prize Prize;
        }

        public void Setup(ProbWeightedList<Prize> prizeProbabilities)
        {
            foreach (var instantiatedObject in _instantiatedObjects)
            {
                Destroy(instantiatedObject);
            }

            _instantiatedObjects.Clear();
            _probabilityDataList.Clear();

            foreach (var prizeProbability in prizeProbabilities)
            {
                var foundExisting = false;
                foreach (var probabilityData in _probabilityDataList)
                {
                    if (probabilityData.GachaPrizeType != prizeProbability.Type || probabilityData.Value != prizeProbability.Value) continue;
                    
                    probabilityData.Weight += prizeProbability.Weight;
                    foundExisting = true;
                    break;
                }

                if (foundExisting)
                    continue;

                _probabilityDataList.Add(new ProbabilityData()
                {
                    GachaPrizeType = prizeProbability.Type,
                    Value = prizeProbability.Value,
                    Weight = prizeProbability.Weight,
                    Prize = prizeProbability,
                });
            }

            var sumWeight = 0;

            foreach (var item in _probabilityDataList)
            {
                sumWeight += item.Weight;
            }

            foreach (var probabilityData in _probabilityDataList)
            {
                var probabilityPercent = Mathf.RoundToInt((float)probabilityData.Weight / sumWeight * 100f);

                var prizeGo = _factory.Create(probabilityData.Prize, true);
                var gachaOddsItemCmp = ObjectUtils.Instantiate<GachaOddsItem>(_gachaOddsItem, _oddsContainer, false);
                gachaOddsItemCmp.Init(prizeGo, probabilityPercent);

                _instantiatedObjects.Add(gachaOddsItemCmp.gameObject);
            }
        }
    }
}