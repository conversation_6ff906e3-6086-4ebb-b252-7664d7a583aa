using System;
using BBB;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace GameAssets.Scripts.uNode
{
    public class GarbChangeFxController : BbbMonoBehaviour
    {
        private const int WaitCanvasUpdateMilli = 50; 
        private const float SizeScale = 1f;
        private const float StarBlueSizeMin = 20;
        private const float StarBlueSizeMax = 50;
        private const float StarYellowSizeMin = 14;
        private const float StarYellowSizeMax = 150;
        private const float ColorStartSizeMin = 300;
        private const float ColorStartSizeMax = 500;
        private const float HeightMultiplier = 0.4f;
        private const float LowerLimit = 100f;
        private const float MiddleLimit = 200f;
        private const float UpperLimit = 300f;
        private const float LowerMultiplier = 0.5f;
        private const float MiddleMultiplier = 0.7f;
        private const float UpperMultiplier = 1f;
        private const float DefaultMultiplier = 1.1f;
        
        [SerializeField] private ParticleSystem _effectRoot;
        [SerializeField] private RectTransform _rectTransform;
        [SerializeField] private ParticleSystem _blueFx;
        [SerializeField] private ParticleSystem _yellowFx;
        [SerializeField] private ParticleSystem _colorFx;
        [SerializeField] private float _sizeMultiplier;
        [SerializeField] private float _colorRadiusMultiplier;
        [SerializeField] private float _colorSizeMultiplier;

        protected async override void OnEnable()
        {
            base.OnEnable();
            await UniTask.Delay(WaitCanvasUpdateMilli);

            _rectTransform.localScale = Vector3.one;

            var rect = _rectTransform.rect;
            var width = rect.width * SizeScale;
            var height = rect.height * SizeScale;
            var blueShape = _blueFx.shape;
            var yellowShape = _yellowFx.shape;
            var blueMain = _blueFx.main;
            var yellowMain = _yellowFx.main;
            var colorMain = _colorFx.main;

            blueShape.radius = height * HeightMultiplier;
            blueShape.scale = new Vector3(width / height, 1, 1);

            yellowShape.radius = height * HeightMultiplier;
            yellowShape.scale = new Vector3(width / height, 1, 1);

            //these are values found by experimenting to bind scale by size,
            //essentially they come from two points 1 for 300 and 2.5 for 1000
            _colorFx.transform.localScale = new Vector3(width * 0.00214f + 0.357f, height * 0.00214f + 0.357f, 1);

            _sizeMultiplier = blueShape.radius switch
            {
                <= LowerLimit => LowerMultiplier,
                < MiddleLimit => MiddleMultiplier,
                < UpperLimit => UpperMultiplier,
                _ => DefaultMultiplier
            };

            blueMain.startSize =
                new ParticleSystem.MinMaxCurve(StarBlueSizeMin * _sizeMultiplier, StarBlueSizeMax * _sizeMultiplier);
            yellowMain.startSize = new ParticleSystem.MinMaxCurve(StarYellowSizeMin * _sizeMultiplier,
                StarYellowSizeMax * _sizeMultiplier);
            colorMain.startSize = new ParticleSystem.MinMaxCurve(ColorStartSizeMin * _colorFx.transform.localScale.y,
                ColorStartSizeMax * _colorFx.transform.localScale.y);

            _effectRoot.Stop(true, ParticleSystemStopBehavior.StopEmittingAndClear);
            _effectRoot.Play(true);
        }
    }
}
