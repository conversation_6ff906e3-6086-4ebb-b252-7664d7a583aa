using System.Collections.Generic;
using PBGame;
using BBB.Player;
using System;
using BBB;
using GameAssets.Scripts.Player;
using JetBrains.Annotations;
using PBConfig;
using TeamEventConfig = FBConfig.TeamEventConfig;

public interface IPlayer
{
    PBPlayer PlayerDO { get; }
    long NextLifeTimestamp { get; set; }
    double TotalSpendOnIAP { get; }
    double InstallDate { get; }
    IInventory Inventory { get; }
    int MaxLives { get; set; }
    PBGacha Gacha { get; }
    IObjectivesProgress ObjectivesProgress { get; }
    IDictionary<string, PBQuestProgress> QuestProgress { get; }
    List<string> ExpiredQuests { get; }
    List<string> CompletedQuests { get; }
    IDictionary<string, PBMapPlaceable> Placeables { get; }
    bool IsPayer { get; }
    bool IsWhale { get; }
    bool IsSuperWhale { get; }
    int ButlerStreak { get; set; }
    int ButlerWinStreak { get; set; }
    int ButlerLongestWinStreak { get; }
    List<PBCollectionSet> CollectionSets { get; }
    IDictionary<string, List<PBCollectionCard>> CollectionCards { get; }
    IDictionary<string, PBEpisodeSceneProgress> EpisodeScenesProgress { get; }
    string CurrentEpisodeScene { get; set; }
    List<string> OpenedEpisodeScenes { get; }
    int WildCardTokenAmount { get; set; }
    int WildCardTokenAmountDelta { get; set; }

    [UsedImplicitly]
    double SaveTime { get; }
    ISocialInfo SocialInfo { get; }

    bool IsFBConnectRewardReceived { get; set; }
    PBWallet Wallet { get; }
    PBTutorialPersistentData TutorialPersistentData { get; }
    PBPromotionData PromotionData { get; }
    long InfiniteLivesEndTimestamp { get; set; }

    event Action<string> ModalVisited;

    void AddIAPRecord(string uid, double priceUSD, double timestamp);

    void AddNuxStepCompleted(string name);
    bool IsNuxStepCompleted(string name);

    bool TryGetAdCooldownState(string key, out PlayerAdData adData);

    void SetAdCooldownState(string key, PlayerAdData adData);
    bool IsVisited(string modalTypeName);
    void MarkAsVisited(BBB.ModalsType modalType);
    void MarkAsVisited(string name);
    void CompleteTutorialStep(string step);
    bool HasCompletedTutorialStep(string step);
    bool HasPurchased(string productUid);
    bool HasPurchasedAnyOf(string productFamilyUid);

    int GetNumberOfSessions();
    int LossStreak { get; }
    int WinStreak { get; }
    int FirstTryWinsCount { get; }
    void TryIncrementFirstTryWinsCount();
    void ResetLossStreak();
    void IncLossStreak();
    int LongestWinStreak { get; }
    void ResetWinStreak();
    void IncCurrentWinStreak();
    void ResetButlerWinStreak();
    void IncrementCurrentButlerWinStreak(int cachedWinStreak);
    GameEventStateProxy GameEventStateProxy { get; }
    GenericEventStateProxy<PBRaceEventState, RaceGameEventConfig> RaceEventStateProxy { get; }
    GenericEventStateProxy<PBRoyaleEventState, RoyaleGameEventConfig> RoyaleEventStateProxy { get; }
    GenericEventStateProxy<PBTeamCoopEventState, TeamEventConfig> TeamEventStateProxy { get; }
    ChallengeTriviaStateProxy ChallengeTriviaStateProxy { get; }
    SdbStateProxy SdbStateProxy { get; }
    TeamDataCacheProxy TeamDataCacheProxy { get; }
    public bool SdbMigrated { get;}
    PassportInfo PassportInfo { get; }
    void AddWatchedAd();
    int GetAdsWatched();
    Dictionary<string, int> UsedDailyTriviaUids { get; }
    bool LifeLifterApplied { get; set; }
    POIRewardsInfo POIRewardsInfo { get; }
    int DaysSinceLastVisit { get; set; }
    string CurrentVersion { get; }
    string PreviousVersion { get; }
    PBBasketData BasketData { get; }
    List<string> NotValidatedVIPWonders { get; }
    WeeklyLeaderboardState WeeklyLeaderboardState { get; }
    void UpdateEconomy();
    void AddAbTestGroup(string abTest, string group);
    void RemoveMissingAbTestGroups(Predicate<string> isPresentPredicate);
    void RemoveAbTestGroup(string abTest);
    bool ContainsAbTest(string abTest);
    string GetActiveGroupFor(string abTest);
    bool BelongsToAbTestGroup(string abTest, string group);
    IEnumerable<(string, string)> GetActiveAbTestGroups();
    Dictionary<string, string> EndlessTreasureProgress { get; }
    PBDailyLoginData DailyLoginData { get; }
    List<string> AnsweredSurveys { get; }
    List<string> InteractedPromotions { get; }
    Dictionary<string, int> AdminGifts { get; set; }
    public Dictionary<string, double> LastAppliedGiftEntity { get; }
    long LastSeenMessageTimestamp { get; set; }
    long LastSeenIceBreakerAnswersTimestamp {get; set;}

    /// <summary>
    /// Returns -1 if non interacted
    /// </summary>
    /// <param name="system"></param>
    /// <returns></returns>
    double GetLastInteractionTimestamp(string system);

    void UpdateLastInteractionTimestamp(string system, double timestamp);

    int WinRateAfterIapLevels { get; }
    int WinAfterIapLevels { get; }
    int AssistUidAfterIapLevels { get; }
    void SetAssistAfterIap(FBConfig.SystemConfig systemConfig);
    void DecreaseAssistAfterIap();

    int CollectionCardsCount();
    int WildCardTokensCount();
    int WildCardsCount();

    List<string> PendingToBeExecutedDeepLinks { get; }
    List<string> PendingToBeConsumedDeepLinks { get; }
    List<double> LastTeamVsTeamNudges { get; }
    DailyTaskState DailyTaskState { get; }
    Dictionary<string, int> ChallengesStartedByPlayerUid { get; }
    List<string> DiscardedChallengePlayerUids { get; }
    Dictionary<string, int> ChallengeTriviaSeenTimes { get; }
}