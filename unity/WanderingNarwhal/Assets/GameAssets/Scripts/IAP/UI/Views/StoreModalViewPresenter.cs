using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Screens;
using BBB.Store;
using BBB.Tools;
using BBB.UI.IAP.Controllers;
using BBB.UI.IAP.Views.Cells;
using BBB.Wallet;
using BebopBee.Core.Audio;
using Bebopbee.Core.Extensions.Unity;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using FBConfig;
using GameAssets.Scripts.Promotions;
using GameAssets.Scripts.Promotions.Pack;
using UnityEngine;
using UnityEngine.UI;
using IAPStoreCategoryConfig = FBConfig.IAPStoreCategoryConfig;

namespace BBB.UI.IAP.Views
{
    public class StoreModalViewPresenter : ModalsViewPresenter, IStoreModalViewPresenter
    {
        private const string VipUid = "vip";
        private const string PromotionCarouselUid = "PromotionCarousel";
        private const string StartCarouselUid = "StartCarousel";
        private const string EndCarouselUid = "EndCarousel";

        public event Action<IAPStoreCategoryConfig> CategorySelected;
        public event Action<StoreItem> ItemBought;
        public event Action<IapPromotionView> IapPromotionBought;
        public event Action<StorePackItem, int> PackBought;
        public event Action ShowMoreRequested;
        public event Action<StoreCategory> WalletWidgetClicked;

        [Space]
        [SerializeField] private ScrollRect _scrollRect;
        [SerializeField] private RectTransform _widthReference;
        [SerializeField] private RectTransform _itemsRoot;
        [SerializeField] private StoreGrid _storeGrid;

        [SerializeField] private StoreItemsFactory _itemsFactory;

        [Space]
        [SerializeField] private Ease _scrollToEase;
        [SerializeField] private float _scrollToDuration;
        [SerializeField] private float _scrollToDelay;

        [Space]
        [SerializeField] private LocalizedTextPro _titleText;
        [SerializeField] private GameObject _wheelHolder;
        [SerializeField] private GameObject[] _nonVipHolders;
        [SerializeField] private GameObject[] _vipHolders;
        [SerializeField] private LightweightWalletWidget[] _walletWidgets;

        [Space]
        [SerializeField] private IAPStoreCategoryGroupManager _categoryGroupManager;
        [SerializeField] private Transform _categoryTabContainer;
        [SerializeField] private GameObject _categoryButtonPrefab;
        [SerializeField] private GameObject _tabsHolder;

        [SerializeField] private GameObject _vipStoreInfo;
        [SerializeField] private GameObject _freeSpinInfoModal;

        private readonly List<GameObject> _manuallySpawnedItems = new();
        private GenericResourceProvider _genericResourceProvider;
        private GameNotificationManager _notificationManager;
        private IWalletManager _walletManager;
        private PromotionManager _promotionManager;
        private IAPCarousel _currentCarousel;

        private readonly Dictionary<string, IapCategoryView> _categoryTabButtons = new();
        private IDictionary<string, VIPProductsConfg> _vipProductsConfig;

        private readonly Dictionary<string, GameObject> _currentItems = new();

        private TweenerCore<float, float, FloatOptions> _scrollTweener;
        private bool _isScrolling;

        protected override void OnContextInitialized(IContext context)
        {
            _vipProductsConfig = context.Resolve<IConfig>().Get<VIPProductsConfg>();
            _genericResourceProvider = context.Resolve<GenericResourceProvider>();
            _notificationManager = context.Resolve<GameNotificationManager>();
            _walletManager = context.Resolve<IWalletManager>();
            _promotionManager = context.Resolve<PromotionManager>();

            _walletWidgets.Map(widget =>
            {
                widget.Init(context);
                var button = widget.GetComponent<Button>();
                if (button != null)
                {
                    button.ReplaceOnClick(() => { WalletWidgetClicked?.Invoke(widget.StoreCategory); });
                }
            });
        }

        protected override void OnShow()
        {
            base.OnShow();
            _freeSpinInfoModal.SetActive(false);
        }

        protected override void OnHide()
        {
            base.OnHide();
            ResetCurrentItems();
        }

        public void FillCategories(List<IAPStoreCategoryConfig> categories)
        {
            _categoryTabButtons.Clear();
            _categoryGroupManager.ResetCategoryButtons();

            var visibleCategories = 0;
            foreach (var categoryConfig in categories)
            {
                if (!categoryConfig.Visible)
                    continue;

                visibleCategories++;

                IapCategoryView categoryView;
                if (_categoryTabContainer.childCount >= visibleCategories)
                {
                    categoryView = _categoryTabContainer.GetChild(visibleCategories - 1).GetComponent<IapCategoryView>();
                    categoryView.gameObject.SetActive(true);
                    categoryView.SelectButton.onClick.RemoveAllListeners();
                }
                else
                {
                    UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{_categoryButtonPrefab.name}]");
                    categoryView = ObjectUtils.Instantiate<IapCategoryView>(_categoryButtonPrefab, _categoryTabContainer, false);
                    UnityEngine.Profiling.Profiler.EndSample();
                }

                var tabName = Localization.getLocalizedText(categoryConfig.TabName);
                categoryView.Init(_genericResourceProvider, categoryConfig.Icon, tabName, false, _notificationManager.GetNotifierByStoreCategory(categoryConfig.Uid));
                var config = categoryConfig;
                categoryView.SelectButton.onClick.AddListener(() =>
                {
                    if (_isScrolling)
                        return;

                    CategorySelected?.Invoke(config);
                    _categoryGroupManager.CategoryButtonPressed(config.Uid);
                    AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
                });

                _categoryGroupManager.RegisterCategoryButton(categoryConfig.Uid, categoryView.SelectButton);
                _categoryTabButtons.Add(categoryConfig.Uid, categoryView);
            }

            for (var i = _categoryTabContainer.childCount - 1; i >= visibleCategories; --i)
            {
                var child = _categoryTabContainer.GetChild(i);
                child.gameObject.SetActive(false);
            }
        }

        public void SelectCategory(IAPStoreCategoryConfig category)
        {
            SetupTabButtons(category);
            _wheelHolder.SetActive(!category.PromotionUid.IsNullOrEmpty());
            var isVipUid = category.Uid == VipUid;
            _nonVipHolders.Enable(!isVipUid);
            _vipHolders.Enable(isVipUid);
            if (_titleText != null)
            {
                _titleText.SetTextId(category.Title);
            }
        }

        public void SetupTabsVisibility(bool shouldTabsBeVisible)
        {
            _tabsHolder.SetActive(shouldTabsBeVisible);
        }

        private void SetupTabButtons(IAPStoreCategoryConfig category)
        {
            foreach (var pair in _categoryTabButtons)
            {
                if (category.Uid == pair.Key)
                {
                    pair.Value.Select();
                }
                else
                {
                    pair.Value.Deselect();
                }
            }

            _categoryGroupManager.CategoryButtonPressed(category.Uid);
        }

        private readonly List<(Promotion Promotion, IapPromotionView IapPromotionView)> _promotionsWithIap = new();

        public void FillItems(List<StoreItem> storeItems, string itemUidToScrollTo, bool focusInstantly, bool resetCurrentItems)
        {
            if (resetCurrentItems)
            {
                ResetCurrentItems();
            }

            _itemsFactory.ReleaseAll();
            ResetManualItems();
            _currentItems.Clear();
            _currentCarousel = null;

            foreach (var storeItem in storeItems)
            {
                if (storeItem == null)
                {
                    Debug.LogError("Store item is null");
                    continue;
                }

                switch (storeItem.Uid)
                {
                    case PromotionCarouselUid:
                    {
                        var promotionsWithIap = _promotionManager.GetActivePromotionsWithIap();

                        if (promotionsWithIap.Count > 0)
                        {
                            var useCarousel = promotionsWithIap.Count > 1;
                            IAPCarousel promotionCarousel = null;

                            _promotionsWithIap.Clear();
                            foreach (var promotion in promotionsWithIap)
                            {
                                var iapPrefab = promotion.GetIapPrefab();
                                if (iapPrefab == null) continue;

                                var view = iapPrefab.GetComponent<IapPromotionView>();
                                if (view != null)
                                {
                                    _promotionsWithIap.Add((promotion, view));
                                }
                            }

                            if (useCarousel)
                            {
                                var promotionCarouselGameObject = _itemsFactory.Create(storeItem.Prefab);
                                promotionCarouselGameObject.transform.SetParent(_itemsRoot, false);
                                promotionCarouselGameObject.SetActive(true);

                                promotionCarousel = promotionCarouselGameObject.GetComponent<IAPCarousel>();
                                promotionCarousel.Setup(_widthReference, _itemsRoot);
                            }

                            var sortedPromotions = new List<(Promotion Promotion, IapPromotionView IapPromotionView)>(_promotionsWithIap);
                            sortedPromotions.Sort((x, y) => x.IapPromotionView.SortOrder.CompareTo(y.IapPromotionView.SortOrder));

                            foreach (var tuple in sortedPromotions)
                            {
                                var go = Instantiate(tuple.IapPromotionView.gameObject, _itemsRoot);
                                var view = go.GetComponent<IapPromotionView>();
                                view.Setup(tuple.Promotion, () => IapPromotionBought?.Invoke(view));

                                if (useCarousel)
                                {
                                    promotionCarousel.AddChild(go);
                                }
                                else
                                {
                                    _manuallySpawnedItems.Add(go);
                                }
                            }


                            if (useCarousel)
                            {
                                promotionCarousel.FinishSetup();
                            }
                        }

                        continue;
                    }
                    // for carousel end we return base root
                    case EndCarouselUid:
                    {
                        if (_currentCarousel != null)
                        {
                            _currentCarousel.FinishSetup();
                        }

                        _currentCarousel = null;
                        continue;
                    }
                }

                if (storeItem.Prefab.IsNullOrEmpty())
                {
                    Debug.LogError($"Store item have no prefab assigned. itemUid={storeItem.Uid}");
                    continue;
                }

                var itemGameObject = _itemsFactory.Create(storeItem.Prefab);
                if (itemGameObject == null)
                {
                    Debug.LogError($"Couldn't create instance from prefab {storeItem.Prefab}, uid={storeItem.Uid}");
                    continue;
                }

                itemGameObject.transform.SetParent(_itemsRoot, false);
                itemGameObject.SetActive(true);

                if (_currentCarousel != null)
                {
                    _currentCarousel.AddChild(itemGameObject);
                }
                else
                {
                    _currentItems.Add(storeItem.Uid.Trim().ToLowerInvariant(), itemGameObject);
                }

                // for carousel start we set custom root
                if (storeItem.Uid == StartCarouselUid)
                {
                    var iapCarousel = itemGameObject.GetComponent<IAPCarousel>();
                    if (iapCarousel != null)
                    {
                        iapCarousel.Setup(_widthReference, _itemsRoot);
                        _currentCarousel = iapCarousel;
                    }
                    else
                    {
                        Debug.LogError($"Couldn't get IAPCarousel from prefab: {storeItem.Prefab}, uid={storeItem.Uid}");
                    }

                    continue;
                }

                var closableAction = itemGameObject.GetComponent<IClosableStoreAction>();
                closableAction?.SetupStoreCloseAction(TriggerOnCloseClicked);

                var storeItemView = itemGameObject.GetComponent<IStoreItemView>();

                if (storeItemView == null)
                    continue;

                InitStoreItemView(storeItemView, storeItem);
            }

            _storeGrid.Refresh();

            if (itemUidToScrollTo.IsNullOrEmpty() || !_currentItems.TryGetValue(itemUidToScrollTo, out var item)) return;

            if (focusInstantly)
            {
                ScrollToItemInstantly(item.transform.GetSiblingIndex());
            }
            else
            {
                StartCoroutine(ScrollToItem(item.transform.GetSiblingIndex(), resetCurrentItems ? _scrollToDelay : 0f, _scrollToDuration));
            }
        }

        private void InitStoreItemView(IStoreItemView storeItemView, StoreItem storeItem)
        {
            storeItemView.SetStoreItem(storeItem);

            switch (storeItem)
            {
                case Store.GenericStoreItem:
                {
                    var genericStoreItemView = storeItemView as GenericStoreItem;
                    if (genericStoreItemView == null)
                        return;

                    genericStoreItemView.OnClick -= BuyGenericItemHandler;
                    genericStoreItemView.OnClick += BuyGenericItemHandler;
                    break;
                }
                default:
                    storeItemView.OnClick -= BuyItemHandler;
                    storeItemView.OnClick += BuyItemHandler;
                    break;
            }
        }

        private void ScrollToItemInstantly(int scrollToIndex)
        {
            _isScrolling = false;
            _scrollTweener = null;

            var item = _itemsRoot.transform.GetChild(scrollToIndex);
            var endValue = (Math.Abs(item.RectTransform().anchoredPosition.y) - item.RectTransform().sizeDelta.y / 2) /
                           (_itemsRoot.RectTransform().sizeDelta.y - _scrollRect.viewport.rect.height);

            _scrollRect.verticalNormalizedPosition = 1 - endValue;
        }

        private IEnumerator ScrollToItem(int scrollToIndex, float delay, float duration)
        {
            _isScrolling = true;
            yield return null;

            var item = _itemsRoot.transform.GetChild(scrollToIndex);
            var endValue = (Math.Abs(item.RectTransform().anchoredPosition.y) - item.RectTransform().sizeDelta.y / 2) /
                           (_itemsRoot.RectTransform().sizeDelta.y - _scrollRect.viewport.rect.height);
            endValue = Math.Min(1, Math.Max(0, endValue));

            _scrollTweener = _scrollRect.ScrollVerticalToNormalizedPosition(1 - endValue, duration, () =>
            {
                _isScrolling = false;
                _scrollTweener = null;
            }, _scrollToEase, delay);
        }

        private void ResetCurrentItems()
        {
            _itemsFactory.ReleaseAll();
            _currentItems.Clear();
            ResetManualItems();
            _scrollTweener?.Kill();
            _isScrolling = false;

            _scrollRect.normalizedPosition = Vector2.zero;
            _scrollRect.velocity = Vector2.zero;
        }

        private void ResetManualItems()
        {
            // moving out of hierarchy as destroy doesn't happen instantly
            foreach (var item in _manuallySpawnedItems)
            {
                item.gameObject.SetActive(false);
                item.transform.SetParent(null);
                Destroy(item);
            }

            _manuallySpawnedItems.Clear();
        }

        private void BuyGenericItemHandler(StoreItem storeItem)
        {
            //For now, this is the only action allowed, but we may add more in the future
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
            ShowMoreRequested?.Invoke();
        }

        private void BuyItemHandler(StoreItem storeItem)
        {
            if (storeItem is StoreVIPItem)
            {
                var config = _vipProductsConfig[storeItem.Uid];

                var transaction = new Transaction().Spend(WalletCurrencies.VipCurrency, config.Price);
                if (!_walletManager.TransactionController.CanMakeTransaction(transaction))
                {
                    _vipStoreInfo.SetActive(true);
                    return;
                }
            }

            ItemBought?.Invoke(storeItem);
        }

        public void StopScrollingToItem()
        {
            _scrollTweener?.Kill();
            _isScrolling = false;
        }
    }
}