using System;
using BBB.Audio;
using BBB.DI;
using BBB.Store;
using BBB.UI.Core;
using BebopBee.Core.Audio;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI
{
    public class GenericStoreItem : ContextedUiBehaviour, IStoreItemView
    {
        public event Action<StoreItem> OnClick;

        [SerializeField] private Button _button;
        private StoreItem _storeItem;

        protected override void InitWithContextInternal(IContext context)
        {
        }

        public void SetStoreItem(StoreItem storeItem)
        {
            _storeItem = storeItem;
            _button.ReplaceOnClick(ButtonHandler);
            LazyInit();
        }

        private void ButtonHandler()
        {
            OnClick?.Invoke(_storeItem);
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
        }
    }
}