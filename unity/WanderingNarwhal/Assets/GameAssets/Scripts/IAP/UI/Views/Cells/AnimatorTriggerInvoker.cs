using System;
using UnityEngine;
using BebopBee.Core;

namespace BBB.UI
{
    public class AnimatorTriggerInvoker : BbbMonoBehaviour
    {
        [SerializeField] private Animator _animator;
        [SerializeField] private string _triggerName;
        [SerializeField] private float _period = 3f;
        [SerializeField] private bool _definePeriodRandomly = true;
        [SerializeField] private float _minPeriod = 2f;
        [SerializeField] private float _maxPeriod = 4f;
        private IDisposable _triggerInvoker;

        protected override void OnEnable()
        {
            if (_definePeriodRandomly)
            {
                _period = UnityEngine.Random.Range(_minPeriod, _maxPeriod);
            }

            var initialDelay = UnityEngine.Random.Range(0f, (_minPeriod + _maxPeriod) / 2);
            _triggerInvoker = Rx.InvokeRepeating(initialDelay, _period, _ => InvokeTrigger());
        }

        private void InvokeTrigger()
        {
            _animator.SetTrigger(_triggerName);
        }

        protected override void OnDisable()
        {
            _triggerInvoker?.Dispose();
            _triggerInvoker = null;
        }
    }
}