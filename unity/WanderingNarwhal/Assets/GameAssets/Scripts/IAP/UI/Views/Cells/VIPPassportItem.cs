using System;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.UI.Core;
using BebopBee.Core.UI;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI
{
    public class VIPPassportItem : ContextedUiBehaviour, IClosableStoreAction
    {
        [SerializeField] private Button _passportButton;
        
        private Action _closeStoreAction;
        private IModalsBuilder _modalsBuilder;
        
        protected override void InitWithContextInternal(IContext context)
        {
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            Setup();
        }
        
        private void Setup()
        {
            _passportButton.ReplaceOnClick(OpenPassportModalButtonHandler);
        }

        private void OpenPassportModalButtonHandler()
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnHud.Name, DauInteractions.TapOnHud.PassportVIP, string.Empty));
            var passportModalController = _modalsBuilder.CreateModalView<PassportModalController>(ModalsType.Passport);
            passportModalController.Setup(PassportTab.Info);
            passportModalController.ShowModal(ShowMode.Delayed);
            CloseShop();
        }

        private void CloseShop()
        {
            _closeStoreAction?.Invoke();
            _closeStoreAction = null;
        }
        
        public void SetupStoreCloseAction(Action closeStoreAction)
        {
            _closeStoreAction = closeStoreAction;
        }
    }
}