
using UnityEngine.Purchasing;

namespace BBB
{
    public class IapPurchaseData
    {
        public Product Product { get; }
        public bool Success { get; }
        public bool Duplicated { get; }

        public double TotalUsdPrice { get; }

        public IapPurchaseData(Product product, bool success, bool duplicated, double totalUsdPrice)
        {
            Product = product;
            Success = success;
            Duplicated = duplicated;
            TotalUsdPrice = totalUsdPrice;
        }
    }
}