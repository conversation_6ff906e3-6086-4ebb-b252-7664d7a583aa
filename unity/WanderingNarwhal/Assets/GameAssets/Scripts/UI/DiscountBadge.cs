using BBB;
using TMPro;
using UnityEngine;
using Bebopbee.Core.Extensions.Unity;

namespace UI
{
    public class DiscountBadge : BbbMonoBehaviour
    {
        [SerializeField] private Animator _animator;
        [SerializeField] private TextMeshProUGUI _discountText;

        private const string DiscountLocalizationId = "DISCOUNT_PERCENTAGE_OFF";

        public void SetDiscount(string discount, string title, bool animate = false)
        {
            if (discount.IsNullOrEmpty())
            {
                gameObject.SetActive(false);
                return;
            }
            

            if (!animate)
            {
                _animator.Rebind();
                _animator.enabled = false;
            }
            else if (!_animator.enabled)
            {
                _animator.enabled = true;
            }

            if (title.IsNullOrEmpty())
                title = DiscountLocalizationId;
            
            _discountText.SetTextAndResizeFont(LocalizationManager.GetLocalizedTextWithArgs(title, discount));
            gameObject.SetActive(true);
        }
    }
}