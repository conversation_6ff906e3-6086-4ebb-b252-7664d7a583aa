using System;
using System.Collections.Generic;
using BBB;
using BBB.Controller;
using BBB.Core;
using BBB.DI;
using BBB.Modals;
using BBB.UI;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.UI.OverlayDialog
{
    public enum DisplayType
    {
        RewardSpeechBubble,
        FloatingText,
        EpisodeScreenBubble
    }

    [Serializable]
    public class OverlayDialogConfig
    {
        [SerializeField] public Transform TargetTransform;
        [SerializeField] public RewardInfoController.Direction Direction;
        [SerializeField] public bool AutoHide;
        [SerializeField] public DisplayType DisplayType;
        [SerializeField] public bool ShowBackground;
        [SerializeField] public string TextToDisplay;
        [SerializeField] public bool IsMatch3Specific;
        [SerializeField] public bool DisableClickToHide;
        public IDictionary<string, int> RewardToDisplay = new Dictionary<string, int>();
        public object[] TextArgs = Array.Empty<object>();
    }

    public class OverlayDialogManager : BbbMonoBehaviour, IOverlayDialogManager
    {
        [SerializeField] private int _initialPoolSize;
        [SerializeField] private Button _closeButton;
        [SerializeField] private CanvasGroup _canvasGroup;

        private IScreensManager _screensManager;
        private IModalsManager _modalsManager;
        private IEventDispatcher _eventDispatcher;
        private GenericResourceProvider _genericResourceProvider;
        private PopupManager _popupManager;
        private IContext _context;
        private Camera _levelCamera;

        private readonly List<(Transform, IOverlayDialogController)> _controllers = new();

        private readonly Dictionary<DisplayType, GoPool> _overlayDialogPools = new();

        private readonly Dictionary<Transform, (IOverlayDialogController controller, DisplayType displayType)> _shownOverlayDialogs = new();

        private readonly Dictionary<DisplayType, string> _prefabMapping = new(3)
        {
            { DisplayType.RewardSpeechBubble, GenericResKeys.SpeechBubblePrefab },
            { DisplayType.FloatingText, GenericResKeys.FloatingTextPrefab },
            { DisplayType.EpisodeScreenBubble, GenericResKeys.FloatingTextEpisodePrefab }
        };

        public void Init(IContext context)
        {
            _context = context;
            _screensManager = context.Resolve<IScreensManager>();
            _modalsManager = context.Resolve<IModalsManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _genericResourceProvider = context.Resolve<GenericResourceProvider>();
            _popupManager = context.Resolve<PopupManager>();
            _canvasGroup.blocksRaycasts = false;
            _closeButton?.ReplaceOnClick(HideAllOverlayDialogs);
            PreparePools();
            Subscribe();
        }

        private void PreparePools()
        {
            foreach (var (displayType, prefabName) in _prefabMapping)
            {
                var prefab = _genericResourceProvider.GetPreloaded<GameObject>(prefabName);

                if (prefab == null)
                {
                    BDebug.LogError(LogCat.General,
                        $"OverlayDialogManager: Prefab not found in context.");
                    continue;
                }

                _overlayDialogPools[displayType] = new GoPool(prefab, transform, _initialPoolSize, instance =>
                {
                    var overlayDialogController = instance.GetComponent<IOverlayDialogController>();
                    if (overlayDialogController == null)
                    {
                        BDebug.LogError(LogCat.General,
                            $"OverlayDialogManager: IOverlayDialogController not found on prefab.");
                    }
                    else
                    {
                        overlayDialogController.Init(_context);
                    }
                });
            }
        }

        private void Subscribe()
        {
            Unsubscribe();

            _screensManager.OnScreenChangingStarted += HideAllOverlayDialogs;
            _modalsManager.ModalShown += HideAllOverlayDialogs;
            _modalsManager.ModalHidden += HideAllOverlayDialogs;

            _eventDispatcher.AddListener<LevelCameraInitialized>(LevelCameraInitializedHandler);
        }

        private void Unsubscribe()
        {
            if (_screensManager != null)
            {
                _screensManager.OnScreenChangingStarted -= HideAllOverlayDialogs;
            }

            if (_modalsManager != null)
            {
                _modalsManager.ModalShown -= HideAllOverlayDialogs;
                _modalsManager.ModalHidden -= HideAllOverlayDialogs;
            }

            _eventDispatcher?.RemoveListener<LevelCameraInitialized>(LevelCameraInitializedHandler);
        }

        private void LevelCameraInitializedHandler(LevelCameraInitialized obj)
        {
            _levelCamera = obj.Arg0;
        }

        private void HideAllOverlayDialogs(IController obj)
        {
            HideAllOverlayDialogsFast();
        }

        private void HideAllOverlayDialogs(ScreenType arg1, IScreensController arg2)
        {
            HideAllOverlayDialogsFast();
        }

        public IOverlayDialogController GetOverlayDialog(Transform targetTransform)
        {
            return _shownOverlayDialogs.TryGetValue(targetTransform, out var overlayDialog) ? overlayDialog.controller : null;
        }

        private IOverlayDialogController GetOrCreateOverlayDialog(Transform targetTransform, OverlayDialogConfig config)
        {
            HideAllOverlayDialogsExcept(targetTransform);

            if (config.IsMatch3Specific)
            {
                _popupManager.SetCanvasToScreenSpaceCamera(_levelCamera);
            }

            if (!_shownOverlayDialogs.TryGetValue(targetTransform, out var overlayDialog))
            {
                var pool = _overlayDialogPools[config.DisplayType];
                var controller = pool.Spawn().GetComponent<IOverlayDialogController>();
                _shownOverlayDialogs[targetTransform] = (controller, config.DisplayType);
                controller.Destroyed += RemoveFromShownAndReleaseToPool;
                return controller;
            }

            return overlayDialog.controller;
        }

        public void ToggleOverlayDialog(OverlayDialogConfig overlayDialogConfig)
        {
            var targetTransform = overlayDialogConfig.TargetTransform;
            if (targetTransform == null)
            {
                return;
            }

            var overlayDialog = GetOrCreateOverlayDialog(targetTransform, overlayDialogConfig);
            overlayDialog.ToggleOverlayDialog(overlayDialogConfig, OnHideComplete);
            _canvasGroup.blocksRaycasts = !overlayDialogConfig.DisableClickToHide;
        }

        public void ShowOverlayDialog(OverlayDialogConfig overlayDialogConfig)
        {
            var targetTransform = overlayDialogConfig.TargetTransform;
            if (targetTransform == null)
            {
                return;
            }

            var overlayDialog = GetOrCreateOverlayDialog(targetTransform, overlayDialogConfig);
            overlayDialog.ShowOverlayDialog(overlayDialogConfig, OnHideComplete);
            _canvasGroup.blocksRaycasts = !overlayDialogConfig.DisableClickToHide;
        }

        private void PrepareToHide()
        {
            _controllers.Clear();
            foreach (var item in _shownOverlayDialogs)
            {
                _controllers.Add((item.Key, item.Value.controller));
            }
        }

        private void ForEachVisible(Action<Transform, IOverlayDialogController> action)
        {
            PrepareToHide();
            foreach (var (targetTransform, controller) in _controllers)
            {
                action(targetTransform, controller);
            }
        }

        public void HideAllOverlayDialogs() => ForEachVisible((_, ctrl) => ctrl.HideRewardInfo(OnHideComplete));

        private void HideAllOverlayDialogsFast() =>
            ForEachVisible((_, ctrl) => ctrl.HideRewardInfo(OnHideComplete, true));

        private void HideAllOverlayDialogsExcept(Transform keep) => ForEachVisible((targetTransform, ctrl) =>
        {
            if (targetTransform != keep)
            {
                ctrl.HideRewardInfo(OnHideComplete);
            }
        });

        private void OnHideComplete(IOverlayDialogController overlayDialogController, Transform targetTransform)
        {
            _popupManager.SetCanvasToScreenSpaceOverlay();
            _canvasGroup.blocksRaycasts = false;
            RemoveFromShownAndReleaseToPool(targetTransform, overlayDialogController);
        }

        private void RemoveFromShownAndReleaseToPool(Transform targetTransform, IOverlayDialogController overlayDialogController)
        {
            if (_shownOverlayDialogs.Remove(targetTransform, out var overlayDialog) &&
                overlayDialogController is MonoBehaviour objectToRelease)
            {
                overlayDialogController.Destroyed -= RemoveFromShownAndReleaseToPool;
                var pool = _overlayDialogPools[overlayDialog.displayType];
                pool.Release(objectToRelease.gameObject);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Unsubscribe();
            
            foreach (var (controller, _) in _shownOverlayDialogs.Values)
            {
                controller.Destroyed -= RemoveFromShownAndReleaseToPool;
            }

            foreach (var pool in _overlayDialogPools.Values)
            {
                pool.Cleanup();
            }
            _overlayDialogPools.Clear();
            _shownOverlayDialogs.Clear();
        }
    }
}