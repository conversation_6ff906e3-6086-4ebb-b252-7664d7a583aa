using System.Collections.Generic;
using System.Text;
using BBB;
using BBB.Wallet;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace GameAssets.Scripts.UI.OverlayDialog
{
    public class SpeechBubbleTest : BbbMonoBehaviour
    {
        [SerializeField] private string _rewardText;
        [SerializeField] private int _maxRewardsToShow = 9;
        [SerializeField] private int _rewardValue = 10;
        [SerializeField] private Button _button;
        [SerializeField] private TextMeshProUGUI _text;
        [FormerlySerializedAs("_speechBubbleConfig")] [SerializeField] private OverlayDialogConfig _overlayDialogConfig;
        private IOverlayDialogManager _overlayDialogManager;
        private readonly StringBuilder _stringBuilder = new();

        private void OnValidate()
        {
            UpdateText();
        }

        private void UpdateText()
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendLine($"{gameObject.name}");
            _stringBuilder.AppendLine($"{_overlayDialogConfig.Direction} Dir");
            _stringBuilder.AppendLine($"{_maxRewardsToShow} Rewards");
            _stringBuilder.AppendLine(_rewardText.IsNullOrEmpty() ? "Text N" : "Text Y");
            _stringBuilder.Append(_overlayDialogConfig.AutoHide ? "AutoHide Y" : "AutoHide N");
            _text.text = _stringBuilder.ToString();
        }

        private void Awake()
        {
            _button.ReplaceOnClick(ShowBubble);
            UpdateText();
        }

        private void ShowBubble()
        {
            UpdateText();

            var rewardList = new List<string>(InventoryBoosters.AllBoosters);
            var rewards = new Dictionary<string, int>();

            for (var i = 0; i < rewardList.Count; i++)
            {
                if (i >= _maxRewardsToShow)
                    break;

                rewards.TryAdd(rewardList[i], _rewardValue);
            }

            _overlayDialogManager ??= FindAnyObjectByType<OverlayDialogManager>();
            _overlayDialogConfig.DisplayType = DisplayType.RewardSpeechBubble;
            _overlayDialogConfig.RewardToDisplay = rewards;
            _overlayDialogConfig.TextToDisplay = _rewardText;
            _overlayDialogManager?.ToggleOverlayDialog(_overlayDialogConfig);
        }
    }
}