using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI.Extensions
{
    [AddComponentMenu("Layout/Extensions/Circular Layout")]
    public class CircularLayout : LayoutGroup
    {
        public float cellAngle;
        public float radius;
        [Range(0f, 360f)]
        public float MinAngle, MaxAngle, StartAngle;
        protected override void OnEnable() { base.OnEnable(); CalculateRadial(); }
        public override void SetLayoutHorizontal()
        {
        }
        public override void SetLayoutVertical()
        {
        }
        public override void CalculateLayoutInputVertical()
        {
            CalculateRadial();
        }
        public override void CalculateLayoutInputHorizontal()
        {
            CalculateRadial();
        }
#if UNITY_EDITOR
        protected override void OnValidate()
        {
            base.OnValidate();
            CalculateRadial();
        }
#endif
        void CalculateRadial()
        {
            m_Tracker.Clear();
            var count = 0;
            foreach(Transform t in transform)
                if (t.gameObject.activeSelf) count++;
            
            if (count == 0)
                return;

            float fOffsetAngle = ((MaxAngle - MinAngle)) / (count);
            if (cellAngle > 0)
                fOffsetAngle = Mathf.Min(cellAngle, fOffsetAngle);

            float fAngle = StartAngle + (((MaxAngle - MinAngle) - (fOffsetAngle * (count - 1))) / 2f);

            for (int i = 0; i < transform.childCount; i++)
            {
                RectTransform child = (RectTransform)transform.GetChild(i);
                if (child != null && child.gameObject.activeSelf)
                {
                    //Adding the elements to the tracker stops the user from modifiying their positions via the editor.
                    m_Tracker.Add(this, child,
                    DrivenTransformProperties.Anchors |
                    DrivenTransformProperties.AnchoredPosition |
                    DrivenTransformProperties.Pivot);
                    // Vector3 vPos = new Vector3(Mathf.Cos(fAngle * Mathf.Deg2Rad), Mathf.Sin(fAngle * Mathf.Deg2Rad), 0);
                    // Inverted - Starts from Top clockwise
                    Vector3 vPos = new Vector3(Mathf.Sin(fAngle * Mathf.Deg2Rad), Mathf.Cos(fAngle * Mathf.Deg2Rad), 0);
                    child.localPosition = vPos * radius;
                    //Force objects to be center aligned, this can be changed however I'd suggest you keep all of the objects with the same anchor points.
                    child.anchorMin = child.anchorMax = child.pivot = new Vector2(0.5f, 0.5f);
                    fAngle += fOffsetAngle;
                }
            }

        }
    }
}
