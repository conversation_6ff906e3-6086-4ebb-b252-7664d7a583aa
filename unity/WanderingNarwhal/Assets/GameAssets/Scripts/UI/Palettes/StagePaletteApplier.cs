using BBB;
using BBB.GameAssets.Scripts.Player;
using UnityEngine;

public class StagePaletteApplier : BbbMonoBehaviour
{
    [SerializeField] private bool _applyOnEnable = false;
    [SerializeField] private Stage _stageToApplyOnEnable;


    [SerializeField] private StagePalette _goodPalette;
    [SerializeField] private StagePalette _betterPalette;
    [SerializeField] private StagePalette _bestPalette;
    [SerializeField] private StagePalette _completePalette;

    [SerializeField] private StagePalette _defaultPalette;
    [SerializeField] private PaletteApplier[] _paletteAppliers;

    protected override void OnEnable()
    {
        if (_applyOnEnable)
        {
            Apply(_stageToApplyOnEnable);
        }
    }

    public void Apply(Stage stage)
    {
        switch (stage)
        {
            case Stage.Good:
                ApplyPalette(_goodPalette);
                break;
            case Stage.Better:
                ApplyPalette(_betterPalette);
                break;
            case Stage.Best:
                ApplyPalette(_bestPalette);
                break;
            case Stage.Complete:
                ApplyPalette(_completePalette);
                break;
            default:
                break;
        }
    }

    private void ApplyPalette(IPalette palette)
    {
        foreach (var paletteApplier in _paletteAppliers)
        {
            if (paletteApplier != null)
                paletteApplier.Apply(palette);
        }
    }

    public void Reset()
    {
        if (_defaultPalette != null)
            ApplyPalette(_defaultPalette);
    }
}
