using System;
using UnityEngine;
using System.Collections.Generic;
using BBB.DI;
using BBB.UI.Core;
using BBB.Controller;
using BBB.Core;
using BBB.Core.ResourcesManager;
using Cysharp.Threading.Tasks;
using UnityEngine.Profiling;
using UnityEngine.Serialization;

namespace BBB
{
    public class BaseViewsManager : BbbMonoBehaviour, IContextInitializable, IViewsManager
    {
        [FormerlySerializedAs("_curtain")] [SerializeField]
        protected AnimatorShowable Curtain;

        protected readonly Dictionary<Type, ControllerView> ControllerViewMap = new();
        
        protected IResourceCacheHandle ResourceCache;
        protected IAssetsManager AssetsManager;
        private IViewsResources _viewResources;
        private GenericResourceProvider _genericResourceProvider;

        protected Transform RootTransform { get; set; }

        protected virtual void Awake()
        {
            RootTransform = gameObject.transform.root.parent;
        }

        public virtual void InitializeByContext(IContext context)
        {
            AssetsManager = context.Resolve<IAssetsManager>();
            _viewResources = context.Resolve<IViewsResources>();
            ResourceCache = context.Resolve<IResourceCacheHandle>();
            _genericResourceProvider = context.Resolve<GenericResourceProvider>();
        }

        public TController GetOrCreateController<TController, TViewPresenter, TViewType>(One<TViewType> type)
            where TController : class, IController<TViewPresenter>, IController, new()
            where TViewPresenter : class, IViewPresenter
        {

            var ctrlType = typeof(TController);
            var viewType = type.Value.ToInt();
            if (ControllerViewMap.TryGetValue(ctrlType, out var cvm))
            {
                if (cvm.ViewType != viewType)
                {
                    BDebug.LogError(LogCat.CoreViews, $"Controller {ctrlType} is already registered with {viewType} view type. It will be replaced to {ctrlType} view type");
                    cvm.ViewType = viewType;
                }
                return cvm.Controller as TController;
            }

            var ctrl = new TController();
            ControllerViewMap[ctrlType] = new ControllerView(ctrl, viewType);
            return ctrl;
        }

        public async UniTask<GameObject> PreloadGameObject<TController, TViewPresenter, TViewType>(TViewType type, TController ctrl)
            where TController : class, IController<TViewPresenter>, new()
            where TViewPresenter : class, IViewPresenter
            where TViewType : struct, IConvertible
        {
            var prefab = CachePrefab(ctrl, type);

            var instantiatedPrefab = await AssetsManager.LoadAsync<GameObject>(prefab);

            if (instantiatedPrefab != null)
                return InstantiateView(ctrl, instantiatedPrefab.Get());
            
            BDebug.LogErrorFormat(LogCat.CoreViews, "Can't find view for type:{0} name:{1}", type, prefab);
            return null;
        }

        public IViewPresenter TryGetCachedView<TController, TViewPresenter, TViewType>(TViewType type, TController ctrl)
            where TController : class, IController<TViewPresenter>, new()
            where TViewPresenter : class, IViewPresenter
            where TViewType : struct, IConvertible
        {
            var prefabPath = CachePrefab(ctrl, type);

            if (prefabPath == null)
                BDebug.LogError(LogCat.CoreViews, "Can't find prefab name for type:" + type);
            
            ControllerView storedView = null;
            foreach (var viewGo in ControllerViewMap.Values)
            {
                if (viewGo.IsPrefabNameEquals(prefabPath))
                {
                    storedView = viewGo;
                    break;
                }
            }

            if (storedView != null)
            {
                // View is already exist, don't load prefab and return cached value.
                var rootGo = storedView.ViewGameObject;
                var view = rootGo.GetComponent<TViewPresenter>();
                ControllerViewMap[ctrl.GetType()].SetView(rootGo, view);
                return view;
            }

            return null;
        }

        public async UniTask<TViewPresenter> PreloadView<TController, TViewPresenter, TViewType>(TViewType type, TController ctrl)
            where TController : class, IController<TViewPresenter>, new()
            where TViewPresenter : class, IViewPresenter
            where TViewType : struct, IConvertible
        {
            Profiler.BeginSample($"CachePrefab {typeof(TController).FullName}");
            var prefabPath = CachePrefab(ctrl, type);
            Profiler.EndSample();
            
            if (prefabPath == null)
                BDebug.LogError(LogCat.CoreViews, "Can't find prefab name for type:" + type);


            var prefabGo = await AssetsManager.LoadAsync<GameObject>(prefabPath);

            if (prefabGo != null)
                return CacheView<TController, TViewPresenter, TViewType>(prefabGo.Get(), type, ctrl);
            
            BDebug.LogErrorFormat(LogCat.CoreViews, "Can't find view for type:{0} name:{1}", type, prefabPath);
            return null;
        }

        private GameObject InstantiateView<TController>(TController ctrl, GameObject obj)
            where TController : class, IController, new()
        {
            Profiler.BeginSample($"Instantiate View [{obj.name}]");
            var go = Instantiate(obj, RootTransform);
            Profiler.EndSample();
            PostInstantiatedView(ctrl, go);
            go.name = go.name.Replace("(Clone)", string.Empty);
            BDebug.LogFormat(LogCat.Resources, "InstantiateView {0}  {1}", ctrl.GetType().ToString(), go.name);                
            ControllerViewMap[ctrl.GetType()].SetView(go, null);
            return go;
        }

        private string CachePrefab<TController, TViewType>(TController ctrl, TViewType type)
            where TController : class, IController, new()
        {
            if (!typeof(TViewType).IsEnum)
            {
                throw new ArgumentException("TViewType must be an enumerated type");
            }
            
            var prefab = typeof(TViewType) == typeof(ScreenType) ?
            _viewResources.GetPrefabName((ScreenType)type.ToInt())
            : _viewResources.GetPrefabName((ModalsType)type.ToInt());

            if (!ControllerViewMap.ContainsKey(ctrl.GetType()))
            {
                ControllerViewMap[ctrl.GetType()] = new ControllerView(ctrl, type.ToInt());
                BDebug.LogFormat(LogCat.Resources, "CachePrefab add ControllerViewMap {0}  {1}", ctrl.GetType().ToString(), prefab);
            }

            return prefab;
        }

        private TViewPresenter CacheView<TController, TViewPresenter, TViewType>(GameObject prefab, TViewType type, TController ctrl)
            where TController : class, IController<TViewPresenter>, new()
            where TViewPresenter : class, IViewPresenter
        {
            Profiler.BeginSample($"ControllerViewMap.Values.FirstOrDefault");

            GameObject rootGo = null;
            foreach (var value in ControllerViewMap.Values)
            {
                if (!value.IsPrefabNameEquals(prefab.name)) continue;
                
                rootGo = value.ViewGameObject;
                break;
            }

            Profiler.EndSample();

            if (rootGo == null)
            {
                var holder = RootTransform;

                Profiler.BeginSample($"Instantiate[{prefab.name}]");
                rootGo = Instantiate(prefab, holder);
                Profiler.EndSample();
                Profiler.BeginSample($"PostInstantiatedView {typeof(TController).FullName}");
                PostInstantiatedView(ctrl, rootGo);
                Profiler.EndSample();
                rootGo.name = rootGo.name.Replace("(Clone)", string.Empty);
                rootGo.SetActive(false);
            }

            var view = rootGo.GetComponent<TViewPresenter>();
            ControllerViewMap[ctrl.GetType()].SetView(rootGo, view);
            return view;
        }


        protected virtual void PostInstantiatedView(IController ctrl, GameObject rootGo)
        {
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            foreach (var controllerView in ControllerViewMap)
            {
                controllerView.Value?.Destroy();
            }
            ControllerViewMap.Clear();
        }
    }

    public interface IViewGameObject
    {
        GameObject gameObject { get; }
    }

    public interface IViewsManager : ICoroutineExecutor
    {
        UniTask<GameObject> PreloadGameObject<TController, TViewPresenter, TViewType>(TViewType type, TController ctrl)
            where TController : class, IController<TViewPresenter>, new()
            where TViewType : struct, IConvertible
            where TViewPresenter : class, IViewPresenter;

        IViewPresenter TryGetCachedView<TController, TViewPresenter, TViewType>(TViewType type, TController ctrl)
            where TController : class, IController<TViewPresenter>, new()
            where TViewType : struct, IConvertible
            where TViewPresenter : class, IViewPresenter;

        UniTask<TViewPresenter> PreloadView<TController, TViewPresenter, TViewType>(TViewType type, TController ctrl)
            where TController : class, IController<TViewPresenter>, new()
            where TViewType : struct, IConvertible
            where TViewPresenter : class, IViewPresenter;

        TController GetOrCreateController<TController, TViewPresenter, TViewType>(One<TViewType> type)
            where TController : class, IController<TViewPresenter>, IController, new() where TViewPresenter : class, IViewPresenter;
    }
}