using BBB.Controller;
using BBB.Generic.Modal;
using BBB.UI.Core;
using UnityEngine;

namespace BBB
{
    public class ControllerView : IDestroyable
    {
        public IController Controller;
        public int ViewType;
        public IViewPresenter View;
        public GameObject ViewGameObject;

        public ControllerView(IController controller, int type)
        {
            Controller = controller;
            ViewType = type;
        }

        public void SetView(GameObject go, IViewPresenter view)
        {
            ViewGameObject = go;
            View = view;
        }

        public void Destroy()
        {
            Controller.Destroy();
            View.DisposeResources();
            View.DisposeContext();
            View = null;
            ViewGameObject = null;
            Controller = null;
        }
        
        public bool IsReady()
        {
            return ViewGameObject != null;
        }

        public bool IsPrefabNameEquals(string prefabName)
        {
            return ViewGameObject != null && ViewGameObject.gameObject != null && ViewGameObject.gameObject.name.Equals(prefabName);
        }
    }
}