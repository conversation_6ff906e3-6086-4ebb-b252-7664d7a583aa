using System;
using BBB.Audio;
using BBB.Core;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.MMVibrations;
using BBB.MMVibrations.Plugins;
using BebopBee.Core.Audio;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace BBB.UI.Core
{
    public abstract class ViewPresenter : ContextedUiBehaviour, IViewPresenter, IViewGameObject
    {
        [FormerlySerializedAs("_closeButton")]
        [SerializeField]
        protected Button CloseButton;

        protected ILocalizationManager Localization;
        protected GenericResourceProvider GenericResourceProvider;
        protected RectTransform RectTransform { get; private set; }
        public event Action OnCloseClicked = delegate { };
        public event Action OnCloseButtonEvent = delegate { };
        private IVibrationsWrapper _vibrations;
        

        public virtual void Init(IContext previousContext)
        {
            Localization = previousContext.Resolve<ILocalizationManager>();
            GenericResourceProvider = previousContext.Resolve<GenericResourceProvider>();
            OnContextInitialized(previousContext);
        }

        /// <summary>
        /// Additional filter is used by the ThemeManager in order to add an extra layer of filtering (in addition
        /// to the Placement Id). Child classes should override this method if they need to implement this functionality
        ///
        /// An example of usage is the SpecialPackModal, which uses the Uid from the PackModalVisualOverrideConfig,
        /// since there may be Special Packs running that do not need to be themed
        /// </summary>
        protected virtual string AdditionalFilter()
        {
            return "";
        }
        
        public virtual void CacheResources(IResourceCache cache)
        {
        }

        public virtual void DisposeResources()
        {
        }

        protected override void Awake()
        {
            base.Awake();
            RectTransform = transform as RectTransform;
            if (CloseButton != null)
                CloseButton.onClick.AddListener(OnCloseButton);
        }

        public virtual void Show()
        {
            try
            {
                gameObject.SetActive(true);
                SetTransparency(1f);
                OnShow();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                Debug.LogError($"Exception with message '{e.Message}' while show asset {name}");
                throw;
            }

        }

        protected virtual void OnShow()
        {
        }

        public virtual void Hide()
        {
            if (this) //don't rebind if it's going to be destroyed anyway
            {
                var animator = GetComponent<Animator>();
                if (animator)
                {
                    animator.Rebind();
                }

                gameObject.SetActive(false);
            }

            OnHide();
        }

        protected virtual void OnHide()
        {
        }

        public virtual void Refresh()
        {
        }

        public void SetGoActive(bool active)
        {
            gameObject.SetActive(active);
        }

        public virtual void SetTransparency(float transparency)
        {
            
        }

        public virtual void DisposeContext()
        {

        }

        public bool IsVisible()
        {
            if (this == null)
                return false;
            
            return gameObject.activeInHierarchy;
        }

        protected virtual void OnTransitionStarted()
        {
        }

        protected virtual void OnTransitionCompleted()
        {
        }

        protected virtual void OnCloseButton()
        {
            OnCloseButtonEvent();
            BDebug.Log(LogCat.Vibration, $"Playing haptic feedback for button {gameObject.name} -- Vibrations available={_vibrations != null} -- ImpactType = {ImpactPreset.LightImpact} from ViewPresenter");
            _vibrations?.PlayHaptic(ImpactPreset.LightImpact);
        }

        public void HideFromCloseButton()
        {
            OnCloseButtonClick();
        }

        protected virtual void OnCloseButtonClick()
        {
            Hide();
            TriggerOnCloseClicked();
            PlayCloseFX();
        }

        protected virtual void OnContextInitialized(IContext context)
        {
        }

        protected override void InitWithContextInternal(IContext context)
        {
            _vibrations = context.Resolve<IVibrationsWrapper>();
        }

        protected virtual void OnContextInitialization(UnityContext context)
        {
        }

        protected void TriggerOnCloseClicked()
        {
            OnCloseClicked();
        }

        protected void PlayCloseFX()
        {
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
        }
    }
}

