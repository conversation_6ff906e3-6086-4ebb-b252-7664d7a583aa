using System;
using UnityEngine;
using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core;
using BebopBee.Core.Audio;
using DG.Tweening;
using BBB.Core;
using Cysharp.Threading.Tasks;
using UnityEngine.Serialization;

namespace BBB.Wallet
{
    [System.Serializable]
    public class CurrencyFlyingAnimationSettings
    {
        [Tooltip("Steps count is now automatic and serialized preset will be ignored.")]
        public int StepsCount = 4;

        public float MinStartDelay = 0f;
        public float MaxStartDelay = 0.1f;
        public float DelayBetweenSteps = 0.3f;
        public float MinOffset = 0.02f;
        public float MaxOffset = 0.2f;
        public float FlightDuration = 0.9f;
        public Ease EaseType = Ease.InOutSine;
        public bool PreInstantiate = false;
        public string SoundFlyStart = GenericSoundIds.CurrencyFlyStart;
        public string SoundFlyEnd = GenericSoundIds.CurrencyFlyEnd;
        public bool FlippingAnimation = true;
        public Vector2 AnticipationMultipliers;

        public CurrencyFlyingAnimationSettings Clone()
        {
            return (CurrencyFlyingAnimationSettings) MemberwiseClone();
        }

        public CurrencyFlyingAnimationSettings CloneInto(CurrencyFlyingAnimationSettings target)
        {
            if (target == null)
            {
                target = this.Clone();
            }
            else
            {
                target.StepsCount = StepsCount;
                target.MinStartDelay = MinStartDelay;
                target.MaxStartDelay = MaxStartDelay;
                target.DelayBetweenSteps = DelayBetweenSteps;
                target.MinOffset = MinOffset;
                target.MaxOffset = MaxOffset;
                target.FlightDuration = FlightDuration;
                target.EaseType = EaseType;
                target.PreInstantiate = PreInstantiate;
                target.SoundFlyStart = SoundFlyStart;
                target.SoundFlyEnd = SoundFlyEnd;
                target.FlippingAnimation = FlippingAnimation;
            }

            return target;
        }
    }

    public class DroppableAnimator : BbbMonoBehaviour
    {
        [SerializeField] private int _minAdjustableCount = 1;
        [SerializeField] private int _maxAdjustableCount = 12;
        [SerializeField] private float _extraFlyTimePerDroppable = 0.05f;

        // TODO: remove FormerlySerializedAs attribute after pull request is merged. -VK
        [SerializeField] [Tooltip("Coins fly should start immediately with small or no delay between steps.")] [FormerlySerializedAs("_plusOneSettings")]
        private CurrencyFlyingAnimationSettings _animationPresetImmediateWithoutSpread;

        [SerializeField] private Transform _centerPosition;
        [SerializeField] private GameObject _baseCurrencyPrefab;
        private int _prepOffsetMult = 1;


        public void Init()
        {
            _baseCurrencyPrefab.SetActive(false);
        }
    }
}