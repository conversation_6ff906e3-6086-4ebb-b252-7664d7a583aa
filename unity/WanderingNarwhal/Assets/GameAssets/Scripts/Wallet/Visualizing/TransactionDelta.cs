namespace BBB.Core.Wallet
{
    public class TransactionDelta
    {
        public string Tag;
        public bool IsHidden;
        public bool IsShowFloatingIcons;
        public bool IsShowFlyingIcons;
        public bool IsShowWalletBarIncrement;
        public bool IsBlockingModalsDisplayDuringFlyAnimation;
        public string GameEventUid;
        public CurrencyDictionary CurrenciesDelta;

        public override string ToString()
        {
            return $"Tag= {Tag} ; Currencies = {CurrenciesDelta}";
        }

        public TransactionDelta()
        {
            CurrenciesDelta = new CurrencyDictionary();
        }

        public TransactionDelta(string tag)
        {
            Tag = tag;
            CurrenciesDelta = new CurrencyDictionary();
        }

        public void AddOther(TransactionDelta other)
        {
            Tag = other.Tag;
            IsHidden = other.IsHidden;
            IsShowFloatingIcons |= other.IsShowFloatingIcons;
            IsShowFlyingIcons |= other.IsShowFlyingIcons;
            IsShowWalletBarIncrement |= other.IsShowWalletBarIncrement;
            IsBlockingModalsDisplayDuringFlyAnimation |= other.IsBlockingModalsDisplayDuringFlyAnimation;
            if (!other.GameEventUid.IsNullOrEmpty())
            {
                GameEventUid = other.GameEventUid;
            }

            if (other.CurrenciesDelta == null) return;

            if (CurrenciesDelta == null)
            {
                CurrenciesDelta = new CurrencyDictionary();
            }

            foreach (var item in other.CurrenciesDelta)
            {
                if (CurrenciesDelta.ContainsKey(item.Key))
                {
                    CurrenciesDelta[item.Key] += other.CurrenciesDelta[item.Key];
                }
                else
                {
                    CurrenciesDelta[item.Key] = other.CurrenciesDelta[item.Key];
                }
            }

            if (IsBlockingModalsDisplayDuringFlyAnimation)
            {
                IsBlockingModalsDisplayDuringFlyAnimation = false;
                foreach (var currencyUid in CurrenciesDelta.Keys)
                {
                    if (CurrenciesDelta[currencyUid] > 0)
                    {
                        IsBlockingModalsDisplayDuringFlyAnimation = true;
                        break;
                    }
                }
            }
        }
    }
}