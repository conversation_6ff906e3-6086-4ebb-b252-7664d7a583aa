using System.Collections;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Wallet
{
    public class CurrenciesAmountFloatingTexts : BbbMonoBehaviour
    {
        [SerializeField] private GameObject _itemTemplate;
        [SerializeField] private float _floatingAnimationDuration = 3f;
        [Tooltip("Delay for flying coins animation, when Floating effect is active.")]
        [SerializeField] private float _flyAnimationDelay = 2.8f;
        [SerializeField] private int _itemsPerLine = 3;
        [SerializeField] private float _itemsDistanceX = 100;
        [SerializeField] private float _itemsDistanceY = 100;
        [SerializeField] private AnimationCurve _scaleCurve = AnimationCurve.Linear(0, 1, 1, 1);
        [SerializeField] private AnimationCurve _positionYCurve = AnimationCurve.Linear(0, 0, 1, 0);

        private CurrencyIconsLoader _iconsLoader;
        private ICurrenciesCountProvider _currenciesCountProvider;

        private readonly List<GameObject> _itemsInstances = new(3);
        private int _activeItemsCount;

        private readonly List<string> _currencies = new();
        private readonly List<string> _amountTexts = new();

        private Canvas _canvas;

        public float FlyingCoinsAnimationDelay => _flyAnimationDelay;

        public void Init(CurrencyIconsLoader iconsLoader, ICurrenciesCountProvider currenciesCountProvider)
        {
            _itemTemplate.SetActive(false);
            _iconsLoader = iconsLoader;
            _currenciesCountProvider = currenciesCountProvider;
            _canvas = GetComponent<Canvas>();
        }

        public void AddCurrencyDisplay(string currencyUid, int amount)
        {
            _activeItemsCount++;
            _currencies.Add(currencyUid);

            var amountText = _currenciesCountProvider.GetLocalizedCount(currencyUid, amount);
            _amountTexts.Add(amountText);
        }

        public void StartFloatingAnimation()
        {
            UpdateLayout(_activeItemsCount);

            for (var i = 0; i < _activeItemsCount; i++)
            {
                _itemsInstances[i].SetActive(true);
                var images = _itemsInstances[i].GetComponentsInChildren<Image>();
                _iconsLoader.LoadAndGetCurrencySpriteAsync(_currencies[i]).ContinueWith(sprite =>  images[^1].sprite = sprite);

                var text = _itemsInstances[i].GetComponentInChildren<TextMeshProUGUI>();
                text.text = _amountTexts[i];
                text.enabled = _currenciesCountProvider.ShouldDisplayCount(_currencies[i]);
            }

            for (var i = _activeItemsCount; i < _itemsInstances.Count; i++)
            {
                _itemsInstances[i].SetActive(false);
            }

            _activeItemsCount = 0;
            _currencies.Clear();
            _amountTexts.Clear();

            StartCoroutine(AnimationRoutine());
        }

        private void UpdateLayout(int count)
        {
            while (_itemsInstances.Count < count)
            {
                var item = Instantiate(_itemTemplate, parent: _itemTemplate.transform.parent);
                _itemsInstances.Add(item);
            }

            var lines = Mathf.CeilToInt(count / (float)Mathf.Max(1, _itemsPerLine));

            var startPointY = (lines - 1) * _itemsDistanceY * 0.5f;

            for (var i = 0; i < count; i++)
            {
                var indexOfLine = i / _itemsPerLine;
                var indexInLine = i % _itemsPerLine;

                var isLastLineContainsOnlyOneItem = (count % _itemsPerLine) == 1;
                var isCanShuffleItemToLastLine = _itemsPerLine > 1 && lines > 1 && isLastLineContainsOnlyOneItem;
                var isLastLine = indexOfLine == lines - 1;
                var isBeforeLastLine = indexOfLine == lines - 2;
                if (isCanShuffleItemToLastLine)
                {
                    var isLastItemInCurrentLine = indexInLine == _itemsPerLine - 1;
                    if (isBeforeLastLine && isLastItemInCurrentLine)
                    {
                        indexOfLine++;
                        indexInLine = 0;
                    }
                    else
                    {
                        if (isLastLine)
                        {
                            indexInLine++;
                        }
                    }
                }

                var maxPossibleItemsInCurrentLine = _itemsPerLine - (isCanShuffleItemToLastLine && isBeforeLastLine ? 1 : 0);
                var actualItemsInThisLine = Mathf.Min(maxPossibleItemsInCurrentLine, (count - i) + indexInLine);
                var startPointX = -Mathf.Max(actualItemsInThisLine - 1, 0) * _itemsDistanceX * 0.5f;
                var posX = startPointX + indexInLine * _itemsDistanceX;
                var posY = startPointY - indexOfLine * _itemsDistanceY;

                _itemsInstances[i].GetComponent<RectTransform>().anchoredPosition = new Vector3(posX, posY);
            }
        }

        private IEnumerator AnimationRoutine()
        {
            if (_canvas == null)
            {
                _canvas = GetComponent<Canvas>();
            }

            // Refresh canvas to fix occasional bug with few seconds of missing rendered icons when animation starts.
            _canvas.enabled = false;
            _canvas.enabled = true;

            transform.localPosition = new Vector3();
            transform.localScale = new Vector3(1, 1, 1);
            var timer = 0f;
            while (timer < _floatingAnimationDuration)
            {
                timer += Time.deltaTime;

                var ratio = timer / _floatingAnimationDuration;
                var scale = _scaleCurve.Evaluate(ratio);
                var posY = _positionYCurve.Evaluate(ratio);

                transform.localScale = new Vector3(scale, scale, scale);
                var localPos = transform.localPosition;
                localPos.y = posY;
                transform.localPosition = localPos;

                yield return null;
            }

            foreach (var go in _itemsInstances)
            {
                go.SetActive(false);
            }
        }

        [SerializeField] private int _debugLayout = 3;
        [ContextMenu("Debug set layout")]
        private void DebugSetLayut()
        {
            for (var i = 0; i < _debugLayout; i++)
            {
                AddCurrencyDisplay("regular", 15);
            }

            StartFloatingAnimation();
        }

        protected override void OnDestroy()
        {
            foreach (var itemInstance in _itemsInstances)
            {
                itemInstance.GetComponentsInChildren<Image>()[^1].sprite = null;
            }
            base.OnDestroy();
        }
    }
}
