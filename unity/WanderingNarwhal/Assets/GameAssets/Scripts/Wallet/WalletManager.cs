using System.Collections.Generic;
using BBB.Core.Wallet;

namespace BBB.Wallet
{
    public class WalletManager : IWalletManager
    {
        private readonly List<IWalletTransactionObserver> _observerList = new();
        private IWalletsAggregator _walletsAggregator;
        private IWalletPredefiner _walletPredefiner;
        private AnalyticsWalletMonitor _analyticsWalletMonitor;
        
        public IWalletBalance Balance { get; private set; }
        public IWalletTransactionController TransactionController { get; private set; }
        public IWalletTypesInspector TypesInspector { get; private set; }
        public ITransactionsDeltaPool TransactionsDeltaPool { get; private set; }
        public IPlayerManager PlayerManager { get; private set; }

        /// <summary>
        /// USE FOR DEBUG PURPOSES ONLY
        /// </summary>
        public Balance DebugResourceWalletBalance
        {
            get { return _walletsAggregator.GetWalletByType(WalletType.ResourcesWallet).Balance; }
        }

        public void Init(IPlayerManager playerManager, IConfig config,
            AnalyticsPropertiesMonitor analyticsMonitor, INotificationManager notificationManager)
        {
            PlayerManager = playerManager;
            _walletPredefiner = new WalletPredefiner(playerManager.Player.Wallet);
            _walletsAggregator = new WalletsAggregator(_walletPredefiner);

            TypesInspector = new WalletTypesInspector(_walletsAggregator);
            Balance = new WalletBalance(_walletsAggregator);

            UnsubscribeToTransactions(_analyticsWalletMonitor);
            _analyticsWalletMonitor = new AnalyticsWalletMonitor();
            SubscribeToTransactions(_analyticsWalletMonitor);

            TransactionController = new WalletTransactionController(_walletsAggregator, this, playerManager);

            UnsubscribeToTransactions(TransactionsDeltaPool);
            TransactionsDeltaPool = new TransactionsDeltaPool();
            SubscribeToTransactions(TransactionsDeltaPool);
            
            UnsubscribeToTransactions(PlayerManager);
            SubscribeToTransactions(PlayerManager);

            UnsubscribeToTransactions(analyticsMonitor);
            analyticsMonitor.Init(this);
            SubscribeToTransactions(analyticsMonitor);
        }
        
        public void SubscribeToTransactions(IWalletTransactionObserver observer)
        {
            if (!_observerList.Contains(observer))
            {
                _observerList.Add(observer);
            }
        }

        public void UnsubscribeToTransactions(IWalletTransactionObserver observer)
        {
            if (_observerList.Contains(observer))
            {
                _observerList.Remove(observer);
            }
        }

        public List<IWalletTransactionObserver> GetTransactionObservers()
        {
            return _observerList;
        }

        public void Restart()
        {
            _observerList.Clear();
        }
    }
}